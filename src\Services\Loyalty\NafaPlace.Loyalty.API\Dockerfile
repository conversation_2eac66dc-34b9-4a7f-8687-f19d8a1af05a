FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 80

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy csproj files and restore dependencies
COPY ["src/Services/Loyalty/NafaPlace.Loyalty.API/NafaPlace.Loyalty.API.csproj", "src/Services/Loyalty/NafaPlace.Loyalty.API/"]
COPY ["src/Services/Loyalty/NafaPlace.Loyalty.Application/NafaPlace.Loyalty.Application.csproj", "src/Services/Loyalty/NafaPlace.Loyalty.Application/"]
COPY ["src/Services/Loyalty/NafaPlace.Loyalty.Domain/NafaPlace.Loyalty.Domain.csproj", "src/Services/Loyalty/NafaPlace.Loyalty.Domain/"]
COPY ["src/Services/Loyalty/NafaPlace.Loyalty.Infrastructure/NafaPlace.Loyalty.Infrastructure.csproj", "src/Services/Loyalty/NafaPlace.Loyalty.Infrastructure/"]
COPY ["src/BuildingBlocks/Common/NafaPlace.Common/NafaPlace.Common.csproj", "src/BuildingBlocks/Common/NafaPlace.Common/"]

RUN dotnet restore "src/Services/Loyalty/NafaPlace.Loyalty.API/NafaPlace.Loyalty.API.csproj"

# Copy source code
COPY . .
WORKDIR "/src/src/Services/Loyalty/NafaPlace.Loyalty.API"
RUN dotnet build "NafaPlace.Loyalty.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "NafaPlace.Loyalty.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "NafaPlace.Loyalty.API.dll"]
