@using Microsoft.JSInterop
@using NafaPlace.Web.Models.Common
@inject IJSRuntime JSRuntime
@inject HttpClient HttpClient

<div class="advanced-search-container">
    <!-- Main Search Bar -->
    <div class="main-search-bar">
        <div class="search-input-wrapper">
            <i class="fas fa-search search-icon"></i>
            <input @bind="SearchQuery"
                   @bind:event="oninput"
                   @onkeyup="OnSearchInput"
                   @onfocus="OnSearchFocus"
                   @onblur="OnSearchBlur"
                   class="search-input"
                   placeholder="Rechercher des produits, marques, catégories..."
                   autocomplete="off" />

            @if (!string.IsNullOrEmpty(SearchQuery))
            {
                <button class="clear-search-btn" @onclick="ClearSearch">
                    <i class="fas fa-times"></i>
                </button>
            }

            <!-- Voice Search Button -->
            <button class="voice-search-btn" @onclick="StartVoiceSearch" title="Recherche vocale">
                <i class="fas fa-microphone @(IsListening ? "listening" : "")"></i>
            </button>

            <!-- Camera Search Button -->
            <button class="camera-search-btn" @onclick="StartCameraSearch" title="Recherche par image">
                <i class="fas fa-camera"></i>
            </button>
        </div>

        <!-- Search Suggestions Dropdown -->
        @if (ShowSuggestions && (Suggestions.Any() || RecentSearches.Any()))
        {
            <div class="search-suggestions">
                @if (Suggestions.Any())
                {
                    <div class="suggestions-section">
                        <div class="suggestions-header">
                            <i class="fas fa-search me-2"></i>
                            Suggestions
                        </div>
                        @foreach (var suggestion in Suggestions.Take(8))
                        {
                            <div class="suggestion-item" @onclick="() => SelectSuggestion(suggestion)">
                                <i class="fas fa-search suggestion-icon"></i>
                                <span class="suggestion-text">@HighlightMatch(suggestion, SearchQuery)</span>
                                <i class="fas fa-arrow-up-right-from-square suggestion-action"></i>
                            </div>
                        }
                    </div>
                }

                @if (ProductSuggestions.Any())
                {
                    <div class="suggestions-section">
                        <div class="suggestions-header">
                            <i class="fas fa-box me-2"></i>
                            Produits
                        </div>
                        @foreach (var product in ProductSuggestions.Take(5))
                        {
                            <div class="product-suggestion" @onclick="() => SelectProduct(product)">
                                <img src="@product.ImageUrl" alt="@product.Name" class="product-image" />
                                <div class="product-info">
                                    <div class="product-name">@product.Name</div>
                                    <div class="product-price">@product.Price.ToString("N0") GNF</div>
                                </div>
                            </div>
                        }
                    </div>
                }

                @if (CategorySuggestions.Any())
                {
                    <div class="suggestions-section">
                        <div class="suggestions-header">
                            <i class="fas fa-tags me-2"></i>
                            Catégories
                        </div>
                        @foreach (var category in CategorySuggestions.Take(4))
                        {
                            <div class="category-suggestion" @onclick="() => SelectCategory(category)">
                                <i class="fas fa-folder category-icon"></i>
                                <span class="category-name">@category.Name</span>
                                <span class="category-count">(@category.ProductCount)</span>
                            </div>
                        }
                    </div>
                }

                @if (RecentSearches.Any() && string.IsNullOrEmpty(SearchQuery))
                {
                    <div class="suggestions-section">
                        <div class="suggestions-header">
                            <i class="fas fa-history me-2"></i>
                            Recherches récentes
                            <button class="clear-history-btn" @onclick="ClearSearchHistory">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        @foreach (var recent in RecentSearches.Take(5))
                        {
                            <div class="suggestion-item" @onclick="() => SelectSuggestion(recent)">
                                <i class="fas fa-history suggestion-icon"></i>
                                <span class="suggestion-text">@recent</span>
                                <button class="remove-recent-btn" @onclick="(e) => RemoveRecentSearch(e, recent)">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        }
                    </div>
                }

                @if (TrendingSearches.Any() && string.IsNullOrEmpty(SearchQuery))
                {
                    <div class="suggestions-section">
                        <div class="suggestions-header">
                            <i class="fas fa-fire me-2"></i>
                            Tendances
                        </div>
                        @foreach (var trending in TrendingSearches.Take(4))
                        {
                            <div class="trending-item" @onclick="() => SelectSuggestion(trending.Query)">
                                <i class="fas fa-trending-up trending-icon"></i>
                                <span class="trending-text">@trending.Query</span>
                                <span class="trending-badge">+@trending.GrowthPercentage%</span>
                            </div>
                        }
                    </div>
                }
            </div>
        }
    </div>

    <!-- Advanced Filters (Collapsible) -->
    <div class="advanced-filters @(ShowAdvancedFilters ? "expanded" : "collapsed")">
        <div class="filters-header" @onclick="ToggleAdvancedFilters">
            <span>Filtres avancés</span>
            <i class="fas fa-chevron-down toggle-icon @(ShowAdvancedFilters ? "rotated" : "")"></i>
        </div>

        @if (ShowAdvancedFilters)
        {
            <div class="filters-content">
                <div class="row">
                    <!-- Price Range -->
                    <div class="col-md-3">
                        <div class="filter-group">
                            <label class="filter-label">Prix (GNF)</label>
                            <div class="price-range">
                                <input type="number" @bind="MinPrice" class="form-control" placeholder="Min" />
                                <span class="range-separator">-</span>
                                <input type="number" @bind="MaxPrice" class="form-control" placeholder="Max" />
                            </div>
                        </div>
                    </div>

                    <!-- Category -->
                    <div class="col-md-3">
                        <div class="filter-group">
                            <label class="filter-label">Catégorie</label>
                            <select @bind="SelectedCategoryId" class="form-select">
                                <option value="">Toutes les catégories</option>
                                @foreach (var category in Categories)
                                {
                                    <option value="@category.Id">@category.Name</option>
                                }
                            </select>
                        </div>
                    </div>

                    <!-- Brand -->
                    <div class="col-md-3">
                        <div class="filter-group">
                            <label class="filter-label">Marque</label>
                            <select @bind="SelectedBrand" class="form-select">
                                <option value="">Toutes les marques</option>
                                @foreach (var brand in Brands)
                                {
                                    <option value="@brand">@brand</option>
                                }
                            </select>
                        </div>
                    </div>

                    <!-- Rating -->
                    <div class="col-md-3">
                        <div class="filter-group">
                            <label class="filter-label">Note minimum</label>
                            <div class="rating-filter">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    var stars = i;
                                    <div class="rating-option" @onclick="() => SelectMinRating(stars)">
                                        <input type="radio" name="rating" checked="@(MinRating == stars)" />
                                        <div class="stars">
                                            @for (int j = 1; j <= 5; j++)
                                            {
                                                <i class="fas fa-star @(j <= stars ? "filled" : "")"></i>
                                            }
                                        </div>
                                        <span>& plus</span>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Filters -->
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="additional-filters">
                            <div class="filter-chips">
                                <label class="filter-chip">
                                    <input type="checkbox" @bind="InStock" />
                                    <span class="chip-text">En stock uniquement</span>
                                </label>
                                <label class="filter-chip">
                                    <input type="checkbox" @bind="FreeShipping" />
                                    <span class="chip-text">Livraison gratuite</span>
                                </label>
                                <label class="filter-chip">
                                    <input type="checkbox" @bind="OnSale" />
                                    <span class="chip-text">En promotion</span>
                                </label>
                                <label class="filter-chip">
                                    <input type="checkbox" @bind="NewProducts" />
                                    <span class="chip-text">Nouveautés</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="filters-actions mt-3">
                    <button class="btn btn-primary" @onclick="ApplyFilters">
                        <i class="fas fa-search me-2"></i>
                        Rechercher
                    </button>
                    <button class="btn btn-outline-secondary" @onclick="ClearFilters">
                        <i class="fas fa-times me-2"></i>
                        Effacer les filtres
                    </button>
                </div>
            </div>
        }
    </div>
</div>

<!-- Voice Search Modal -->
@if (ShowVoiceModal)
{
    <div class="voice-search-modal">
        <div class="voice-modal-content">
            <div class="voice-animation">
                <div class="voice-circle @(IsListening ? "listening" : "")"></div>
                <i class="fas fa-microphone voice-icon"></i>
            </div>
            <h5>@(IsListening ? "Écoute en cours..." : "Appuyez pour parler")</h5>
            <p class="text-muted">Dites ce que vous recherchez</p>
            <div class="voice-actions">
                <button class="btn btn-outline-secondary" @onclick="StopVoiceSearch">Annuler</button>
            </div>
        </div>
    </div>
}

<!-- Camera Search Modal -->
@if (ShowCameraModal)
{
    <div class="camera-search-modal">
        <div class="camera-modal-content">
            <div class="camera-header">
                <h5>Recherche par image</h5>
                <button class="btn-close" @onclick="CloseCameraSearch"></button>
            </div>
            <div class="camera-body">
                <video id="cameraPreview" autoplay playsinline style="display: none;"></video>
                <canvas id="cameraCanvas" style="display: none;"></canvas>
                <div class="camera-controls">
                    <button class="btn btn-primary" @onclick="TakePicture">
                        <i class="fas fa-camera me-2"></i>
                        Prendre une photo
                    </button>
                    <input type="file" id="imageUpload" accept="image/*" @onchange="HandleImageUpload" style="display: none;" />
                    <button class="btn btn-outline-primary" @onclick="SelectImage">
                        <i class="fas fa-upload me-2"></i>
                        Choisir une image
                    </button>
                </div>
            </div>
        </div>
    </div>
}

<style>
    .advanced-search-container {
        position: relative;
        width: 100%;
        max-width: 800px;
        margin: 0 auto;
    }

    .main-search-bar {
        position: relative;
    }

    .search-input-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        background: white;
        border: 2px solid #e0e0e0;
        border-radius: 50px;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .search-input-wrapper:focus-within {
        border-color: #E73C30;
        box-shadow: 0 4px 20px rgba(231, 60, 48, 0.2);
    }

    .search-icon {
        position: absolute;
        left: 20px;
        color: #999;
        z-index: 2;
    }

    .search-input {
        width: 100%;
        padding: 15px 150px 15px 50px;
        border: none;
        outline: none;
        font-size: 16px;
        background: transparent;
    }

    .clear-search-btn,
    .voice-search-btn,
    .camera-search-btn {
        position: absolute;
        right: 10px;
        background: none;
        border: none;
        padding: 8px;
        border-radius: 50%;
        color: #666;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .voice-search-btn {
        right: 50px;
    }

    .camera-search-btn {
        right: 90px;
    }

    .clear-search-btn:hover,
    .voice-search-btn:hover,
    .camera-search-btn:hover {
        background: #f0f0f0;
        color: #E73C30;
    }

    .voice-search-btn .fa-microphone.listening {
        color: #dc3545;
        animation: pulse 1s infinite;
    }

    @@keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    .search-suggestions {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 15px;
        box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        z-index: 1004 !important;
        max-height: 500px;
        overflow-y: auto;
        margin-top: 5px;
    }

    .suggestions-section {
        padding: 10px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .suggestions-section:last-child {
        border-bottom: none;
    }

    .suggestions-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 20px;
        font-weight: 600;
        color: #666;
        font-size: 12px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .suggestion-item,
    .product-suggestion,
    .category-suggestion,
    .trending-item {
        display: flex;
        align-items: center;
        padding: 12px 20px;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    .suggestion-item:hover,
    .product-suggestion:hover,
    .category-suggestion:hover,
    .trending-item:hover {
        background-color: #f8f9fa;
    }

    .suggestion-icon,
    .category-icon,
    .trending-icon {
        margin-right: 12px;
        color: #999;
        width: 16px;
    }

    .suggestion-text,
    .category-name,
    .trending-text {
        flex: 1;
        font-size: 14px;
    }

    .suggestion-action,
    .remove-recent-btn {
        color: #ccc;
        padding: 4px;
        border: none;
        background: none;
        cursor: pointer;
    }

    .remove-recent-btn:hover {
        color: #dc3545;
    }

    .product-image {
        width: 40px;
        height: 40px;
        object-fit: cover;
        border-radius: 6px;
        margin-right: 12px;
    }

    .product-info {
        flex: 1;
    }

    .product-name {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 2px;
    }

    .product-price {
        font-size: 12px;
        color: #E73C30;
        font-weight: 600;
    }

    .category-count {
        color: #999;
        font-size: 12px;
        margin-left: 5px;
    }

    .trending-badge {
        background: #28a745;
        color: white;
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 10px;
        font-weight: 600;
    }

    .clear-history-btn {
        background: none;
        border: none;
        color: #dc3545;
        padding: 2px 6px;
        border-radius: 4px;
        cursor: pointer;
    }

    .clear-history-btn:hover {
        background: #f8d7da;
    }

    .advanced-filters {
        margin-top: 15px;
        background: white;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .filters-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px 20px;
        cursor: pointer;
        background: #f8f9fa;
        font-weight: 600;
        color: #333;
    }

    .toggle-icon {
        transition: transform 0.3s ease;
    }

    .toggle-icon.rotated {
        transform: rotate(180deg);
    }

    .advanced-filters.collapsed .filters-content {
        display: none;
    }

    .filters-content {
        padding: 20px;
    }

    .filter-group {
        margin-bottom: 15px;
    }

    .filter-label {
        display: block;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
        font-size: 14px;
    }

    .price-range {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .range-separator {
        color: #999;
        font-weight: 600;
    }

    .rating-filter {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .rating-option {
        display: flex;
        align-items: center;
        gap: 10px;
        cursor: pointer;
        padding: 5px;
        border-radius: 6px;
        transition: background-color 0.2s ease;
    }

    .rating-option:hover {
        background-color: #f8f9fa;
    }

    .stars {
        display: flex;
        gap: 2px;
    }

    .stars .fa-star {
        color: #ddd;
        font-size: 14px;
    }

    .stars .fa-star.filled {
        color: #ffc107;
    }

    .additional-filters {
        padding: 15px 0;
        border-top: 1px solid #f0f0f0;
    }

    .filter-chips {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }

    .filter-chip {
        display: flex;
        align-items: center;
        padding: 8px 15px;
        background: #f8f9fa;
        border: 1px solid #e0e0e0;
        border-radius: 25px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 14px;
    }

    .filter-chip:hover {
        background: #e9ecef;
    }

    .filter-chip input[type="checkbox"] {
        display: none;
    }

    .filter-chip input[type="checkbox"]:checked + .chip-text {
        color: #E73C30;
        font-weight: 600;
    }

    .filter-chip:has(input[type="checkbox"]:checked) {
        background: #fff5f5;
        border-color: #E73C30;
    }

    .filters-actions {
        display: flex;
        gap: 10px;
        padding-top: 15px;
        border-top: 1px solid #f0f0f0;
    }

    /* Voice Search Modal */
    .voice-search-modal,
    .camera-search-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2000;
    }

    .voice-modal-content,
    .camera-modal-content {
        background: white;
        padding: 30px;
        border-radius: 20px;
        text-align: center;
        box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        max-width: 400px;
        width: 90%;
    }

    .voice-animation {
        position: relative;
        margin: 20px auto;
        width: 80px;
        height: 80px;
    }

    .voice-circle {
        position: absolute;
        top: 0;
        left: 0;
        width: 80px;
        height: 80px;
        border: 3px solid #E73C30;
        border-radius: 50%;
        opacity: 0.3;
        transition: all 0.3s ease;
    }

    .voice-circle.listening {
        animation: voicePulse 1.5s infinite;
    }

    @@keyframes voicePulse {
        0% { transform: scale(1); opacity: 0.3; }
        50% { transform: scale(1.2); opacity: 0.1; }
        100% { transform: scale(1); opacity: 0.3; }
    }

    .voice-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 30px;
        color: #E73C30;
    }

    .voice-actions,
    .camera-controls {
        margin-top: 20px;
        display: flex;
        gap: 10px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .camera-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .camera-body {
        text-align: center;
    }

    #cameraPreview,
    #cameraCanvas {
        max-width: 100%;
        max-height: 300px;
        border-radius: 10px;
        margin-bottom: 15px;
    }

    @@media (max-width: 768px) {
        .search-input {
            padding: 12px 120px 12px 45px;
            font-size: 14px;
        }

        .voice-search-btn {
            right: 45px;
        }

        .camera-search-btn {
            right: 80px;
        }

        .filters-content .row {
            flex-direction: column;
        }

        .filter-chips {
            justify-content: center;
        }

        .filters-actions {
            flex-direction: column;
        }
    }

    .highlight {
        background-color: #ffeb3b;
        font-weight: 600;
    }
</style>

@code {
    [Parameter] public EventCallback<SearchParams> OnSearch { get; set; }

    private string SearchQuery = "";
    private bool ShowSuggestions = false;
    private bool ShowAdvancedFilters = false;
    private bool ShowVoiceModal = false;
    private bool ShowCameraModal = false;
    private bool IsListening = false;

    // Filter Parameters
    private decimal? MinPrice;
    private decimal? MaxPrice;
    private string SelectedCategoryId = "";
    private string SelectedBrand = "";
    private int MinRating = 0;
    private bool InStock = false;
    private bool FreeShipping = false;
    private bool OnSale = false;
    private bool NewProducts = false;

    // Suggestions Data
    private List<string> Suggestions = new();
    private List<ProductSuggestionDto> ProductSuggestions = new();
    private List<CategorySuggestionDto> CategorySuggestions = new();
    private List<string> RecentSearches = new();
    private List<TrendingSearchDto> TrendingSearches = new();

    // Static Data
    private List<CategoryDto> Categories = new();
    private List<string> Brands = new();

    private Timer? _debounceTimer;

    protected override async Task OnInitializedAsync()
    {
        await LoadStaticData();
        await LoadRecentSearches();
        await LoadTrendingSearches();
    }

    private async Task LoadStaticData()
    {
        try
        {
            // Load categories
            var categoriesResponse = await HttpClient.GetAsync("/api/catalog/categories");
            if (categoriesResponse.IsSuccessStatusCode)
            {
                Categories = await categoriesResponse.Content.ReadFromJsonAsync<List<CategoryDto>>() ?? new();
            }

            // Load brands
            var brandsResponse = await HttpClient.GetAsync("/api/catalog/brands");
            if (brandsResponse.IsSuccessStatusCode)
            {
                Brands = await brandsResponse.Content.ReadFromJsonAsync<List<string>>() ?? new();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading static data: {ex.Message}");
        }
    }

    private async Task LoadRecentSearches()
    {
        try
        {
            var stored = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "recent-searches");
            if (!string.IsNullOrEmpty(stored))
            {
                RecentSearches = System.Text.Json.JsonSerializer.Deserialize<List<string>>(stored) ?? new();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading recent searches: {ex.Message}");
        }
    }

    private async Task LoadTrendingSearches()
    {
        try
        {
            var response = await HttpClient.GetAsync("/api/search/trending");
            if (response.IsSuccessStatusCode)
            {
                TrendingSearches = await response.Content.ReadFromJsonAsync<List<TrendingSearchDto>>() ?? new();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading trending searches: {ex.Message}");
        }
    }

    private async Task OnSearchInput()
    {
        _debounceTimer?.Dispose();
        _debounceTimer = new Timer(async _ =>
        {
            await InvokeAsync(async () =>
            {
                await LoadSuggestions();
                StateHasChanged();
            });
        }, null, 300, Timeout.Infinite);
    }

    private async Task LoadSuggestions()
    {
        if (string.IsNullOrEmpty(SearchQuery))
        {
            Suggestions.Clear();
            ProductSuggestions.Clear();
            CategorySuggestions.Clear();
            return;
        }

        try
        {
            var response = await HttpClient.GetAsync($"/api/search/suggestions?query={Uri.EscapeDataString(SearchQuery)}");
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<SearchSuggestionsDto>();
                if (result != null)
                {
                    Suggestions = result.QuerySuggestions;
                    ProductSuggestions = result.ProductSuggestions;
                    CategorySuggestions = result.CategorySuggestions;
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading suggestions: {ex.Message}");
        }
    }

    private void OnSearchFocus()
    {
        ShowSuggestions = true;
    }

    private void OnSearchBlur()
    {
        // Delay hiding to allow clicks on suggestions
        Task.Delay(200).ContinueWith(_ => InvokeAsync(() =>
        {
            ShowSuggestions = false;
            StateHasChanged();
        }));
    }

    private async Task SelectSuggestion(string suggestion)
    {
        SearchQuery = suggestion;
        ShowSuggestions = false;
        await AddToRecentSearches(suggestion);
        await PerformSearch();
    }

    private async Task SelectProduct(ProductSuggestionDto product)
    {
        // Navigate to product page
        await JSRuntime.InvokeVoidAsync("window.location.href", $"/product/{product.Id}");
    }

    private async Task SelectCategory(CategorySuggestionDto category)
    {
        SelectedCategoryId = category.Id.ToString();
        SearchQuery = "";
        ShowSuggestions = false;
        await PerformSearch();
    }

    private async Task AddToRecentSearches(string query)
    {
        if (RecentSearches.Contains(query))
        {
            RecentSearches.Remove(query);
        }
        RecentSearches.Insert(0, query);

        // Keep only last 10 searches
        if (RecentSearches.Count > 10)
        {
            RecentSearches = RecentSearches.Take(10).ToList();
        }

        await JSRuntime.InvokeVoidAsync("localStorage.setItem", "recent-searches",
            System.Text.Json.JsonSerializer.Serialize(RecentSearches));
    }

    private async Task RemoveRecentSearch(MouseEventArgs e, string query)
    {
        await JSRuntime.InvokeVoidAsync("eval", "event.stopPropagation()");
        RecentSearches.Remove(query);
        await JSRuntime.InvokeVoidAsync("localStorage.setItem", "recent-searches",
            System.Text.Json.JsonSerializer.Serialize(RecentSearches));
        StateHasChanged();
    }

    private async Task ClearSearchHistory()
    {
        RecentSearches.Clear();
        await JSRuntime.InvokeVoidAsync("localStorage.removeItem", "recent-searches");
        StateHasChanged();
    }

    private void ClearSearch()
    {
        SearchQuery = "";
        ShowSuggestions = false;
        StateHasChanged();
    }

    private void ToggleAdvancedFilters()
    {
        ShowAdvancedFilters = !ShowAdvancedFilters;
        StateHasChanged();
    }

    private void SelectMinRating(int rating)
    {
        MinRating = MinRating == rating ? 0 : rating;
        StateHasChanged();
    }

    private async Task ApplyFilters()
    {
        await PerformSearch();
    }

    private void ClearFilters()
    {
        MinPrice = null;
        MaxPrice = null;
        SelectedCategoryId = "";
        SelectedBrand = "";
        MinRating = 0;
        InStock = false;
        FreeShipping = false;
        OnSale = false;
        NewProducts = false;
        StateHasChanged();
    }

    private async Task PerformSearch()
    {
        if (!string.IsNullOrEmpty(SearchQuery))
        {
            await AddToRecentSearches(SearchQuery);
        }

        var searchParams = new SearchParams
        {
            Query = SearchQuery,
            CategoryId = string.IsNullOrEmpty(SelectedCategoryId) ? null : int.Parse(SelectedCategoryId),
            Brand = SelectedBrand,
            MinPrice = MinPrice,
            MaxPrice = MaxPrice,
            MinRating = MinRating,
            InStock = InStock,
            FreeShipping = FreeShipping,
            OnSale = OnSale,
            NewProducts = NewProducts
        };

        await OnSearch.InvokeAsync(searchParams);
        ShowSuggestions = false;
        StateHasChanged();
    }

    private async Task StartVoiceSearch()
    {
        ShowVoiceModal = true;
        StateHasChanged();

        try
        {
            await JSRuntime.InvokeVoidAsync("voiceSearch.start", DotNetObjectReference.Create(this));
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Voice search error: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", "La recherche vocale n'est pas supportée sur cet appareil");
            ShowVoiceModal = false;
            StateHasChanged();
        }
    }

    private async Task StopVoiceSearch()
    {
        await JSRuntime.InvokeVoidAsync("voiceSearch.stop");
        ShowVoiceModal = false;
        IsListening = false;
        StateHasChanged();
    }

    [JSInvokable]
    public async Task OnVoiceResult(string result)
    {
        SearchQuery = result;
        ShowVoiceModal = false;
        IsListening = false;
        await PerformSearch();
        StateHasChanged();
    }

    [JSInvokable]
    public void OnVoiceListening(bool listening)
    {
        IsListening = listening;
        StateHasChanged();
    }

    private async Task StartCameraSearch()
    {
        ShowCameraModal = true;
        StateHasChanged();

        try
        {
            await JSRuntime.InvokeVoidAsync("cameraSearch.init");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Camera search error: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", "L'accès à la caméra n'est pas disponible");
            ShowCameraModal = false;
            StateHasChanged();
        }
    }

    private void CloseCameraSearch()
    {
        ShowCameraModal = false;
        JSRuntime.InvokeVoidAsync("cameraSearch.stop");
        StateHasChanged();
    }

    private async Task TakePicture()
    {
        await JSRuntime.InvokeVoidAsync("cameraSearch.takePicture", DotNetObjectReference.Create(this));
    }

    private async Task SelectImage()
    {
        await JSRuntime.InvokeVoidAsync("document.getElementById('imageUpload').click");
    }

    private async Task HandleImageUpload(ChangeEventArgs e)
    {
        var files = e.Value as string;
        if (!string.IsNullOrEmpty(files))
        {
            await JSRuntime.InvokeVoidAsync("cameraSearch.processImage", files, DotNetObjectReference.Create(this));
        }
    }

    [JSInvokable]
    public async Task OnImageSearchResult(string[] results)
    {
        ShowCameraModal = false;
        if (results?.Length > 0)
        {
            SearchQuery = results[0];
            await PerformSearch();
        }
        StateHasChanged();
    }

    private MarkupString HighlightMatch(string text, string query)
    {
        if (string.IsNullOrEmpty(query) || string.IsNullOrEmpty(text))
            return new MarkupString(text);

        var highlighted = text.Replace(query, $"<span class='highlight'>{query}</span>", StringComparison.OrdinalIgnoreCase);
        return new MarkupString(highlighted);
    }

    public class SearchParams
    {
        public string Query { get; set; } = "";
        public int? CategoryId { get; set; }
        public string Brand { get; set; } = "";
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public int MinRating { get; set; }
        public bool InStock { get; set; }
        public bool FreeShipping { get; set; }
        public bool OnSale { get; set; }
        public bool NewProducts { get; set; }
    }

    public class SearchSuggestionsDto
    {
        public List<string> QuerySuggestions { get; set; } = new();
        public List<ProductSuggestionDto> ProductSuggestions { get; set; } = new();
        public List<CategorySuggestionDto> CategorySuggestions { get; set; } = new();
    }

    public class ProductSuggestionDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public decimal Price { get; set; }
        public string ImageUrl { get; set; } = "";
    }

    public class CategorySuggestionDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public int ProductCount { get; set; }
    }

    public class TrendingSearchDto
    {
        public string Query { get; set; } = "";
        public int GrowthPercentage { get; set; }
    }

    public class CategoryDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
    }
}

<script>
    window.voiceSearch = {
        recognition: null,
        dotNetHelper: null,

        start: function (dotNetHelper) {
            this.dotNetHelper = dotNetHelper;

            if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
                throw new Error('Speech recognition not supported');
            }

            this.recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
            this.recognition.continuous = false;
            this.recognition.interimResults = false;
            this.recognition.lang = 'fr-FR';

            this.recognition.onstart = () => {
                this.dotNetHelper.invokeMethodAsync('OnVoiceListening', true);
            };

            this.recognition.onresult = (event) => {
                const result = event.results[0][0].transcript;
                this.dotNetHelper.invokeMethodAsync('OnVoiceResult', result);
            };

            this.recognition.onerror = (event) => {
                console.error('Speech recognition error:', event.error);
                this.dotNetHelper.invokeMethodAsync('OnVoiceListening', false);
            };

            this.recognition.onend = () => {
                this.dotNetHelper.invokeMethodAsync('OnVoiceListening', false);
            };

            this.recognition.start();
        },

        stop: function () {
            if (this.recognition) {
                this.recognition.stop();
            }
        }
    };

    window.cameraSearch = {
        stream: null,
        video: null,
        canvas: null,

        init: async function () {
            this.video = document.getElementById('cameraPreview');
            this.canvas = document.getElementById('cameraCanvas');

            try {
                this.stream = await navigator.mediaDevices.getUserMedia({ video: true });
                this.video.srcObject = this.stream;
                this.video.style.display = 'block';
            } catch (error) {
                throw new Error('Camera access denied');
            }
        },

        takePicture: function (dotNetHelper) {
            const context = this.canvas.getContext('2d');
            this.canvas.width = this.video.videoWidth;
            this.canvas.height = this.video.videoHeight;
            context.drawImage(this.video, 0, 0);

            const imageData = this.canvas.toDataURL('image/jpeg');
            this.processImageData(imageData, dotNetHelper);
        },

        processImage: function (fileInput, dotNetHelper) {
            const file = fileInput.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.processImageData(e.target.result, dotNetHelper);
                };
                reader.readAsDataURL(file);
            }
        },

        processImageData: async function (imageData, dotNetHelper) {
            try {
                // Here you would typically send the image to an AI service
                // For now, we'll simulate a response
                const mockResults = ['produit similaire', 'article comparable'];
                dotNetHelper.invokeMethodAsync('OnImageSearchResult', mockResults);
            } catch (error) {
                console.error('Image processing error:', error);
            }
        },

        stop: function () {
            if (this.stream) {
                this.stream.getTracks().forEach(track => track.stop());
                this.stream = null;
            }
            if (this.video) {
                this.video.style.display = 'none';
                this.video.srcObject = null;
            }
        }
    };
</script>