using NafaPlace.Notification.Application.DTOs;

namespace NafaPlace.Notification.Application.Services;

public interface IRealTimeNotificationService
{
    // Envoi de notifications temps réel
    Task SendToUserAsync(string userId, RealTimeNotificationDto notification);
    Task SendToGroupAsync(string groupName, RealTimeNotificationDto notification);
    Task SendToAllAsync(RealTimeNotificationDto notification);
    Task SendToRoleAsync(string role, RealTimeNotificationDto notification);
    Task SendToSellersAsync(RealTimeNotificationDto notification);
    Task SendToAdminsAsync(RealTimeNotificationDto notification);
    
    // Gestion des groupes
    Task JoinGroupAsync(string userId, string groupName);
    Task LeaveGroupAsync(string userId, string groupName);
    Task JoinSellerGroupAsync(string userId, int sellerId);
    Task LeaveSellerGroupAsync(string userId, int sellerId);
    Task JoinOrderGroupAsync(string userId, int orderId);
    Task LeaveOrderGroupAsync(string userId, int orderId);
    
    // Notifications spécialisées
    Task SendNotificationCountUpdateAsync(string userId, int unreadCount);
    Task SendOrderUpdateAsync(string userId, int orderId, string status, Dictionary<string, object>? data = null);
    Task SendPaymentUpdateAsync(string userId, int orderId, string paymentStatus, Dictionary<string, object>? data = null);
    Task SendStockAlertAsync(string sellerId, int productId, string productName, int currentStock);
    Task SendSystemMaintenanceAsync(string message, DateTime? scheduledAt = null);
    Task SendChatMessageAsync(string recipientId, string senderName, string message, int chatId);
    
    // Gestion des connexions
    Task OnUserConnectedAsync(string userId, string connectionId);
    Task OnUserDisconnectedAsync(string userId, string connectionId);
    Task GetActiveUsersAsync();
    Task<List<string>> GetUserConnectionsAsync(string userId);
    Task<bool> IsUserOnlineAsync(string userId);
    Task<int> GetOnlineUsersCountAsync();
    
    // Notifications de présence
    Task SendUserOnlineStatusAsync(string userId, bool isOnline);
    Task SendTypingIndicatorAsync(string chatId, string userId, bool isTyping);
    Task SendUserActivityAsync(string userId, string activity, Dictionary<string, object>? data = null);
    
    // Notifications d'événements système
    Task SendServerStatusUpdateAsync(string status, Dictionary<string, object>? data = null);
    Task SendMaintenanceModeAsync(bool isMaintenanceMode, string? message = null);
    Task SendEmergencyAlertAsync(string message, NotificationPriority priority = NotificationPriority.Critical);
    
    // Analytics et monitoring
    Task<Dictionary<string, object>> GetConnectionStatsAsync();
    Task<List<string>> GetActiveGroupsAsync();
    Task<int> GetGroupMemberCountAsync(string groupName);
    Task LogConnectionEventAsync(string userId, string eventType, Dictionary<string, object>? data = null);
    
    // Configuration et test
    Task TestConnectionAsync(string userId);
    Task SendHeartbeatAsync();
    Task<bool> ValidateConnectionAsync(string connectionId);
}
