using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.Logging;
using Blazored.LocalStorage;

namespace NafaPlace.SellerPortal.Services;

public class ChatSignalRService : IAsyncDisposable
{
    private HubConnection? _hubConnection;
    private readonly ILocalStorageService _localStorage;
    private readonly IConfiguration _configuration;
    private readonly ILogger<ChatSignalRService> _logger;
    private readonly string _hubUrl;

    // Events pour les notifications en temps réel
    public event Action<MessageReceivedEventArgs>? OnMessageReceived;
    public event Action<string>? OnUserOnline;
    public event Action<string>? OnUserOffline;
    public event Action<TypingIndicatorEventArgs>? OnTypingIndicator;
    public event Action<int, string>? OnUserJoinedConversation;
    public event Action<int, string>? OnUserLeftConversation;
    public event Action<int>? OnNewConversation;

    public bool IsConnected => _hubConnection?.State == HubConnectionState.Connected;

    public ChatSignalRService(
        ILocalStorageService localStorage,
        IConfiguration configuration,
        ILogger<ChatSignalRService> logger)
    {
        _localStorage = localStorage;
        _configuration = configuration;
        _logger = logger;

        var baseUrl = configuration["ChatEcommerceApi:BaseUrl"] ?? "http://localhost:5000/api/chat-ecommerce";
        _hubUrl = $"{baseUrl}/chathub";
    }

    public async Task InitializeAsync()
    {
        try
        {
            if (_hubConnection != null)
            {
                _logger.LogWarning("Hub déjà initialisé");
                return;
            }

            // Récupérer le token JWT
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                _logger.LogWarning("Aucun token d'authentification trouvé");
                return;
            }

            token = token.Trim('"');

            // Créer la connexion SignalR avec le token dans la query string
            _hubConnection = new HubConnectionBuilder()
                .WithUrl($"{_hubUrl}?access_token={token}")
                .WithAutomaticReconnect(new[] { TimeSpan.Zero, TimeSpan.FromSeconds(2), TimeSpan.FromSeconds(5), TimeSpan.FromSeconds(10) })
                .Build();

            // Enregistrer les handlers d'événements
            RegisterEventHandlers();

            // Démarrer la connexion
            await _hubConnection.StartAsync();
            _logger.LogInformation("Connexion SignalR établie avec succès");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'initialisation de la connexion SignalR");
            throw;
        }
    }

    private void RegisterEventHandlers()
    {
        if (_hubConnection == null) return;

        // Réception d'un nouveau message
        _hubConnection.On<MessageReceivedEventArgs>("ReceiveMessage", (message) =>
        {
            _logger.LogInformation("Message reçu: ConversationId={ConversationId}, SenderId={SenderId}", 
                message.ConversationId, message.SenderId);
            OnMessageReceived?.Invoke(message);
        });

        // Utilisateur en ligne
        _hubConnection.On<string>("UserOnline", (userId) =>
        {
            _logger.LogInformation("Utilisateur en ligne: {UserId}", userId);
            OnUserOnline?.Invoke(userId);
        });

        // Utilisateur hors ligne
        _hubConnection.On<string>("UserOffline", (userId) =>
        {
            _logger.LogInformation("Utilisateur hors ligne: {UserId}", userId);
            OnUserOffline?.Invoke(userId);
        });

        // Indicateur de saisie
        _hubConnection.On<TypingIndicatorEventArgs>("TypingIndicator", (indicator) =>
        {
            _logger.LogInformation("Indicateur de saisie: ConversationId={ConversationId}, UserId={UserId}, IsTyping={IsTyping}", 
                indicator.ConversationId, indicator.UserId, indicator.IsTyping);
            OnTypingIndicator?.Invoke(indicator);
        });

        // Utilisateur a rejoint une conversation
        _hubConnection.On<string, int>("UserJoinedConversation", (userId, conversationId) =>
        {
            _logger.LogInformation("Utilisateur {UserId} a rejoint la conversation {ConversationId}", userId, conversationId);
            OnUserJoinedConversation?.Invoke(conversationId, userId);
        });

        // Utilisateur a quitté une conversation
        _hubConnection.On<string, int>("UserLeftConversation", (userId, conversationId) =>
        {
            _logger.LogInformation("Utilisateur {UserId} a quitté la conversation {ConversationId}", userId, conversationId);
            OnUserLeftConversation?.Invoke(conversationId, userId);
        });

        // Nouvelle conversation créée
        _hubConnection.On<int>("NewConversation", (conversationId) =>
        {
            _logger.LogInformation("Nouvelle conversation créée: {ConversationId}", conversationId);
            OnNewConversation?.Invoke(conversationId);
        });

        // Événements de reconnexion
        _hubConnection.Reconnecting += (error) =>
        {
            _logger.LogWarning(error, "Reconnexion SignalR en cours...");
            return Task.CompletedTask;
        };

        _hubConnection.Reconnected += (connectionId) =>
        {
            _logger.LogInformation("Reconnexion SignalR réussie: {ConnectionId}", connectionId);
            return Task.CompletedTask;
        };

        _hubConnection.Closed += (error) =>
        {
            _logger.LogError(error, "Connexion SignalR fermée");
            return Task.CompletedTask;
        };
    }

    public async Task JoinConversationAsync(int conversationId)
    {
        if (_hubConnection == null || _hubConnection.State != HubConnectionState.Connected)
        {
            _logger.LogWarning("Impossible de rejoindre la conversation: connexion non établie");
            return;
        }

        try
        {
            await _hubConnection.SendAsync("JoinConversation", conversationId);
            _logger.LogInformation("Rejoint la conversation {ConversationId}", conversationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la tentative de rejoindre la conversation {ConversationId}", conversationId);
            throw;
        }
    }

    public async Task LeaveConversationAsync(int conversationId)
    {
        if (_hubConnection == null || _hubConnection.State != HubConnectionState.Connected)
        {
            _logger.LogWarning("Impossible de quitter la conversation: connexion non établie");
            return;
        }

        try
        {
            await _hubConnection.SendAsync("LeaveConversation", conversationId);
            _logger.LogInformation("Quitté la conversation {ConversationId}", conversationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la tentative de quitter la conversation {ConversationId}", conversationId);
            throw;
        }
    }

    public async Task SendMessageAsync(int conversationId, string message, string senderName)
    {
        if (_hubConnection == null || _hubConnection.State != HubConnectionState.Connected)
        {
            _logger.LogWarning("Impossible d'envoyer le message: connexion non établie");
            return;
        }

        try
        {
            await _hubConnection.SendAsync("SendMessage", conversationId, message, senderName);
            _logger.LogInformation("Message envoyé à la conversation {ConversationId}", conversationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi du message à la conversation {ConversationId}", conversationId);
            throw;
        }
    }

    public async Task SendTypingIndicatorAsync(int conversationId, bool isTyping)
    {
        if (_hubConnection == null || _hubConnection.State != HubConnectionState.Connected)
        {
            return;
        }

        try
        {
            await _hubConnection.SendAsync("SendTypingIndicator", conversationId, isTyping);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de l'indicateur de saisie");
        }
    }

    public async Task MarkMessagesAsReadAsync(int conversationId, List<int> messageIds)
    {
        if (_hubConnection == null || _hubConnection.State != HubConnectionState.Connected)
        {
            return;
        }

        try
        {
            await _hubConnection.SendAsync("MarkMessagesAsRead", conversationId, messageIds);
            _logger.LogInformation("Messages marqués comme lus dans la conversation {ConversationId}", conversationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du marquage des messages comme lus");
        }
    }

    public async ValueTask DisposeAsync()
    {
        if (_hubConnection != null)
        {
            try
            {
                await _hubConnection.StopAsync();
                await _hubConnection.DisposeAsync();
                _logger.LogInformation("Connexion SignalR fermée proprement");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la fermeture de la connexion SignalR");
            }
        }
    }
}

// DTOs pour les événements SignalR
public class MessageReceivedEventArgs
{
    public int ConversationId { get; set; }
    public string SenderId { get; set; } = string.Empty;
    public string SenderName { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
}

public class TypingIndicatorEventArgs
{
    public int ConversationId { get; set; }
    public string UserId { get; set; } = string.Empty;
    public bool IsTyping { get; set; }
}

