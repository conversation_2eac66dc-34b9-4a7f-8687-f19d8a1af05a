using Microsoft.Extensions.Logging;
using NafaPlace.Recommendation.Application.DTOs;
using NafaPlace.Recommendation.Application.Interfaces;

namespace NafaPlace.Recommendation.Infrastructure.Services;

public class ContentBasedService : IContentBasedService
{
    private readonly ILogger<ContentBasedService> _logger;

    public ContentBasedService(ILogger<ContentBasedService> logger)
    {
        _logger = logger;
    }

    public async Task<List<SimilarProductDto>> GetRecommendationsAsync(string userId, int limit)
    {
        try
        {
            _logger.LogInformation("Génération de recommandations basées sur le contenu pour {UserId}", userId);

            // Simuler des recommandations basées sur le contenu
            var recommendations = new List<SimilarProductDto>();
            
            for (int i = 1; i <= limit; i++)
            {
                recommendations.Add(new SimilarProductDto
                {
                    ProductId = i + 2000,
                    Name = $"Produit Contenu {i}",
                    Price = 32000 + (i * 6000),
                    ImageUrl = $"/images/content-based-{i}.jpg",
                    SimilarityScore = 0.88 - (i * 0.04),
                    Reason = "Correspond à vos préférences",
                    IsInStock = true,
                    Rating = 4.4 + (i * 0.05),
                    ReviewCount = 90 + (i * 12)
                });
            }

            return recommendations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la génération des recommandations basées sur le contenu");
            return new List<SimilarProductDto>();
        }
    }

    public async Task<List<SimilarProductDto>> GetSimilarProductsAsync(int productId, int limit)
    {
        try
        {
            _logger.LogInformation("Recherche de produits similaires par contenu pour {ProductId}", productId);

            var similarProducts = new List<SimilarProductDto>();
            
            for (int i = 1; i <= limit; i++)
            {
                similarProducts.Add(new SimilarProductDto
                {
                    ProductId = productId + (i * 15),
                    Name = $"Produit Similaire CB {i}",
                    Price = 26000 + (i * 3500),
                    ImageUrl = $"/images/similar-cb-{productId}-{i}.jpg",
                    SimilarityScore = 0.82 - (i * 0.02),
                    Reason = "Caractéristiques similaires",
                    IsInStock = true,
                    Rating = 4.3,
                    ReviewCount = 70 + (i * 6)
                });
            }

            return similarProducts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche de produits similaires par contenu");
            return new List<SimilarProductDto>();
        }
    }
}


