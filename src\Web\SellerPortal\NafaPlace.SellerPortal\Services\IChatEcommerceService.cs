namespace NafaPlace.SellerPortal.Services;

public interface IChatEcommerceService
{
    // Health Check
    Task<bool> IsHealthyAsync();

    // FAQ Methods
    Task<List<FAQDto>> GetFAQsAsync(string? category = null);
    Task<List<string>> GetFAQCategoriesAsync();

    // Quick Reply Methods
    Task<List<QuickReplyDto>> GetQuickRepliesAsync(string? category = null);
    Task<int> CreateQuickReplyAsync(CreateQuickReplyDto quickReply);
    Task<bool> UpdateQuickReplyAsync(int id, CreateQuickReplyDto quickReply);
    Task<bool> DeleteQuickReplyAsync(int id);

    // Conversation Methods
    Task<List<ConversationDto>> GetConversationsAsync(string sellerId);
    Task<ConversationDto?> GetConversationAsync(int conversationId);
    Task<ConversationDto?> GetConversationByIdAsync(int conversationId);
    Task<bool> UpdateConversationStatusAsync(int conversationId, string status);

    // Message Methods
    Task<List<MessageDto>> GetMessagesAsync(int conversationId);
    Task<bool> SendMessageAsync(int conversationId, SendMessageDto message);
    Task<bool> SendMessageAsync(SendMessageDto message);
    Task<bool> MarkMessagesAsReadAsync(int conversationId);
}

