namespace NafaPlace.ChatEcommerce.Application.DTOs;

public class ConversationDto
{
    public int Id { get; set; }
    public string CustomerId { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public string? CustomerEmail { get; set; }
    public string? SellerId { get; set; }
    public string? SellerName { get; set; }
    public int? ProductId { get; set; }
    public string? ProductName { get; set; }
    public int? OrderId { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public string? Category { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public DateTime? LastMessageAt { get; set; }
    public DateTime? ClosedAt { get; set; }
    public string? ClosedBy { get; set; }
    public string? CloseReason { get; set; }
    public bool HasUnreadMessages { get; set; }
    public int UnreadCount { get; set; }
    public List<string> Tags { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public List<MessageDto> Messages { get; set; } = new();
    public string? LastMessage { get; set; }
    public bool IsOnline { get; set; }
}

public class CreateConversationDto
{
    public string CustomerId { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public string? CustomerEmail { get; set; }
    public string? SellerId { get; set; }
    public int? ProductId { get; set; }
    public int? OrderId { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string Type { get; set; } = "General";
    public string Priority { get; set; } = "Normal";
    public string? Category { get; set; }
    public string? InitialMessage { get; set; }
    public List<string> Tags { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class ConversationFilterDto
{
    public string? CustomerId { get; set; }
    public string? SellerId { get; set; }
    public int? ProductId { get; set; }
    public int? OrderId { get; set; }
    public string? Status { get; set; }
    public string? Type { get; set; }
    public string? Priority { get; set; }
    public string? Category { get; set; }
    public bool? HasUnreadMessages { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string? SearchTerm { get; set; }
    public List<string> Tags { get; set; } = new();
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public string SortBy { get; set; } = "UpdatedAt";
    public string SortDirection { get; set; } = "DESC";
}

public class ConversationStatsDto
{
    public int TotalConversations { get; set; }
    public int OpenConversations { get; set; }
    public int InProgressConversations { get; set; }
    public int ResolvedConversations { get; set; }
    public int ClosedConversations { get; set; }
    public int UnreadConversations { get; set; }
    public double AverageResponseTime { get; set; }
    public double AverageResolutionTime { get; set; }
    public Dictionary<string, int> ConversationsByType { get; set; } = new();
    public Dictionary<string, int> ConversationsByPriority { get; set; } = new();
}
