using System.ComponentModel.DataAnnotations;

namespace NafaPlace.ChatEcommerce.Domain.Entities;

public class Message
{
    public int Id { get; set; }
    
    [Required]
    public int ConversationId { get; set; }
    
    [Required]
    public string SenderId { get; set; } = string.Empty;
    
    [Required]
    public string SenderName { get; set; } = string.Empty;
    
    public SenderType SenderType { get; set; } = SenderType.Customer;
    
    [Required]
    [StringLength(5000)]
    public string Content { get; set; } = string.Empty;
    
    public MessageType MessageType { get; set; } = MessageType.Text;
    
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    public bool IsRead { get; set; } = false;
    
    public DateTime? ReadAt { get; set; }
    
    public bool IsEdited { get; set; } = false;
    
    public DateTime? EditedAt { get; set; }
    
    public string? AttachmentUrl { get; set; }
    
    public string? AttachmentType { get; set; }
    
    public long? AttachmentSize { get; set; }
    
    public string? Metadata { get; set; } // JSON metadata
    
    // Navigation properties
    public virtual Conversation Conversation { get; set; } = null!;
}

public enum SenderType
{
    Customer = 0,
    Seller = 1,
    Admin = 2,
    System = 3,
    Bot = 4
}

public enum MessageType
{
    Text = 0,
    Image = 1,
    File = 2,
    System = 3,
    ProductLink = 4,
    OrderLink = 5,
    QuickReply = 6
}
