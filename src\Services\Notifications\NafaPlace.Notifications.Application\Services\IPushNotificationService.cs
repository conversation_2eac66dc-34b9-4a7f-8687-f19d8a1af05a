using NafaPlace.Notifications.Domain.DTOs;

namespace NafaPlace.Notifications.Application.Services;

public interface IPushNotificationService
{
    Task<NotificationResult> SendToUserAsync(string userId, PushNotificationDto notification);
    Task<NotificationResult> SendToUsersAsync(List<string> userIds, PushNotificationDto notification);
    Task<NotificationResult> SendToAllAsync(PushNotificationDto notification);
    Task<NotificationResult> SendToSegmentAsync(string segment, PushNotificationDto notification);
    Task<NotificationResult> SendWelcomeNotificationAsync(string userId);
    Task<NotificationResult> SendOrderNotificationAsync(string userId, string orderId, string status);
    Task<NotificationResult> SendPromotionNotificationAsync(List<string> userIds, string promotionTitle, string promotionMessage);
    Task<NotificationResult> SendStockAlertAsync(string userId, string productName, string productId);
    Task<NotificationResult> SendPriceDropNotificationAsync(string userId, string productName, decimal oldPrice, decimal newPrice);

    // Scheduling
    Task<ScheduleResult> ScheduleNotificationAsync(PushNotificationDto notification, DateTime scheduledFor, List<string>? userIds = null, string? segment = null);
    Task<NotificationResult> CancelScheduledNotificationAsync(int scheduledNotificationId);
    Task ProcessScheduledNotificationsAsync();

    // Analytics and Reporting
    Task<NotificationStatsDto> GetNotificationStatsAsync(DateTime? fromDate = null, DateTime? toDate = null);
    Task<NotificationReportDto?> GetNotificationReportAsync(int notificationId);
    Task TrackNotificationClickAsync(int notificationId, string userId, DateTime clickedAt);

    // Configuration
    string GetVapidPublicKey();
    Task<bool> ValidateSubscriptionAsync(string endpoint);

    // Bulk operations
    Task<NotificationResult> SendBulkNotificationsAsync(List<BulkNotificationDto> notifications);
    Task<NotificationResult> SendTemplateNotificationAsync(string templateName, Dictionary<string, object> parameters, List<string> userIds);
}

public class NotificationResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public int SentCount { get; set; }
    public int FailedCount { get; set; }
    public List<string> FailedEndpoints { get; set; } = new();
    public string? NotificationId { get; set; }
}

public class ScheduleResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public int ScheduledNotificationId { get; set; }
}

public class BulkNotificationDto
{
    public string UserId { get; set; } = "";
    public PushNotificationDto Notification { get; set; } = new();
}