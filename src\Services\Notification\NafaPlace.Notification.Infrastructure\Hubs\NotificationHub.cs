using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using NafaPlace.Notification.Application.DTOs;
using NafaPlace.Notification.Application.Services;
using System.Security.Claims;

namespace NafaPlace.Notification.Infrastructure.Hubs;

[Authorize]
public class NotificationHub : Hub
{
    private readonly IRealTimeNotificationService _realTimeService;
    private readonly INotificationService _notificationService;
    private readonly ILogger<NotificationHub> _logger;

    public NotificationHub(
        IRealTimeNotificationService realTimeService,
        INotificationService notificationService,
        ILogger<NotificationHub> logger)
    {
        _realTimeService = realTimeService;
        _notificationService = notificationService;
        _logger = logger;
    }

    public override async Task OnConnectedAsync()
    {
        var userId = GetUserId();
        var userRole = GetUserRole();
        var sellerId = GetSellerId();

        _logger.LogInformation("Utilisateur {UserId} connecté avec la connexion {ConnectionId}", userId, Context.ConnectionId);

        // Ajouter aux groupes appropriés
        await Groups.AddToGroupAsync(Context.ConnectionId, "AllUsers");
        
        if (!string.IsNullOrEmpty(userRole))
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, $"Role_{userRole}");
        }

        if (sellerId.HasValue)
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, $"Seller_{sellerId}");
        }

        // Notifier le service de la connexion
        await _realTimeService.OnUserConnectedAsync(userId, Context.ConnectionId);

        // Envoyer le nombre de notifications non lues
        var unreadCount = await _notificationService.GetUnreadCountAsync(userId);
        await Clients.Caller.SendAsync("NotificationCountUpdate", unreadCount);

        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var userId = GetUserId();
        
        _logger.LogInformation("Utilisateur {UserId} déconnecté de la connexion {ConnectionId}", userId, Context.ConnectionId);

        await _realTimeService.OnUserDisconnectedAsync(userId, Context.ConnectionId);

        if (exception != null)
        {
            _logger.LogError(exception, "Déconnexion avec erreur pour {UserId}", userId);
        }

        await base.OnDisconnectedAsync(exception);
    }

    // Méthodes appelables par le client
    public async Task JoinGroup(string groupName)
    {
        var userId = GetUserId();
        
        _logger.LogInformation("Utilisateur {UserId} rejoint le groupe {GroupName}", userId, groupName);
        
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
        await _realTimeService.JoinGroupAsync(userId, groupName);
    }

    public async Task LeaveGroup(string groupName)
    {
        var userId = GetUserId();
        
        _logger.LogInformation("Utilisateur {UserId} quitte le groupe {GroupName}", userId, groupName);
        
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
        await _realTimeService.LeaveGroupAsync(userId, groupName);
    }

    public async Task JoinOrderGroup(int orderId)
    {
        var userId = GetUserId();
        var groupName = $"Order_{orderId}";
        
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
        await _realTimeService.JoinOrderGroupAsync(userId, orderId);
        
        _logger.LogInformation("Utilisateur {UserId} a rejoint le groupe de commande {OrderId}", userId, orderId);
    }

    public async Task LeaveOrderGroup(int orderId)
    {
        var userId = GetUserId();
        var groupName = $"Order_{orderId}";
        
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
        await _realTimeService.LeaveOrderGroupAsync(userId, orderId);
        
        _logger.LogInformation("Utilisateur {UserId} a quitté le groupe de commande {OrderId}", userId, orderId);
    }

    public async Task MarkNotificationAsRead(int notificationId)
    {
        var userId = GetUserId();
        
        var success = await _notificationService.MarkAsReadAsync(notificationId, userId);
        
        if (success)
        {
            await Clients.Caller.SendAsync("NotificationMarkedAsRead", notificationId);
        }
    }

    public async Task MarkAllNotificationsAsRead()
    {
        var userId = GetUserId();
        
        var success = await _notificationService.MarkAllAsReadAsync(userId);
        
        if (success)
        {
            await Clients.Caller.SendAsync("AllNotificationsMarkedAsRead");
        }
    }

    public async Task SendTypingIndicator(int chatId, bool isTyping)
    {
        var userId = GetUserId();
        
        await _realTimeService.SendTypingIndicatorAsync(chatId.ToString(), userId, isTyping);
    }

    public async Task UpdateUserActivity(string activity, Dictionary<string, object>? data = null)
    {
        var userId = GetUserId();
        
        await _realTimeService.SendUserActivityAsync(userId, activity, data);
    }

    public async Task RequestNotificationHistory(int page = 1, int pageSize = 20)
    {
        var userId = GetUserId();
        
        try
        {
            var filter = new NotificationFilterDto
            {
                RecipientId = userId,
                Page = page,
                PageSize = pageSize
            };

            var notifications = await _notificationService.GetNotificationsAsync(filter);
            
            await Clients.Caller.SendAsync("NotificationHistory", new
            {
                notifications,
                page,
                pageSize,
                hasMore = notifications.Count == pageSize
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de l'historique des notifications pour {UserId}", userId);
            await Clients.Caller.SendAsync("Error", "Impossible de récupérer l'historique des notifications");
        }
    }

    public async Task TestConnection()
    {
        var userId = GetUserId();
        
        await Clients.Caller.SendAsync("ConnectionTest", new
        {
            userId,
            connectionId = Context.ConnectionId,
            timestamp = DateTime.UtcNow,
            message = "Connexion active"
        });
        
        _logger.LogInformation("Test de connexion effectué pour {UserId}", userId);
    }

    // Méthodes d'aide privées
    private string GetUserId()
    {
        return Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "anonymous";
    }

    private string? GetUserRole()
    {
        return Context.User?.FindFirst(ClaimTypes.Role)?.Value;
    }

    private int? GetSellerId()
    {
        var sellerIdClaim = Context.User?.FindFirst("SellerId")?.Value;
        return int.TryParse(sellerIdClaim, out var sellerId) ? sellerId : null;
    }

    private string GetUserName()
    {
        return Context.User?.FindFirst(ClaimTypes.Name)?.Value ?? "Utilisateur";
    }
}

// Service d'implémentation pour les notifications temps réel
public class RealTimeNotificationService : IRealTimeNotificationService
{
    private readonly IHubContext<NotificationHub> _hubContext;
    private readonly ILogger<RealTimeNotificationService> _logger;
    private readonly Dictionary<string, List<string>> _userConnections = new();
    private readonly Dictionary<string, HashSet<string>> _groupMembers = new();

    public RealTimeNotificationService(
        IHubContext<NotificationHub> hubContext,
        ILogger<RealTimeNotificationService> logger)
    {
        _hubContext = hubContext;
        _logger = logger;
    }

    public async Task SendToUserAsync(string userId, RealTimeNotificationDto notification)
    {
        try
        {
            _logger.LogInformation("Envoi notification temps réel à {UserId}: {Title}", userId, notification.Title);
            
            await _hubContext.Clients.User(userId).SendAsync("ReceiveNotification", notification);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de notification temps réel à {UserId}", userId);
        }
    }

    public async Task SendToGroupAsync(string groupName, RealTimeNotificationDto notification)
    {
        try
        {
            _logger.LogInformation("Envoi notification temps réel au groupe {GroupName}: {Title}", groupName, notification.Title);
            
            await _hubContext.Clients.Group(groupName).SendAsync("ReceiveNotification", notification);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de notification temps réel au groupe {GroupName}", groupName);
        }
    }

    public async Task SendToAllAsync(RealTimeNotificationDto notification)
    {
        try
        {
            _logger.LogInformation("Envoi notification temps réel à tous: {Title}", notification.Title);
            
            await _hubContext.Clients.All.SendAsync("ReceiveNotification", notification);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de notification temps réel à tous");
        }
    }

    public async Task SendToRoleAsync(string role, RealTimeNotificationDto notification)
    {
        try
        {
            await _hubContext.Clients.Group($"Role_{role}").SendAsync("ReceiveNotification", notification);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de notification au rôle {Role}", role);
        }
    }

    public async Task SendToSellersAsync(RealTimeNotificationDto notification)
    {
        await SendToRoleAsync("Seller", notification);
    }

    public async Task SendToAdminsAsync(RealTimeNotificationDto notification)
    {
        await SendToRoleAsync("Admin", notification);
    }

    public async Task JoinGroupAsync(string userId, string groupName)
    {
        try
        {
            if (!_groupMembers.ContainsKey(groupName))
            {
                _groupMembers[groupName] = new HashSet<string>();
            }
            
            _groupMembers[groupName].Add(userId);
            
            _logger.LogInformation("Utilisateur {UserId} ajouté au groupe {GroupName}", userId, groupName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ajout de {UserId} au groupe {GroupName}", userId, groupName);
        }
        
        await Task.CompletedTask;
    }

    public async Task LeaveGroupAsync(string userId, string groupName)
    {
        try
        {
            if (_groupMembers.ContainsKey(groupName))
            {
                _groupMembers[groupName].Remove(userId);
                
                if (_groupMembers[groupName].Count == 0)
                {
                    _groupMembers.Remove(groupName);
                }
            }
            
            _logger.LogInformation("Utilisateur {UserId} retiré du groupe {GroupName}", userId, groupName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de {UserId} du groupe {GroupName}", userId, groupName);
        }
        
        await Task.CompletedTask;
    }

    public async Task JoinSellerGroupAsync(string userId, int sellerId)
    {
        await JoinGroupAsync(userId, $"Seller_{sellerId}");
    }

    public async Task LeaveSellerGroupAsync(string userId, int sellerId)
    {
        await LeaveGroupAsync(userId, $"Seller_{sellerId}");
    }

    public async Task JoinOrderGroupAsync(string userId, int orderId)
    {
        await JoinGroupAsync(userId, $"Order_{orderId}");
    }

    public async Task LeaveOrderGroupAsync(string userId, int orderId)
    {
        await LeaveGroupAsync(userId, $"Order_{orderId}");
    }

    public async Task SendNotificationCountUpdateAsync(string userId, int unreadCount)
    {
        try
        {
            await _hubContext.Clients.User(userId).SendAsync("NotificationCountUpdate", unreadCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de mise à jour du compteur à {UserId}", userId);
        }
    }

    public async Task SendOrderUpdateAsync(string userId, int orderId, string status, Dictionary<string, object>? data = null)
    {
        var notification = new RealTimeNotificationDto
        {
            Id = Guid.NewGuid().ToString(),
            Title = "Mise à jour de commande",
            Message = $"Votre commande #{orderId} est maintenant {status}",
            Type = "OrderUpdate",
            Priority = "Normal",
            Data = data ?? new Dictionary<string, object> { ["orderId"] = orderId, ["status"] = status },
            ActionUrl = $"/orders/{orderId}"
        };

        await SendToUserAsync(userId, notification);
        await SendToGroupAsync($"Order_{orderId}", notification);
    }

    public async Task SendPaymentUpdateAsync(string userId, int orderId, string paymentStatus, Dictionary<string, object>? data = null)
    {
        var notification = new RealTimeNotificationDto
        {
            Id = Guid.NewGuid().ToString(),
            Title = "Mise à jour de paiement",
            Message = $"Le paiement de votre commande #{orderId} est {paymentStatus}",
            Type = "PaymentUpdate",
            Priority = paymentStatus == "completed" ? "High" : "Normal",
            Data = data ?? new Dictionary<string, object> { ["orderId"] = orderId, ["paymentStatus"] = paymentStatus },
            ActionUrl = $"/orders/{orderId}"
        };

        await SendToUserAsync(userId, notification);
    }

    public async Task SendStockAlertAsync(string sellerId, int productId, string productName, int currentStock)
    {
        var notification = new RealTimeNotificationDto
        {
            Id = Guid.NewGuid().ToString(),
            Title = "Alerte de stock",
            Message = $"Stock faible pour {productName}: {currentStock} unités restantes",
            Type = "StockAlert",
            Priority = currentStock == 0 ? "Critical" : "High",
            Data = new Dictionary<string, object> { ["productId"] = productId, ["currentStock"] = currentStock },
            ActionUrl = $"/seller/products/{productId}"
        };

        await SendToUserAsync(sellerId, notification);
    }

    public async Task SendSystemMaintenanceAsync(string message, DateTime? scheduledAt = null)
    {
        var notification = new RealTimeNotificationDto
        {
            Id = Guid.NewGuid().ToString(),
            Title = "Maintenance système",
            Message = message,
            Type = "SystemMaintenance",
            Priority = "High",
            Data = new Dictionary<string, object> { ["scheduledAt"] = scheduledAt ?? DateTime.UtcNow }
        };

        await SendToAllAsync(notification);
    }

    public async Task SendChatMessageAsync(string recipientId, string senderName, string message, int chatId)
    {
        var notification = new RealTimeNotificationDto
        {
            Id = Guid.NewGuid().ToString(),
            Title = $"Nouveau message de {senderName}",
            Message = message,
            Type = "ChatMessage",
            Priority = "Normal",
            Data = new Dictionary<string, object> { ["chatId"] = chatId, ["senderName"] = senderName },
            ActionUrl = $"/chat/{chatId}"
        };

        await SendToUserAsync(recipientId, notification);
    }

    public async Task OnUserConnectedAsync(string userId, string connectionId)
    {
        try
        {
            if (!_userConnections.ContainsKey(userId))
            {
                _userConnections[userId] = new List<string>();
            }
            
            _userConnections[userId].Add(connectionId);
            
            // Notifier les autres utilisateurs de la présence
            await SendUserOnlineStatusAsync(userId, true);
            
            _logger.LogInformation("Connexion {ConnectionId} ajoutée pour {UserId}", connectionId, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ajout de connexion pour {UserId}", userId);
        }
    }

    public async Task OnUserDisconnectedAsync(string userId, string connectionId)
    {
        try
        {
            if (_userConnections.ContainsKey(userId))
            {
                _userConnections[userId].Remove(connectionId);
                
                if (_userConnections[userId].Count == 0)
                {
                    _userConnections.Remove(userId);
                    await SendUserOnlineStatusAsync(userId, false);
                }
            }
            
            _logger.LogInformation("Connexion {ConnectionId} supprimée pour {UserId}", connectionId, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de connexion pour {UserId}", userId);
        }
    }

    public async Task GetActiveUsersAsync()
    {
        await Task.CompletedTask;
        // Retourner la liste des utilisateurs actifs
    }

    public async Task<List<string>> GetUserConnectionsAsync(string userId)
    {
        await Task.CompletedTask;
        return _userConnections.GetValueOrDefault(userId, new List<string>());
    }

    public async Task<bool> IsUserOnlineAsync(string userId)
    {
        await Task.CompletedTask;
        return _userConnections.ContainsKey(userId) && _userConnections[userId].Any();
    }

    public async Task<int> GetOnlineUsersCountAsync()
    {
        await Task.CompletedTask;
        return _userConnections.Count;
    }

    public async Task SendUserOnlineStatusAsync(string userId, bool isOnline)
    {
        try
        {
            await _hubContext.Clients.All.SendAsync("UserOnlineStatus", new { userId, isOnline });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi du statut en ligne pour {UserId}", userId);
        }
    }

    public async Task SendTypingIndicatorAsync(string chatId, string userId, bool isTyping)
    {
        try
        {
            await _hubContext.Clients.Group($"Chat_{chatId}").SendAsync("TypingIndicator", new { userId, isTyping });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de l'indicateur de frappe");
        }
    }

    public async Task SendUserActivityAsync(string userId, string activity, Dictionary<string, object>? data = null)
    {
        try
        {
            await _hubContext.Clients.All.SendAsync("UserActivity", new { userId, activity, data, timestamp = DateTime.UtcNow });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de l'activité utilisateur");
        }
    }

    public async Task SendServerStatusUpdateAsync(string status, Dictionary<string, object>? data = null)
    {
        var notification = new RealTimeNotificationDto
        {
            Id = Guid.NewGuid().ToString(),
            Title = "Statut du serveur",
            Message = $"Statut du serveur: {status}",
            Type = "ServerStatus",
            Priority = "Normal",
            Data = data ?? new Dictionary<string, object>()
        };

        await SendToAllAsync(notification);
    }

    public async Task SendMaintenanceModeAsync(bool isMaintenanceMode, string? message = null)
    {
        var notification = new RealTimeNotificationDto
        {
            Id = Guid.NewGuid().ToString(),
            Title = isMaintenanceMode ? "Mode maintenance activé" : "Mode maintenance désactivé",
            Message = message ?? (isMaintenanceMode ? "Le site est en maintenance" : "Le site est de nouveau disponible"),
            Type = "MaintenanceMode",
            Priority = "High",
            Data = new Dictionary<string, object> { ["isMaintenanceMode"] = isMaintenanceMode }
        };

        await SendToAllAsync(notification);
    }

    public async Task SendEmergencyAlertAsync(string message, NotificationPriority priority = NotificationPriority.Critical)
    {
        var notification = new RealTimeNotificationDto
        {
            Id = Guid.NewGuid().ToString(),
            Title = "Alerte d'urgence",
            Message = message,
            Type = "EmergencyAlert",
            Priority = priority.ToString(),
            Data = new Dictionary<string, object>(),
            RequiresAction = true
        };

        await SendToAllAsync(notification);
    }

    public async Task<Dictionary<string, object>> GetConnectionStatsAsync()
    {
        await Task.CompletedTask;
        
        return new Dictionary<string, object>
        {
            ["totalConnections"] = _userConnections.Values.Sum(connections => connections.Count),
            ["uniqueUsers"] = _userConnections.Count,
            ["activeGroups"] = _groupMembers.Count,
            ["timestamp"] = DateTime.UtcNow
        };
    }

    public async Task<List<string>> GetActiveGroupsAsync()
    {
        await Task.CompletedTask;
        return _groupMembers.Keys.ToList();
    }

    public async Task<int> GetGroupMemberCountAsync(string groupName)
    {
        await Task.CompletedTask;
        return _groupMembers.GetValueOrDefault(groupName, new HashSet<string>()).Count;
    }

    public async Task LogConnectionEventAsync(string userId, string eventType, Dictionary<string, object>? data = null)
    {
        _logger.LogInformation("Événement de connexion: {EventType} pour {UserId} - Data: {@Data}", eventType, userId, data);
        await Task.CompletedTask;
    }

    public async Task TestConnectionAsync(string userId)
    {
        var testNotification = new RealTimeNotificationDto
        {
            Id = Guid.NewGuid().ToString(),
            Title = "Test de connexion",
            Message = "Votre connexion temps réel fonctionne correctement",
            Type = "ConnectionTest",
            Priority = "Low",
            Data = new Dictionary<string, object> { ["timestamp"] = DateTime.UtcNow }
        };

        await SendToUserAsync(userId, testNotification);
    }

    public async Task SendHeartbeatAsync()
    {
        try
        {
            await _hubContext.Clients.All.SendAsync("Heartbeat", DateTime.UtcNow);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi du heartbeat");
        }
    }

    public async Task<bool> ValidateConnectionAsync(string connectionId)
    {
        await Task.CompletedTask;
        return _userConnections.Values.Any(connections => connections.Contains(connectionId));
    }
}
