using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Notifications.Application.Services;
using NafaPlace.Notifications.Domain.DTOs;
using System.Security.Claims;

namespace NafaPlace.Notifications.API.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class PushNotificationsController : ControllerBase
{
    private readonly IPushNotificationService _pushNotificationService;
    private readonly INotificationSubscriptionService _subscriptionService;
    private readonly ILogger<PushNotificationsController> _logger;

    public PushNotificationsController(
        IPushNotificationService pushNotificationService,
        INotificationSubscriptionService subscriptionService,
        ILogger<PushNotificationsController> logger)
    {
        _pushNotificationService = pushNotificationService;
        _subscriptionService = subscriptionService;
        _logger = logger;
    }

    /// <summary>
    /// Subscribe to push notifications
    /// </summary>
    [HttpPost("subscribe")]
    [Authorize]
    public async Task<IActionResult> Subscribe([FromBody] PushSubscriptionDto subscription)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            subscription.UserId = userId;
            subscription.CreatedAt = DateTime.UtcNow;

            var result = await _subscriptionService.CreateSubscriptionAsync(subscription);
            if (result.Success)
            {
                // Send welcome notification
                await _pushNotificationService.SendWelcomeNotificationAsync(userId);

                return Ok(new { Success = true, Message = "Subscription created successfully" });
            }

            return BadRequest(new { Success = false, Message = result.ErrorMessage });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating push subscription for user {UserId}", User.Identity?.Name);
            return StatusCode(500, new { Success = false, Message = "Internal server error" });
        }
    }

    /// <summary>
    /// Update push subscription
    /// </summary>
    [HttpPut("subscribe")]
    [Authorize]
    public async Task<IActionResult> UpdateSubscription([FromBody] UpdatePushSubscriptionDto updateDto)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            updateDto.UserId = userId;

            var result = await _subscriptionService.UpdateSubscriptionAsync(userId, updateDto);
            return Ok(new { Success = result, Message = result ? "Subscription updated successfully" : "Failed to update subscription" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating push subscription for user {UserId}", User.Identity?.Name);
            return StatusCode(500, new { Success = false, Message = "Internal server error" });
        }
    }

    /// <summary>
    /// Unsubscribe from push notifications
    /// </summary>
    [HttpDelete("subscribe")]
    [Authorize]
    public async Task<IActionResult> Unsubscribe([FromBody] UnsubscribePushDto unsubscribeDto)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            var result = await _subscriptionService.RemoveSubscriptionAsync(unsubscribeDto.Endpoint);
            return Ok(new { Success = result, Message = result ? "Subscription removed successfully" : "Failed to remove subscription" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing push subscription for user {UserId}", User.Identity?.Name);
            return StatusCode(500, new { Success = false, Message = "Internal server error" });
        }
    }

    /// <summary>
    /// Get user's notification preferences
    /// </summary>
    [HttpGet("preferences")]
    [Authorize]
    public async Task<IActionResult> GetNotificationPreferences()
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            var preferences = await _subscriptionService.GetUserPreferencesAsync(userId);
            return Ok(preferences);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting notification preferences for user {UserId}", User.Identity?.Name);
            return StatusCode(500, new { Success = false, Message = "Internal server error" });
        }
    }

    /// <summary>
    /// Update user's notification preferences
    /// </summary>
    [HttpPut("preferences")]
    [Authorize]
    public async Task<IActionResult> UpdateNotificationPreferences([FromBody] NotificationPreferencesDto preferences)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            preferences.UserId = userId;

            var result = await _subscriptionService.UpdateUserPreferencesAsync(userId, preferences);
            return Ok(new { Success = result, Message = result ? "Preferences updated successfully" : "Failed to update preferences" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating notification preferences for user {UserId}", User.Identity?.Name);
            return StatusCode(500, new { Success = false, Message = "Internal server error" });
        }
    }

    /// <summary>
    /// Send test notification (admin only)
    /// </summary>
    [HttpPost("test")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> SendTestNotification([FromBody] TestNotificationDto testDto)
    {
        try
        {
            var notification = new PushNotificationDto
            {
                Title = testDto.Title,
                Body = testDto.Body,
                Icon = testDto.Icon ?? "/images/logo.svg",
                Badge = 1,
                Data = new Dictionary<string, string>
                {
                    {"type", "test"},
                    {"timestamp", DateTime.UtcNow.ToString("O")}
                }
            };

            NotificationResult result;

            if (!string.IsNullOrEmpty(testDto.UserId))
            {
                // Send to specific user
                result = await _pushNotificationService.SendToUserAsync(testDto.UserId, notification);
            }
            else
            {
                // Send to all subscribed users
                result = await _pushNotificationService.SendToAllAsync(notification);
            }

            return Ok(new
            {
                Success = result.Success,
                Message = result.ErrorMessage,
                SentCount = result.SentCount,
                FailedCount = result.FailedCount
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending test notification");
            return StatusCode(500, new { Success = false, Message = "Internal server error" });
        }
    }

    /// <summary>
    /// Send notification to specific users (admin only)
    /// </summary>
    [HttpPost("send")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> SendNotification([FromBody] SendNotificationDto sendDto)
    {
        try
        {
            var notification = new PushNotificationDto
            {
                Title = sendDto.Title,
                Body = sendDto.Body,
                Icon = sendDto.Icon ?? "/images/logo.svg",
                Badge = 1,
                Url = sendDto.Url,
                Data = sendDto.Data?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.ToString() ?? "") ?? new Dictionary<string, string>()
            };

            NotificationResult result;

            if (sendDto.UserIds?.Any() == true)
            {
                // Send to specific users
                result = await _pushNotificationService.SendToUsersAsync(sendDto.UserIds, notification);
            }
            else if (!string.IsNullOrEmpty(sendDto.Segment))
            {
                // Send to user segment
                result = await _pushNotificationService.SendToSegmentAsync(sendDto.Segment, notification);
            }
            else
            {
                // Send to all users
                result = await _pushNotificationService.SendToAllAsync(notification);
            }

            return Ok(new
            {
                Success = result.Success,
                Message = result.ErrorMessage,
                SentCount = result.SentCount,
                FailedCount = result.FailedCount
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending notification");
            return StatusCode(500, new { Success = false, Message = "Internal server error" });
        }
    }

    /// <summary>
    /// Get notification statistics (admin only)
    /// </summary>
    [HttpGet("stats")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> GetNotificationStats([FromQuery] DateTime? fromDate, [FromQuery] DateTime? toDate)
    {
        try
        {
            var stats = await _pushNotificationService.GetNotificationStatsAsync(fromDate, toDate);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting notification statistics");
            return StatusCode(500, new { Success = false, Message = "Internal server error" });
        }
    }

    /// <summary>
    /// Get notification delivery report (admin only)
    /// </summary>
    [HttpGet("reports/{notificationId}")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> GetNotificationReport(int notificationId)
    {
        try
        {
            var report = await _pushNotificationService.GetNotificationReportAsync(notificationId);
            if (report == null)
                return NotFound();

            return Ok(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting notification report for {NotificationId}", notificationId);
            return StatusCode(500, new { Success = false, Message = "Internal server error" });
        }
    }

    /// <summary>
    /// Schedule a notification (admin only)
    /// </summary>
    [HttpPost("schedule")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> ScheduleNotification([FromBody] ScheduleNotificationDto scheduleDto)
    {
        try
        {
            var notification = new PushNotificationDto
            {
                Title = scheduleDto.Title,
                Body = scheduleDto.Body,
                Icon = scheduleDto.Icon ?? "/images/logo.svg",
                Badge = 1,
                Url = scheduleDto.Url,
                Data = scheduleDto.Data?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.ToString() ?? "") ?? new Dictionary<string, string>()
            };

            var result = await _pushNotificationService.ScheduleNotificationAsync(
                notification,
                scheduleDto.ScheduledFor,
                scheduleDto.UserIds,
                scheduleDto.Segment);

            return Ok(new
            {
                Success = result.Success,
                Message = result.ErrorMessage,
                ScheduledNotificationId = result.ScheduledNotificationId
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling notification");
            return StatusCode(500, new { Success = false, Message = "Internal server error" });
        }
    }

    /// <summary>
    /// Cancel a scheduled notification (admin only)
    /// </summary>
    [HttpDelete("schedule/{scheduledNotificationId}")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> CancelScheduledNotification(int scheduledNotificationId)
    {
        try
        {
            var result = await _pushNotificationService.CancelScheduledNotificationAsync(scheduledNotificationId);
            return Ok(new { Success = result.Success, Message = result.ErrorMessage });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error canceling scheduled notification {ScheduledNotificationId}", scheduledNotificationId);
            return StatusCode(500, new { Success = false, Message = "Internal server error" });
        }
    }

    /// <summary>
    /// Get VAPID public key for client subscription
    /// </summary>
    [HttpGet("vapid-key")]
    public IActionResult GetVapidPublicKey()
    {
        try
        {
            var publicKey = _pushNotificationService.GetVapidPublicKey();
            return Ok(new { PublicKey = publicKey });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting VAPID public key");
            return StatusCode(500, new { Success = false, Message = "Internal server error" });
        }
    }

    /// <summary>
    /// Mark notification as clicked (for analytics)
    /// </summary>
    [HttpPost("click/{notificationId}")]
    [Authorize]
    public async Task<IActionResult> TrackNotificationClick(int notificationId, [FromBody] ClickTrackingDto clickDto)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            await _pushNotificationService.TrackNotificationClickAsync(notificationId, userId, clickDto.ClickedAt);
            return Ok(new { Success = true });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error tracking notification click");
            return StatusCode(500, new { Success = false, Message = "Internal server error" });
        }
    }
}

// DTOs for the controller
public class TestNotificationDto
{
    public string Title { get; set; } = "";
    public string Body { get; set; } = "";
    public string? Icon { get; set; }
    public string? UserId { get; set; }
}

public class SendNotificationDto
{
    public string Title { get; set; } = "";
    public string Body { get; set; } = "";
    public string? Icon { get; set; }
    public string? Url { get; set; }
    public List<string>? UserIds { get; set; }
    public string? Segment { get; set; }
    public Dictionary<string, object>? Data { get; set; }
}

public class ScheduleNotificationDto
{
    public string Title { get; set; } = "";
    public string Body { get; set; } = "";
    public string? Icon { get; set; }
    public string? Url { get; set; }
    public DateTime ScheduledFor { get; set; }
    public List<string>? UserIds { get; set; }
    public string? Segment { get; set; }
    public Dictionary<string, object>? Data { get; set; }
}

public class UnsubscribePushDto
{
    public string Endpoint { get; set; } = "";
}

public class ClickTrackingDto
{
    public DateTime ClickedAt { get; set; } = DateTime.UtcNow;
}