using System.ComponentModel.DataAnnotations;

namespace NafaPlace.ChatEcommerce.Domain.Entities;

public class Conversation
{
    public int Id { get; set; }
    
    [Required]
    public string CustomerId { get; set; } = string.Empty;
    
    [Required]
    public string CustomerName { get; set; } = string.Empty;
    
    public string? CustomerEmail { get; set; }
    
    public string? SellerId { get; set; }
    
    public string? SellerName { get; set; }
    
    public int? ProductId { get; set; }
    
    public string? ProductName { get; set; }
    
    public int? OrderId { get; set; }
    
    [Required]
    [StringLength(200)]
    public string Subject { get; set; } = string.Empty;
    
    public ConversationType Type { get; set; } = ConversationType.General;
    
    public ConversationStatus Status { get; set; } = ConversationStatus.Open;
    
    public ConversationPriority Priority { get; set; } = ConversationPriority.Normal;
    
    public string? Category { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? LastMessageAt { get; set; }
    
    public DateTime? ClosedAt { get; set; }
    
    public string? ClosedBy { get; set; }
    
    public string? CloseReason { get; set; }
    
    public bool HasUnreadMessages { get; set; } = false;
    
    public int UnreadCount { get; set; } = 0;
    
    public string? Tags { get; set; } // JSON array of tags
    
    public string? Metadata { get; set; } // JSON metadata
    
    // Navigation properties
    public virtual ICollection<Message> Messages { get; set; } = new List<Message>();
}

public enum ConversationType
{
    General = 0,
    ProductInquiry = 1,
    OrderSupport = 2,
    TechnicalSupport = 3,
    Complaint = 4,
    PreSale = 5,
    AfterSale = 6,
    Return = 7,
    Refund = 8
}

public enum ConversationStatus
{
    Open = 0,
    InProgress = 1,
    Waiting = 2,
    Resolved = 3,
    Closed = 4,
    Escalated = 5
}

public enum ConversationPriority
{
    Low = 0,
    Normal = 1,
    High = 2,
    Urgent = 3,
    Critical = 4
}
