<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <BlazorWebAssemblyLoadAllGlobalizationData>false</BlazorWebAssemblyLoadAllGlobalizationData>
    <PublishTrimmed>false</PublishTrimmed>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Blazored.LocalStorage" Version="4.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="9.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="9.0.2" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="9.0.2" PrivateAssets="all" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.2" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.3" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.0.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../../../Services/Catalog/NafaPlace.Catalog.Domain/NafaPlace.Catalog.Domain.csproj" />
    <ProjectReference Include="../../../BuildingBlocks/Common/NafaPlace.Common/NafaPlace.Common.csproj" />
  </ItemGroup>

</Project>
