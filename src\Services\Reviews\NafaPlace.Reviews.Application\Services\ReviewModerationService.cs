using Microsoft.Extensions.Logging;
using NafaPlace.Reviews.Application.Interfaces;
using NafaPlace.Reviews.DTOs;
using NafaPlace.Reviews.Domain.Models;

namespace NafaPlace.Reviews.Application.Services;

public class ReviewModerationService : IReviewModerationService
{
    private readonly IReviewRepository _reviewRepository;
    private readonly ILogger<ReviewModerationService> _logger;

    public ReviewModerationService(
        IReviewRepository reviewRepository,
        ILogger<ReviewModerationService> logger)
    {
        _reviewRepository = reviewRepository;
        _logger = logger;
    }

    public async Task<bool> ReportReviewAsync(int reviewId, ReportReviewDto reportDto, string userId)
    {
        try
        {
            var review = await _reviewRepository.GetByIdAsync(reviewId);
            if (review == null)
                return false;

            // For now, just increment report count
            review.ReportCount++;
            await _reviewRepository.UpdateAsync(review);

            _logger.LogInformation("Review {ReviewId} reported by user {UserId}", reviewId, userId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reporting review {ReviewId}", reviewId);
            return false;
        }
    }

    public async Task<bool> ApproveReviewAsync(int reviewId, string moderatorId)
    {
        try
        {
            var review = await _reviewRepository.GetByIdAsync(reviewId);
            if (review == null)
                return false;

            review.IsApproved = true;
            review.Status = ReviewStatus.Published;
            review.UpdatedAt = DateTime.UtcNow;

            await _reviewRepository.UpdateAsync(review);

            _logger.LogInformation("Review {ReviewId} approved by moderator {ModeratorId}", reviewId, moderatorId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error approving review {ReviewId}", reviewId);
            return false;
        }
    }

    public async Task<bool> RejectReviewAsync(int reviewId, string moderatorId, string? reason = null)
    {
        try
        {
            var review = await _reviewRepository.GetByIdAsync(reviewId);
            if (review == null)
                return false;

            review.IsApproved = false;
            review.Status = ReviewStatus.Rejected;
            review.UpdatedAt = DateTime.UtcNow;

            await _reviewRepository.UpdateAsync(review);

            _logger.LogInformation("Review {ReviewId} rejected by moderator {ModeratorId}. Reason: {Reason}", 
                reviewId, moderatorId, reason);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rejecting review {ReviewId}", reviewId);
            return false;
        }
    }

    public async Task<bool> DeleteReviewAsync(int reviewId, string moderatorId)
    {
        try
        {
            await _reviewRepository.DeleteAsync(reviewId);

            _logger.LogInformation("Review {ReviewId} deleted by moderator {ModeratorId}", reviewId, moderatorId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting review {ReviewId}", reviewId);
            return false;
        }
    }

    public async Task<List<ReviewDto>> GetReportedReviewsAsync(int page = 1, int pageSize = 10)
    {
        try
        {
            // This would need to be implemented in the repository
            // For now, return empty list
            return new List<ReviewDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting reported reviews");
            return new List<ReviewDto>();
        }
    }

    public async Task<List<ReviewDto>> GetPendingReviewsAsync(int page = 1, int pageSize = 10)
    {
        try
        {
            // This would need to be implemented in the repository
            // For now, return empty list
            return new List<ReviewDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pending reviews");
            return new List<ReviewDto>();
        }
    }

    public async Task<bool> ModerateReviewAsync(int reviewId)
    {
        try
        {
            var review = await _reviewRepository.GetByIdAsync(reviewId);
            if (review == null)
                return false;

            // Simple auto-moderation logic
            review.Status = ReviewStatus.Published;
            review.IsApproved = true;
            review.UpdatedAt = DateTime.UtcNow;

            await _reviewRepository.UpdateAsync(review);

            _logger.LogInformation("Review {ReviewId} auto-moderated", reviewId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error moderating review {ReviewId}", reviewId);
            return false;
        }
    }

    public async Task<bool> BulkModerateReviewsAsync(List<int> reviewIds, string action, string? reason = null)
    {
        try
        {
            foreach (var reviewId in reviewIds)
            {
                var review = await _reviewRepository.GetByIdAsync(reviewId);
                if (review == null) continue;

                switch (action.ToLower())
                {
                    case "approve":
                        review.IsApproved = true;
                        review.Status = ReviewStatus.Published;
                        break;
                    case "reject":
                        review.IsApproved = false;
                        review.Status = ReviewStatus.Rejected;
                        break;
                    case "delete":
                        await _reviewRepository.DeleteAsync(reviewId);
                        continue;
                }

                review.UpdatedAt = DateTime.UtcNow;
                await _reviewRepository.UpdateAsync(review);
            }

            _logger.LogInformation("Bulk moderated {Count} reviews with action {Action}", reviewIds.Count, action);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error bulk moderating reviews");
            return false;
        }
    }
}
