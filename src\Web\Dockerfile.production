# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copier les fichiers csproj et restaurer les dépendances
COPY ["src/Web/NafaPlace.Web/NafaPlace.Web.csproj", "Web/NafaPlace.Web/"]
COPY ["src/BuildingBlocks/Common/NafaPlace.Common/NafaPlace.Common.csproj", "BuildingBlocks/Common/NafaPlace.Common/"]
COPY ["src/BuildingBlocks/Common/NafaPlace.BuildingBlocks.Common/NafaPlace.BuildingBlocks.Common.csproj", "BuildingBlocks/Common/NafaPlace.BuildingBlocks.Common/"]
RUN dotnet restore "Web/NafaPlace.Web/NafaPlace.Web.csproj"

# Copier le reste des fichiers et construire
COPY ["src/Web/NafaPlace.Web/", "Web/NafaPlace.Web/"]
COPY ["src/Services/Reviews/NafaPlace.Reviews.DTOs/", "Services/Reviews/NafaPlace.Reviews.DTOs/"]
COPY ["src/BuildingBlocks/Common/NafaPlace.Common/", "BuildingBlocks/Common/NafaPlace.Common/"]
COPY ["src/BuildingBlocks/Common/NafaPlace.BuildingBlocks.Common/", "BuildingBlocks/Common/NafaPlace.BuildingBlocks.Common/"]
WORKDIR "/src/Web/NafaPlace.Web"
RUN dotnet build "NafaPlace.Web.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "NafaPlace.Web.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage - using Nginx to serve static files
FROM nginx:alpine AS final
WORKDIR /usr/share/nginx/html
COPY --from=publish /app/publish/wwwroot .

# Use Railway-specific nginx configuration for production
COPY src/Web/nginx.railway.conf /etc/nginx/nginx.conf

# Configuration pour le marché africain
ENV TZ=Africa/Dakar
ENV LANG=fr_FR.UTF-8
ENV LANGUAGE=fr_FR.UTF-8
ENV LC_ALL=fr_FR.UTF-8

# Railway uses PORT environment variable
ENV PORT=8080
EXPOSE $PORT

# Set permissions for existing nginx user and create necessary directories
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    mkdir -p /var/run/nginx && \
    chown -R nginx:nginx /var/run/nginx

# Switch to nginx user
USER nginx

# Start nginx with envsubst to replace $PORT in config
CMD ["sh", "-c", "envsubst '$PORT' < /etc/nginx/nginx.conf > /tmp/nginx.conf && exec nginx -c /tmp/nginx.conf -g 'daemon off;'"]
