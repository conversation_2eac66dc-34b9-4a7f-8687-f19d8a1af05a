# 🧪 GUIDE DE TEST COMPLET - SYSTÈME DE CHAT E-COMMERCE

## ✅ **STATUT DES TÂCHES**

- [x] **Tâche 1** : Implémenter l'authentification JWT complète pour le Chat E-commerce
- [x] **Tâche 2** : <PERSON><PERSON><PERSON> le Hub SignalR pour les conversations en temps réel
- [x] **Tâche 3** : Implémenter la gestion complète des conversations
- [x] **Tâche 4** : Implémenter la gestion des réponses rapides
- [x] **Tâche 5** : Intégrer SignalR dans le Seller Portal
- [/] **Tâche 6** : Tester l'ensemble du système de chat (EN COURS)

---

## 🎯 **TESTS À EFFECTUER**

### **1. Tests de l'API Chat E-commerce (Backend)**

#### **1.1 Health Check**
```powershell
Invoke-WebRequest -Uri "http://localhost:5000/api/chat-ecommerce/health" -Method GET
```
**Résultat attendu** : `Healthy`

#### **1.2 FAQs (Sans authentification)**
```powershell
# Récupérer toutes les FAQs
Invoke-WebRequest -Uri "http://localhost:5000/api/chat-ecommerce/api/support/faqs" -Method GET

# Filtrer par catégorie
Invoke-WebRequest -Uri "http://localhost:5000/api/chat-ecommerce/api/support/faqs?category=Commandes" -Method GET

# Rechercher dans les FAQs
Invoke-WebRequest -Uri "http://localhost:5000/api/chat-ecommerce/api/support/faqs/search?query=livraison" -Method GET
```
**Résultat attendu** : Liste de FAQs en JSON

#### **1.3 Quick Replies (Avec authentification)**

**Étape 1 : Obtenir un token JWT**
```powershell
# Se connecter en tant que vendeur
$loginBody = @{
    username = "seller1"
    password = "Seller123!"
} | ConvertTo-Json

$loginResponse = Invoke-WebRequest -Uri "http://localhost:5000/api/identity/api/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
$token = ($loginResponse.Content | ConvertFrom-Json).token
```

**Étape 2 : Tester les Quick Replies**
```powershell
# Récupérer les réponses rapides
$headers = @{ "Authorization" = "Bearer $token" }
Invoke-WebRequest -Uri "http://localhost:5000/api/chat-ecommerce/api/support/quick-replies" -Method GET -Headers $headers

# Créer une nouvelle réponse rapide
$quickReplyBody = @{
    title = "Délai de livraison standard"
    content = "Nos délais de livraison sont de 2-5 jours ouvrables pour la Guinée."
    category = "Livraison"
    tags = @("livraison", "délai", "standard")
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost:5000/api/chat-ecommerce/api/support/quick-replies" -Method POST -Headers $headers -Body $quickReplyBody -ContentType "application/json"

# Mettre à jour une réponse rapide
$updateBody = @{
    title = "Délai de livraison standard (Mis à jour)"
    content = "Nos délais de livraison sont de 2-5 jours ouvrables pour Conakry, 5-7 jours pour les autres régions."
    category = "Livraison"
    tags = @("livraison", "délai", "standard", "conakry")
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost:5000/api/chat-ecommerce/api/support/quick-replies/1" -Method PUT -Headers $headers -Body $updateBody -ContentType "application/json"

# Supprimer une réponse rapide
Invoke-WebRequest -Uri "http://localhost:5000/api/chat-ecommerce/api/support/quick-replies/1" -Method DELETE -Headers $headers
```

#### **1.4 Conversations (Avec authentification)**
```powershell
# Récupérer les conversations du vendeur
Invoke-WebRequest -Uri "http://localhost:5000/api/chat-ecommerce/api/conversations" -Method GET -Headers $headers

# Créer une nouvelle conversation
$conversationBody = @{
    customerId = "1"
    customerName = "Client Test"
    sellerId = "1"
    sellerName = "Vendeur Test"
    productId = 1
    productName = "Produit Test"
    subject = "Question sur le produit"
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost:5000/api/chat-ecommerce/api/conversations" -Method POST -Headers $headers -Body $conversationBody -ContentType "application/json"

# Récupérer les détails d'une conversation
Invoke-WebRequest -Uri "http://localhost:5000/api/chat-ecommerce/api/conversations/1" -Method GET -Headers $headers

# Envoyer un message dans une conversation
$messageBody = @{
    content = "Bonjour, je suis intéressé par ce produit."
    senderId = "1"
    senderName = "Client Test"
    senderType = "Customer"
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost:5000/api/chat-ecommerce/api/conversations/1/messages" -Method POST -Headers $headers -Body $messageBody -ContentType "application/json"

# Marquer les messages comme lus
Invoke-WebRequest -Uri "http://localhost:5000/api/chat-ecommerce/api/conversations/1/read" -Method POST -Headers $headers

# Mettre à jour le statut de la conversation
$statusBody = @{
    status = "Resolved"
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost:5000/api/chat-ecommerce/api/conversations/1/status" -Method PUT -Headers $headers -Body $statusBody -ContentType "application/json"
```

---

### **2. Tests du Seller Portal (Frontend)**

#### **2.1 Accès à la page Support Client**
1. Ouvrez votre navigateur
2. Allez sur `http://localhost:8082`
3. Connectez-vous avec un compte vendeur :
   - **Username** : `seller1`
   - **Password** : `Seller123!`
4. Cliquez sur **"Support Client"** dans le menu
5. Vous devriez voir la page avec 3 onglets :
   - **Conversations**
   - **FAQs**
   - **Réponses Rapides**

#### **2.2 Test de l'onglet FAQs**
1. Cliquez sur l'onglet **"FAQs"**
2. Vous devriez voir **6 FAQs** affichées
3. Vérifiez que les catégories sont affichées (Commandes, Paiement, Livraison, etc.)
4. Vérifiez que les tags sont affichés
5. **Ouvrez la console du navigateur** (F12) et vérifiez qu'il n'y a **aucune erreur**

#### **2.3 Test de l'onglet Réponses Rapides**
1. Cliquez sur l'onglet **"Réponses Rapides"**
2. Vous devriez voir la liste des réponses rapides (peut être vide au début)
3. Testez la création d'une nouvelle réponse rapide :
   - Cliquez sur **"Nouvelle Réponse Rapide"** (si le bouton existe)
   - Remplissez le formulaire
   - Enregistrez
4. Vérifiez que la nouvelle réponse apparaît dans la liste
5. **Ouvrez la console du navigateur** (F12) et vérifiez qu'il n'y a **aucune erreur**

#### **2.4 Test de l'onglet Conversations**
1. Cliquez sur l'onglet **"Conversations"**
2. Vous devriez voir la liste des conversations (peut être vide au début)
3. Si des conversations existent, cliquez sur une conversation
4. Vérifiez que les messages s'affichent
5. Testez l'envoi d'un nouveau message
6. **Ouvrez la console du navigateur** (F12) et vérifiez qu'il n'y a **aucune erreur**

#### **2.5 Test de SignalR (Temps Réel)**
1. Ouvrez la console du navigateur (F12)
2. Allez sur la page **Support Client**
3. Recherchez dans la console les messages suivants :
   - `"SignalR initialisé avec succès"` ✅
   - `"Connexion SignalR établie"` ✅
   - `"Connecté au hub SignalR avec l'ID: ..."` ✅
4. Si vous voyez ces messages, **SignalR fonctionne correctement** ! 🎉

**Test de réception de messages en temps réel** :
1. Ouvrez **deux onglets** du Seller Portal dans votre navigateur
2. Connectez-vous avec le même compte vendeur dans les deux onglets
3. Dans l'onglet 1, envoyez un message dans une conversation
4. Dans l'onglet 2, vous devriez voir le message apparaître **automatiquement** sans rafraîchir la page
5. Vérifiez dans la console que l'événement `"Nouveau message reçu dans la conversation X"` apparaît

---

### **3. Tests de SignalR Hub (Backend)**

#### **3.1 Vérifier que le Hub est accessible**
```powershell
# Vérifier les logs du conteneur Chat E-commerce API
docker logs nafaplace-chat-ecommerce-api --tail 50
```
**Recherchez** : `"Now listening on: http://[::]:8080"` et `"Application started"`

#### **3.2 Test de connexion SignalR**
Le test de connexion SignalR se fait automatiquement lorsque vous ouvrez la page Support Client dans le Seller Portal.

**Vérification dans les logs** :
```powershell
docker logs nafaplace-chat-ecommerce-api --tail 50 | Select-String "SignalR"
```
**Recherchez** : Messages de connexion/déconnexion d'utilisateurs

---

## 🐛 **DÉPANNAGE**

### **Problème 1 : Erreur 404 sur les FAQs**
**Symptôme** : `GET http://localhost:5000/api/support/faqs net::ERR_ABORTED 404`

**Solution** :
1. Vérifiez que l'API Chat E-commerce est en cours d'exécution :
   ```powershell
   docker ps | Select-String "chat-ecommerce-api"
   ```
2. Vérifiez la configuration dans `appsettings.json` du Seller Portal :
   ```json
   "ChatEcommerceApi": {
     "BaseUrl": "http://localhost:5000/api/chat-ecommerce"
   }
   ```
3. Reconstruisez et redémarrez le Seller Portal :
   ```powershell
   docker compose build seller-portal
   docker compose up -d --force-recreate seller-portal
   ```

### **Problème 2 : SignalR ne se connecte pas**
**Symptôme** : Pas de message `"SignalR initialisé avec succès"` dans la console

**Solution** :
1. Vérifiez que le Hub SignalR est configuré dans `Program.cs` de l'API :
   ```csharp
   app.MapHub<ChatHub>("/chathub");
   ```
2. Vérifiez l'URL de connexion dans `ChatSignalRService.cs` :
   ```csharp
   _hubConnection = new HubConnectionBuilder()
       .WithUrl($"{_baseUrl}/chathub", options => { ... })
       .Build();
   ```
3. Vérifiez les logs du conteneur :
   ```powershell
   docker logs nafaplace-chat-ecommerce-api --tail 50
   ```

### **Problème 3 : Erreur d'authentification JWT**
**Symptôme** : `401 Unauthorized` sur les endpoints protégés

**Solution** :
1. Vérifiez que le token JWT est bien stocké dans localStorage :
   - Ouvrez la console du navigateur (F12)
   - Allez dans l'onglet **Application** > **Local Storage**
   - Vérifiez que la clé `authToken` existe
2. Vérifiez que le token est valide :
   - Copiez le token
   - Allez sur https://jwt.io
   - Collez le token et vérifiez les claims (`sub`, `SellerId`, etc.)
3. Reconnectez-vous si le token a expiré

---

## ✅ **CHECKLIST FINALE**

Avant de considérer le système comme **complètement fonctionnel**, vérifiez que :

- [ ] L'API Chat E-commerce répond `Healthy` au health check
- [ ] Les FAQs sont récupérées sans erreur (6 FAQs)
- [ ] Les Quick Replies peuvent être créées, modifiées et supprimées (avec authentification)
- [ ] Les conversations peuvent être créées et les messages envoyés (avec authentification)
- [ ] La page Support Client s'affiche correctement dans le Seller Portal
- [ ] Les 3 onglets (Conversations, FAQs, Réponses Rapides) fonctionnent
- [ ] SignalR se connecte avec succès (message dans la console)
- [ ] Les messages en temps réel sont reçus (test avec 2 onglets)
- [ ] Aucune erreur dans la console du navigateur
- [ ] Aucune erreur dans les logs Docker

---

## 🚀 **PROCHAINES ÉTAPES (Après validation des tests)**

Une fois tous les tests validés, vous pourrez :

1. **Intégrer le chat dans le Web Portal** (pour les clients)
2. **Ajouter des notifications push** pour les nouveaux messages
3. **Implémenter l'historique des conversations** avec pagination
4. **Ajouter des pièces jointes** aux messages (images, documents)
5. **Créer un tableau de bord** avec statistiques de support
6. **Implémenter des chatbots** pour réponses automatiques

---

## 📝 **NOTES IMPORTANTES**

- **JWT Token** : Le token est stocké dans `localStorage` et a une durée de validité limitée
- **SignalR** : La connexion SignalR est automatiquement rétablie en cas de déconnexion
- **Base de données** : Les données sont stockées dans PostgreSQL (`chat-ecommerce-db`)
- **Ports** :
  - API Chat E-commerce : `http://localhost:5012` (direct) ou `http://localhost:5000/api/chat-ecommerce` (via gateway)
  - Seller Portal : `http://localhost:8082`
  - Admin Portal : `http://localhost:8081`
  - Web Portal : `http://localhost:8080`

---

**Bonne chance pour les tests ! 🎉**

