using Microsoft.AspNetCore.Http;
using NafaPlace.Reviews.DTOs;

namespace NafaPlace.Reviews.Application.Services;

public interface IReviewMediaService
{
    Task<MediaProcessingResult> ProcessReviewMediaAsync(int reviewId, List<IFormFile>? images, List<IFormFile>? videos);
    Task<List<ReviewMediaDto>> GetReviewMediaAsync(int reviewId);
    Task<bool> RemoveReviewMediaAsync(int reviewId, List<int> mediaIds);
    Task<bool> ValidateMediaFileAsync(IFormFile file);
    Task<string> GenerateThumbnailAsync(string videoPath);
    Task<MediaProcessingResult> ProcessImageAsync(IFormFile image, int reviewId);
    Task<MediaProcessingResult> ProcessVideoAsync(IFormFile video, int reviewId);
    Task<bool> DeleteMediaFileAsync(string filePath);
    Task<ReviewMediaDto?> GetMediaByIdAsync(int mediaId);
    Task<bool> IsMediaOwnedByUserAsync(int mediaId, string userId);
}

public class MediaProcessingResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public List<ReviewMediaDto> ProcessedMedia { get; set; } = new();
    public int ProcessedCount { get; set; }
    public int FailedCount { get; set; }
    public List<string> Errors { get; set; } = new();
}