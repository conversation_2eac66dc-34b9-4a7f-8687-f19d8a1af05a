using Microsoft.AspNetCore.Mvc;
using NafaPlace.ChatEcommerce.Domain.Entities;
using NafaPlace.ChatEcommerce.Infrastructure.Data;
using System.Text.Json;

namespace NafaPlace.ChatEcommerce.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class SeedController : ControllerBase
{
    private readonly ChatEcommerceDbContext _context;
    private readonly ILogger<SeedController> _logger;

    public SeedController(ChatEcommerceDbContext context, ILogger<SeedController> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// Créer des données de test pour les conversations
    /// </summary>
    [HttpPost("conversations")]
    public async Task<IActionResult> SeedConversations()
    {
        try
        {
            // Vérifier si des conversations existent déjà
            if (_context.Conversations.Any())
            {
                return Ok(new { message = "Des conversations existent déjà dans la base de données" });
            }

            var conversations = new List<Conversation>
            {
                new Conversation
                {
                    CustomerId = "1",
                    CustomerName = "Amadou Diallo",
                    CustomerEmail = "<EMAIL>",
                    SellerId = "14", // seller1
                    SellerName = "Boutique Naba",
                    ProductId = 1,
                    ProductName = "Smartphone Samsung Galaxy A54",
                    Subject = "Question sur la garantie du produit",
                    Type = ConversationType.ProductInquiry,
                    Status = ConversationStatus.Open,
                    Priority = ConversationPriority.Normal,
                    CreatedAt = DateTime.UtcNow.AddDays(-2),
                    UpdatedAt = DateTime.UtcNow.AddHours(-3),
                    LastMessageAt = DateTime.UtcNow.AddHours(-3),
                    HasUnreadMessages = true,
                    UnreadCount = 2,
                    Tags = JsonSerializer.Serialize(new List<string> { "garantie", "produit" })
                },
                new Conversation
                {
                    CustomerId = "2",
                    CustomerName = "Fatoumata Bah",
                    CustomerEmail = "<EMAIL>",
                    SellerId = "14",
                    SellerName = "Boutique Naba",
                    OrderId = 1,
                    Subject = "Problème avec ma commande",
                    Type = ConversationType.OrderSupport,
                    Status = ConversationStatus.InProgress,
                    Priority = ConversationPriority.High,
                    CreatedAt = DateTime.UtcNow.AddDays(-1),
                    UpdatedAt = DateTime.UtcNow.AddMinutes(-30),
                    LastMessageAt = DateTime.UtcNow.AddMinutes(-30),
                    HasUnreadMessages = true,
                    UnreadCount = 1,
                    Tags = JsonSerializer.Serialize(new List<string> { "commande", "urgent" })
                },
                new Conversation
                {
                    CustomerId = "3",
                    CustomerName = "Mamadou Camara",
                    CustomerEmail = "<EMAIL>",
                    SellerId = "14",
                    SellerName = "Boutique Naba",
                    ProductId = 2,
                    ProductName = "Ordinateur portable HP",
                    Subject = "Demande de remboursement",
                    Type = ConversationType.Refund,
                    Status = ConversationStatus.Waiting,
                    Priority = ConversationPriority.Urgent,
                    CreatedAt = DateTime.UtcNow.AddHours(-12),
                    UpdatedAt = DateTime.UtcNow.AddHours(-2),
                    LastMessageAt = DateTime.UtcNow.AddHours(-2),
                    HasUnreadMessages = false,
                    UnreadCount = 0,
                    Tags = JsonSerializer.Serialize(new List<string> { "remboursement", "retour" })
                },
                new Conversation
                {
                    CustomerId = "4",
                    CustomerName = "Aissatou Sow",
                    CustomerEmail = "<EMAIL>",
                    SellerId = "14",
                    SellerName = "Boutique Naba",
                    ProductId = 3,
                    ProductName = "Télévision Samsung 55 pouces",
                    Subject = "Question avant achat",
                    Type = ConversationType.PreSale,
                    Status = ConversationStatus.Open,
                    Priority = ConversationPriority.Low,
                    CreatedAt = DateTime.UtcNow.AddHours(-6),
                    UpdatedAt = DateTime.UtcNow.AddHours(-1),
                    LastMessageAt = DateTime.UtcNow.AddHours(-1),
                    HasUnreadMessages = true,
                    UnreadCount = 1,
                    Tags = JsonSerializer.Serialize(new List<string> { "pré-vente", "information" })
                },
                new Conversation
                {
                    CustomerId = "5",
                    CustomerName = "Ibrahima Diop",
                    CustomerEmail = "<EMAIL>",
                    SellerId = "14",
                    SellerName = "Boutique Naba",
                    OrderId = 2,
                    Subject = "Livraison retardée",
                    Type = ConversationType.OrderSupport,
                    Status = ConversationStatus.Resolved,
                    Priority = ConversationPriority.Normal,
                    CreatedAt = DateTime.UtcNow.AddDays(-5),
                    UpdatedAt = DateTime.UtcNow.AddDays(-4),
                    LastMessageAt = DateTime.UtcNow.AddDays(-4),
                    ClosedAt = DateTime.UtcNow.AddDays(-4),
                    HasUnreadMessages = false,
                    UnreadCount = 0,
                    Tags = JsonSerializer.Serialize(new List<string> { "livraison", "résolu" })
                },
                new Conversation
                {
                    CustomerId = "6",
                    CustomerName = "Mariama Baldé",
                    CustomerEmail = "<EMAIL>",
                    SellerId = "14",
                    SellerName = "Boutique Naba",
                    ProductId = 4,
                    ProductName = "Réfrigérateur LG",
                    Subject = "Produit défectueux",
                    Type = ConversationType.Complaint,
                    Status = ConversationStatus.Escalated,
                    Priority = ConversationPriority.Critical,
                    CreatedAt = DateTime.UtcNow.AddHours(-8),
                    UpdatedAt = DateTime.UtcNow.AddMinutes(-15),
                    LastMessageAt = DateTime.UtcNow.AddMinutes(-15),
                    HasUnreadMessages = true,
                    UnreadCount = 3,
                    Tags = JsonSerializer.Serialize(new List<string> { "défectueux", "urgent", "escaladé" })
                }
            };

            _context.Conversations.AddRange(conversations);
            await _context.SaveChangesAsync();

            // Ajouter quelques messages pour chaque conversation
            var messages = new List<Message>();
            foreach (var conversation in conversations)
            {
                // Message du client
                messages.Add(new Message
                {
                    ConversationId = conversation.Id,
                    SenderId = conversation.CustomerId,
                    SenderName = conversation.CustomerName,
                    SenderType = SenderType.Customer,
                    Content = $"Bonjour, {conversation.Subject}",
                    MessageType = MessageType.Text,
                    Timestamp = conversation.CreatedAt,
                    IsRead = true
                });

                // Réponse du vendeur (sauf pour les conversations non résolues)
                if (conversation.Status != ConversationStatus.Open && conversation.Status != ConversationStatus.Waiting)
                {
                    messages.Add(new Message
                    {
                        ConversationId = conversation.Id,
                        SenderId = conversation.SellerId ?? "",
                        SenderName = conversation.SellerName ?? "",
                        SenderType = SenderType.Seller,
                        Content = "Bonjour, merci pour votre message. Je vais regarder votre demande et vous répondre rapidement.",
                        MessageType = MessageType.Text,
                        Timestamp = conversation.CreatedAt.AddMinutes(30),
                        IsRead = conversation.Status == ConversationStatus.Resolved
                    });
                }

                // Message supplémentaire pour les conversations avec messages non lus
                if (conversation.HasUnreadMessages)
                {
                    messages.Add(new Message
                    {
                        ConversationId = conversation.Id,
                        SenderId = conversation.CustomerId,
                        SenderName = conversation.CustomerName,
                        SenderType = SenderType.Customer,
                        Content = "Avez-vous des nouvelles concernant ma demande ?",
                        MessageType = MessageType.Text,
                        Timestamp = conversation.LastMessageAt ?? conversation.UpdatedAt,
                        IsRead = false
                    });
                }
            }

            _context.Messages.AddRange(messages);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Données de test créées avec succès : {Count} conversations et {MessageCount} messages", 
                conversations.Count, messages.Count);

            return Ok(new 
            { 
                message = "Données de test créées avec succès",
                conversationsCount = conversations.Count,
                messagesCount = messages.Count
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création des données de test");
            return StatusCode(500, new { error = "Erreur lors de la création des données de test", details = ex.Message });
        }
    }

    /// <summary>
    /// Supprimer toutes les conversations de test
    /// </summary>
    [HttpDelete("conversations")]
    public async Task<IActionResult> ClearConversations()
    {
        try
        {
            var messages = _context.Messages.ToList();
            var conversations = _context.Conversations.ToList();

            _context.Messages.RemoveRange(messages);
            _context.Conversations.RemoveRange(conversations);
            
            await _context.SaveChangesAsync();

            _logger.LogInformation("Toutes les conversations et messages ont été supprimés");

            return Ok(new { message = "Toutes les conversations et messages ont été supprimés" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression des données");
            return StatusCode(500, new { error = "Erreur lors de la suppression des données", details = ex.Message });
        }
    }
}

