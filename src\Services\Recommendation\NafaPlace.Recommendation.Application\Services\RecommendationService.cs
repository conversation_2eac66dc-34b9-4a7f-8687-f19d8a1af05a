using Microsoft.Extensions.Logging;
using NafaPlace.Recommendation.Application.DTOs;
using NafaPlace.Recommendation.Application.Interfaces;

namespace NafaPlace.Recommendation.Application.Services;

public class RecommendationService : IRecommendationService
{
    private readonly IRecommendationRepository _repository;
    private readonly ICollaborativeFilteringService _collaborativeService;
    private readonly IContentBasedService _contentService;
    private readonly IMLModelService _mlService;
    private readonly ILogger<RecommendationService> _logger;

    public RecommendationService(
        IRecommendationRepository repository,
        ICollaborativeFilteringService collaborativeService,
        IContentBasedService contentService,
        IMLModelService mlService,
        ILogger<RecommendationService> logger)
    {
        _repository = repository;
        _collaborativeService = collaborativeService;
        _contentService = contentService;
        _mlService = mlService;
        _logger = logger;
    }

    public async Task<RecommendationResponseDto> GetPersonalizedRecommendationsAsync(RecommendationRequestDto request)
    {
        try
        {
            _logger.LogInformation("Génération de recommandations personnalisées pour {UserId}", request.UserId);

            var startTime = DateTime.UtcNow;
            var requestId = Guid.NewGuid().ToString();

            // Obtenir le profil utilisateur
            var userProfile = await GetPersonalizationProfileAsync(request.UserId);
            
            // Sélectionner l'algorithme optimal
            var algorithm = await SelectOptimalAlgorithmAsync(request.UserId, userProfile);
            
            var recommendations = new List<ProductRecommendationDto>();

            // Générer les recommandations selon l'algorithme sélectionné
            switch (algorithm)
            {
                case "collaborative_filtering":
                    recommendations = await GenerateCollaborativeRecommendationsAsync(request);
                    break;
                case "content_based":
                    recommendations = await GenerateContentBasedRecommendationsAsync(request);
                    break;
                case "hybrid":
                    recommendations = await GenerateHybridRecommendationsAsync(request);
                    break;
                default:
                    recommendations = await GenerateDefaultRecommendationsAsync(request);
                    break;
            }

            // Appliquer les filtres
            recommendations = await ApplyFiltersAsync(recommendations, request);

            // Diversifier les résultats
            recommendations = await DiversifyRecommendationsAsync(recommendations, 0.3);

            // Calculer les scores de confiance
            foreach (var rec in recommendations)
            {
                rec.RecommendationScore = await CalculateRecommendationConfidenceAsync(request.UserId, rec.ProductId);
            }

            // Trier par score
            recommendations = recommendations.OrderByDescending(r => r.RecommendationScore).Take(request.Limit).ToList();

            var processingTime = DateTime.UtcNow - startTime;

            // Logger la recommandation
            await LogRecommendationRequestAsync(requestId, request, recommendations.Count, algorithm);

            _logger.LogInformation("Recommandations générées pour {UserId}: {Count} produits en {Time}ms", 
                request.UserId, recommendations.Count, processingTime.TotalMilliseconds);

            return new RecommendationResponseDto
            {
                Products = recommendations,
                Type = request.Type,
                Algorithm = algorithm,
                Confidence = recommendations.Any() ? recommendations.Average(r => r.RecommendationScore) : 0,
                TotalCount = recommendations.Count,
                RequestId = requestId,
                GeneratedAt = DateTime.UtcNow,
                ProcessingTime = processingTime
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la génération de recommandations personnalisées");
            return new RecommendationResponseDto
            {
                Type = request.Type,
                Algorithm = "fallback",
                GeneratedAt = DateTime.UtcNow
            };
        }
    }

    public async Task<RecommendationResponseDto> GetSimilarProductsAsync(int productId, string? userId = null, int limit = 10)
    {
        try
        {
            _logger.LogInformation("Recherche de produits similaires à {ProductId}", productId);

            var startTime = DateTime.UtcNow;

            // Obtenir les caractéristiques du produit
            var productFeatures = await GetProductFeatureVectorAsync(productId);
            
            // Recherche par similarité de contenu
            var contentSimilar = await _contentService.GetSimilarProductsAsync(productId, limit * 2);
            
            // Recherche par filtrage collaboratif si utilisateur connecté
            var collaborativeSimilar = new List<SimilarProductDto>();
            if (!string.IsNullOrEmpty(userId))
            {
                collaborativeSimilar = await _collaborativeService.GetSimilarProductsAsync(productId, userId, limit);
            }

            // Combiner et scorer les résultats
            var allSimilar = new List<SimilarProductDto>();
            allSimilar.AddRange(contentSimilar);
            allSimilar.AddRange(collaborativeSimilar);

            // Éliminer les doublons et calculer le score final
            var uniqueProducts = allSimilar
                .GroupBy(p => p.ProductId)
                .Select(g => new SimilarProductDto
                {
                    ProductId = g.Key,
                    ProductName = g.First().ProductName,
                    SimilarityScore = g.Average(p => p.SimilarityScore),
                    SimilarityType = SimilarityType.Hybrid,
                    CommonFeatures = g.SelectMany(p => p.CommonFeatures).Distinct().ToList()
                })
                .OrderByDescending(p => p.SimilarityScore)
                .Take(limit)
                .ToList();

            // Convertir en ProductRecommendationDto
            var recommendations = new List<ProductRecommendationDto>();
            foreach (var similar in uniqueProducts)
            {
                var product = await GetProductDetailsAsync(similar.ProductId);
                if (product != null)
                {
                    product.RecommendationScore = similar.SimilarityScore;
                    product.RecommendationType = RecommendationType.SimilarProducts;
                    product.RecommendationReason = $"Similaire à {await GetProductNameAsync(productId)}";
                    recommendations.Add(product);
                }
            }

            var processingTime = DateTime.UtcNow - startTime;

            _logger.LogInformation("Produits similaires trouvés pour {ProductId}: {Count} produits", 
                productId, recommendations.Count);

            return new RecommendationResponseDto
            {
                Products = recommendations,
                Type = RecommendationType.SimilarProducts,
                Algorithm = "content_collaborative_hybrid",
                Confidence = recommendations.Any() ? recommendations.Average(r => r.RecommendationScore) : 0,
                TotalCount = recommendations.Count,
                RequestId = Guid.NewGuid().ToString(),
                GeneratedAt = DateTime.UtcNow,
                ProcessingTime = processingTime
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche de produits similaires");
            return new RecommendationResponseDto
            {
                Type = RecommendationType.SimilarProducts,
                Algorithm = "fallback"
            };
        }
    }

    public async Task<RecommendationResponseDto> GetFrequentlyBoughtTogetherAsync(int productId, string? userId = null, int limit = 5)
    {
        try
        {
            _logger.LogInformation("Recherche de produits fréquemment achetés ensemble avec {ProductId}", productId);

            // Analyser les paniers d'achat historiques
            var frequentItems = await _repository.GetFrequentlyBoughtTogetherAsync(productId, limit * 2);
            
            // Appliquer le filtrage collaboratif si utilisateur connecté
            if (!string.IsNullOrEmpty(userId))
            {
                var userPreferences = await GetUserPreferencesAsync(userId);
                frequentItems = await FilterByUserPreferencesAsync(frequentItems, userPreferences);
            }

            // Convertir en recommandations
            var recommendations = new List<ProductRecommendationDto>();
            foreach (var item in frequentItems.Take(limit))
            {
                var product = await GetProductDetailsAsync(item.ProductId);
                if (product != null)
                {
                    product.RecommendationType = RecommendationType.FrequentlyBoughtTogether;
                    product.RecommendationScore = item.Confidence;
                    product.RecommendationReason = $"Souvent acheté avec {await GetProductNameAsync(productId)}";
                    recommendations.Add(product);
                }
            }

            return new RecommendationResponseDto
            {
                Products = recommendations,
                Type = RecommendationType.FrequentlyBoughtTogether,
                Algorithm = "market_basket_analysis",
                Confidence = recommendations.Any() ? recommendations.Average(r => r.RecommendationScore) : 0,
                TotalCount = recommendations.Count,
                RequestId = Guid.NewGuid().ToString(),
                GeneratedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche de produits fréquemment achetés ensemble");
            return new RecommendationResponseDto
            {
                Type = RecommendationType.FrequentlyBoughtTogether,
                Algorithm = "fallback"
            };
        }
    }

    public async Task<bool> RecordUserInteractionAsync(UserInteractionDto interaction)
    {
        try
        {
            _logger.LogInformation("Enregistrement d'interaction utilisateur: {UserId} -> {ProductId} ({Type})", 
                interaction.UserId, interaction.ProductId, interaction.Type);

            // Enregistrer l'interaction
            var success = await _repository.RecordInteractionAsync(interaction);

            if (success)
            {
                // Mettre à jour les préférences utilisateur en temps réel
                await UpdateUserPreferencesRealTimeAsync(interaction);

                // Mettre à jour les modèles de recommandation
                await UpdateRecommendationModelsAsync(interaction);

                // Invalider le cache des recommandations pour cet utilisateur
                await InvalidateUserRecommendationCacheAsync(interaction.UserId);
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'enregistrement de l'interaction utilisateur");
            return false;
        }
    }

    public async Task<UserPreferenceDto> GetUserPreferencesAsync(string userId)
    {
        try
        {
            var preferences = await _repository.GetUserPreferencesAsync(userId);
            
            if (preferences == null)
            {
                // Créer des préférences par défaut pour un nouvel utilisateur
                preferences = await CreateDefaultUserPreferencesAsync(userId);
            }

            return preferences;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des préférences utilisateur {UserId}", userId);
            return new UserPreferenceDto { UserId = userId };
        }
    }

    public async Task<PersonalizationProfileDto> GetPersonalizationProfileAsync(string userId)
    {
        try
        {
            var profile = await _repository.GetPersonalizationProfileAsync(userId);
            
            if (profile == null)
            {
                // Créer un profil par défaut
                profile = await CreateDefaultPersonalizationProfileAsync(userId);
            }

            return profile;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération du profil de personnalisation {UserId}", userId);
            return new PersonalizationProfileDto { UserId = userId, Segment = UserSegment.NewUser };
        }
    }

    // Méthodes d'aide privées
    private async Task<string> SelectOptimalAlgorithmAsync(string userId, PersonalizationProfileDto profile)
    {
        // Sélectionner l'algorithme optimal basé sur le profil utilisateur
        if (profile.Segment == UserSegment.NewUser)
        {
            return "content_based"; // Pour les nouveaux utilisateurs, utiliser le contenu
        }
        
        if (profile.ProfileCompleteness > 0.7)
        {
            return "hybrid"; // Pour les profils complets, utiliser l'hybride
        }
        
        return "collaborative_filtering"; // Par défaut, filtrage collaboratif
    }

    private async Task<List<ProductRecommendationDto>> GenerateCollaborativeRecommendationsAsync(RecommendationRequestDto request)
    {
        var recommendations = await _collaborativeService.GetRecommendationsAsync(request.UserId, request.Limit * 2);
        return await ConvertToProductRecommendationsAsync(recommendations, RecommendationType.PersonalizedForYou);
    }

    private async Task<List<ProductRecommendationDto>> GenerateContentBasedRecommendationsAsync(RecommendationRequestDto request)
    {
        var recommendations = await _contentService.GetRecommendationsAsync(request.UserId, request.Limit * 2);
        return await ConvertToProductRecommendationsAsync(recommendations, RecommendationType.PersonalizedForYou);
    }

    private async Task<List<ProductRecommendationDto>> GenerateHybridRecommendationsAsync(RecommendationRequestDto request)
    {
        // Combiner filtrage collaboratif et basé sur le contenu
        var collaborativeRecs = await GenerateCollaborativeRecommendationsAsync(request);
        var contentRecs = await GenerateContentBasedRecommendationsAsync(request);

        // Fusionner avec des poids
        var hybridRecs = new List<ProductRecommendationDto>();
        
        // 60% collaboratif, 40% contenu
        hybridRecs.AddRange(collaborativeRecs.Take((int)(request.Limit * 0.6)));
        hybridRecs.AddRange(contentRecs.Take((int)(request.Limit * 0.4)));

        return hybridRecs.DistinctBy(r => r.ProductId).Take(request.Limit).ToList();
    }

    private async Task<List<ProductRecommendationDto>> GenerateDefaultRecommendationsAsync(RecommendationRequestDto request)
    {
        // Recommandations par défaut : produits populaires + nouveautés
        var popular = await GetBestSellersAsync(request.CategoryId, request.Limit / 2);
        var newArrivals = await GetNewArrivalsAsync(request.CategoryId, request.Limit / 2);

        var recommendations = new List<ProductRecommendationDto>();
        recommendations.AddRange(popular.Products);
        recommendations.AddRange(newArrivals.Products);

        return recommendations.Take(request.Limit).ToList();
    }

    private async Task<List<ProductRecommendationDto>> ApplyFiltersAsync(List<ProductRecommendationDto> recommendations, RecommendationRequestDto request)
    {
        var filtered = recommendations.AsEnumerable();

        // Filtrer par prix
        if (request.MinPrice.HasValue)
            filtered = filtered.Where(r => r.Price >= request.MinPrice.Value);
        
        if (request.MaxPrice.HasValue)
            filtered = filtered.Where(r => r.Price <= request.MaxPrice.Value);

        // Filtrer par stock
        if (!request.IncludeOutOfStock)
            filtered = filtered.Where(r => r.IsInStock);

        // Exclure certains produits
        if (request.ExcludeProductIds?.Any() == true)
            filtered = filtered.Where(r => !request.ExcludeProductIds.Contains(r.ProductId));

        return filtered.ToList();
    }

    private async Task<List<ProductRecommendationDto>> DiversifyRecommendationsAsync(List<ProductRecommendationDto> recommendations, double diversityWeight)
    {
        // Algorithme simple de diversification
        var diversified = new List<ProductRecommendationDto>();
        var usedCategories = new HashSet<string>();

        foreach (var rec in recommendations.OrderByDescending(r => r.RecommendationScore))
        {
            // Favoriser la diversité des catégories
            if (!usedCategories.Contains(rec.Category) || usedCategories.Count < 3)
            {
                diversified.Add(rec);
                usedCategories.Add(rec.Category);
            }
            else if (diversified.Count < recommendations.Count * (1 - diversityWeight))
            {
                diversified.Add(rec);
            }
        }

        return diversified;
    }

    private async Task UpdateUserPreferencesRealTimeAsync(UserInteractionDto interaction)
    {
        // Mise à jour en temps réel des préférences basée sur l'interaction
        var preferences = await GetUserPreferencesAsync(interaction.UserId);
        
        // Mettre à jour les préférences de catégorie
        var product = await GetProductDetailsAsync(interaction.ProductId);
        if (product != null)
        {
            var categoryPref = preferences.CategoryPreferences.FirstOrDefault(c => c.CategoryName == product.Category);
            if (categoryPref != null)
            {
                categoryPref.Score += (double)interaction.Type * 0.1;
                categoryPref.InteractionCount++;
                categoryPref.LastInteraction = interaction.Timestamp;
            }
            else
            {
                preferences.CategoryPreferences.Add(new CategoryPreferenceDto
                {
                    CategoryName = product.Category,
                    Score = (double)interaction.Type * 0.1,
                    InteractionCount = 1,
                    LastInteraction = interaction.Timestamp
                });
            }
        }

        await _repository.UpdateUserPreferencesAsync(preferences);
    }

    private async Task UpdateRecommendationModelsAsync(UserInteractionDto interaction)
    {
        // Mettre à jour les modèles de ML avec la nouvelle interaction
        await _mlService.UpdateModelsWithInteractionAsync(interaction);
    }

    private async Task InvalidateUserRecommendationCacheAsync(string userId)
    {
        // Invalider le cache des recommandations pour forcer une régénération
        await _repository.InvalidateUserCacheAsync(userId);
    }

    private async Task<UserPreferenceDto> CreateDefaultUserPreferencesAsync(string userId)
    {
        var preferences = new UserPreferenceDto
        {
            UserId = userId,
            LastUpdated = DateTime.UtcNow,
            InteractionCount = 0
        };

        await _repository.CreateUserPreferencesAsync(preferences);
        return preferences;
    }

    private async Task<PersonalizationProfileDto> CreateDefaultPersonalizationProfileAsync(string userId)
    {
        var profile = new PersonalizationProfileDto
        {
            UserId = userId,
            Segment = UserSegment.NewUser,
            LastUpdated = DateTime.UtcNow,
            ProfileCompleteness = 0.1
        };

        await _repository.CreatePersonalizationProfileAsync(profile);
        return profile;
    }

    private async Task<List<ProductRecommendationDto>> ConvertToProductRecommendationsAsync(List<SimilarProductDto> similar, RecommendationType type)
    {
        var recommendations = new List<ProductRecommendationDto>();
        
        foreach (var item in similar)
        {
            var product = await GetProductDetailsAsync(item.ProductId);
            if (product != null)
            {
                product.RecommendationType = type;
                product.RecommendationScore = item.SimilarityScore;
                recommendations.Add(product);
            }
        }

        return recommendations;
    }

    private async Task<ProductRecommendationDto?> GetProductDetailsAsync(int productId)
    {
        // Récupérer les détails du produit depuis le service Catalog
        return await _repository.GetProductDetailsAsync(productId);
    }

    private async Task<string> GetProductNameAsync(int productId)
    {
        var product = await GetProductDetailsAsync(productId);
        return product?.ProductName ?? $"Produit {productId}";
    }

    private async Task LogRecommendationRequestAsync(string requestId, RecommendationRequestDto request, int resultCount, string algorithm)
    {
        // Logger la demande de recommandation pour analytics
        await _repository.LogRecommendationRequestAsync(requestId, request, resultCount, algorithm);
    }

    // Implémentation des méthodes de recommandations de base
    public async Task<RecommendationResponseDto> GetTrendingProductsAsync(string? userId = null, int limit = 10)
    {
        try
        {
            _logger.LogInformation("Récupération des produits tendance, limite: {Limit}", limit);

            var startTime = DateTime.UtcNow;
            var requestId = Guid.NewGuid().ToString();

            // Simuler la récupération des produits tendance basée sur les interactions récentes
            var trendingProducts = await GetTrendingProductsFromInteractionsAsync(limit);

            var response = new RecommendationResponseDto
            {
                RequestId = requestId,
                UserId = userId,
                Algorithm = "trending_analysis",
                Products = trendingProducts,
                TotalCount = trendingProducts.Count,
                ProcessingTimeMs = (int)(DateTime.UtcNow - startTime).TotalMilliseconds,
                Metadata = new Dictionary<string, object>
                {
                    ["algorithm"] = "trending_analysis",
                    ["time_window"] = "7_days",
                    ["interaction_types"] = new[] { "view", "purchase", "add_to_cart" }
                }
            };

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des produits tendance");
            return new RecommendationResponseDto
            {
                RequestId = Guid.NewGuid().ToString(),
                UserId = userId,
                Algorithm = "trending_analysis",
                Products = new List<ProductRecommendationDto>(),
                TotalCount = 0,
                ProcessingTimeMs = 0,
                Error = ex.Message
            };
        }
    }

    public async Task<RecommendationResponseDto> GetBestSellersAsync(int? categoryId = null, int limit = 10)
    {
        try
        {
            _logger.LogInformation("Récupération des meilleures ventes, catégorie: {CategoryId}, limite: {Limit}", categoryId, limit);

            var startTime = DateTime.UtcNow;
            var requestId = Guid.NewGuid().ToString();

            // Récupérer les meilleures ventes basées sur les achats
            var bestSellers = await GetBestSellersFromPurchasesAsync(categoryId, limit);

            var response = new RecommendationResponseDto
            {
                RequestId = requestId,
                Algorithm = "best_sellers",
                Products = bestSellers,
                TotalCount = bestSellers.Count,
                ProcessingTimeMs = (int)(DateTime.UtcNow - startTime).TotalMilliseconds,
                Metadata = new Dictionary<string, object>
                {
                    ["algorithm"] = "best_sellers",
                    ["category_id"] = categoryId?.ToString() ?? "all",
                    ["time_window"] = "30_days"
                }
            };

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des meilleures ventes");
            return new RecommendationResponseDto
            {
                RequestId = Guid.NewGuid().ToString(),
                Algorithm = "best_sellers",
                Products = new List<ProductRecommendationDto>(),
                TotalCount = 0,
                ProcessingTimeMs = 0,
                Error = ex.Message
            };
        }
    }

    public async Task<RecommendationResponseDto> GetNewArrivalsAsync(int? categoryId = null, int limit = 10)
    {
        try
        {
            _logger.LogInformation("Récupération des nouveaux arrivages, catégorie: {CategoryId}, limite: {Limit}", categoryId, limit);

            var startTime = DateTime.UtcNow;
            var requestId = Guid.NewGuid().ToString();

            // Récupérer les nouveaux produits
            var newArrivals = await GetNewArrivalsFromCatalogAsync(categoryId, limit);

            var response = new RecommendationResponseDto
            {
                RequestId = requestId,
                Algorithm = "new_arrivals",
                Products = newArrivals,
                TotalCount = newArrivals.Count,
                ProcessingTimeMs = (int)(DateTime.UtcNow - startTime).TotalMilliseconds,
                Metadata = new Dictionary<string, object>
                {
                    ["algorithm"] = "new_arrivals",
                    ["category_id"] = categoryId?.ToString() ?? "all",
                    ["time_window"] = "14_days"
                }
            };

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des nouveaux arrivages");
            return new RecommendationResponseDto
            {
                RequestId = Guid.NewGuid().ToString(),
                Algorithm = "new_arrivals",
                Products = new List<ProductRecommendationDto>(),
                TotalCount = 0,
                ProcessingTimeMs = 0,
                Error = ex.Message
            };
        }
    }

    public async Task<RecommendationResponseDto> GetRecentlyViewedAsync(string userId, int limit = 10)
    {
        try
        {
            _logger.LogInformation("Récupération des produits récemment vus pour {UserId}, limite: {Limit}", userId, limit);

            var startTime = DateTime.UtcNow;
            var requestId = Guid.NewGuid().ToString();

            // Récupérer les produits récemment vus par l'utilisateur
            var recentlyViewed = await GetRecentlyViewedFromInteractionsAsync(userId, limit);

            var response = new RecommendationResponseDto
            {
                RequestId = requestId,
                UserId = userId,
                Algorithm = "recently_viewed",
                Products = recentlyViewed,
                TotalCount = recentlyViewed.Count,
                ProcessingTimeMs = (int)(DateTime.UtcNow - startTime).TotalMilliseconds,
                Metadata = new Dictionary<string, object>
                {
                    ["algorithm"] = "recently_viewed",
                    ["user_id"] = userId,
                    ["time_window"] = "30_days"
                }
            };

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des produits récemment vus pour {UserId}", userId);
            return new RecommendationResponseDto
            {
                RequestId = Guid.NewGuid().ToString(),
                UserId = userId,
                Algorithm = "recently_viewed",
                Products = new List<ProductRecommendationDto>(),
                TotalCount = 0,
                ProcessingTimeMs = 0,
                Error = ex.Message
            };
        }
    }
    public Task<RecommendationResponseDto> GetCategoryBasedRecommendationsAsync(int categoryId, string? userId = null, int limit = 10) => throw new NotImplementedException();
    public Task<RecommendationResponseDto> GetBrandBasedRecommendationsAsync(string brand, string? userId = null, int limit = 10) => throw new NotImplementedException();
    public Task<RecommendationResponseDto> GetSeasonalRecommendationsAsync(string? userId = null, int limit = 10) => throw new NotImplementedException();
    public Task<RecommendationResponseDto> GetPriceDropRecommendationsAsync(string? userId = null, int limit = 10) => throw new NotImplementedException();
    public Task<RecommendationResponseDto> GetBackInStockRecommendationsAsync(string userId, int limit = 10) => throw new NotImplementedException();
    public Task<RecommendationResponseDto> GetAbandonedCartRecommendationsAsync(string userId, int limit = 5) => throw new NotImplementedException();
    public Task<RecommendationResponseDto> GetWishlistBasedRecommendationsAsync(string userId, int limit = 10) => throw new NotImplementedException();
    public Task<RecommendationResponseDto> GetCrossSellRecommendationsAsync(List<int> productIds, string? userId = null, int limit = 5) => throw new NotImplementedException();
    public async Task<RecommendationResponseDto> GetUpSellRecommendationsAsync(int productId, string? userId = null, int limit = 3)
    {
        try
        {
            _logger.LogInformation("Génération de recommandations up-sell pour le produit {ProductId}", productId);

            var startTime = DateTime.UtcNow;
            var requestId = Guid.NewGuid().ToString();

            // Récupérer les produits de gamme supérieure
            var upSellProducts = await GetUpSellProductsAsync(productId, limit);

            var response = new RecommendationResponseDto
            {
                RequestId = requestId,
                UserId = userId,
                Algorithm = "upsell",
                Products = upSellProducts,
                TotalCount = upSellProducts.Count,
                ProcessingTimeMs = (int)(DateTime.UtcNow - startTime).TotalMilliseconds,
                Metadata = new Dictionary<string, object>
                {
                    ["algorithm"] = "upsell",
                    ["base_product_id"] = productId,
                    ["strategy"] = "higher_tier_products"
                }
            };

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la génération des recommandations up-sell pour {ProductId}", productId);
            return new RecommendationResponseDto
            {
                RequestId = Guid.NewGuid().ToString(),
                UserId = userId,
                Algorithm = "upsell",
                Products = new List<ProductRecommendationDto>(),
                TotalCount = 0,
                ProcessingTimeMs = 0,
                Error = ex.Message
            };
        }
    }

    public async Task<RecommendationResponseDto> GetComplementaryProductsAsync(int productId, string? userId = null, int limit = 5)
    {
        try
        {
            _logger.LogInformation("Génération de recommandations de produits complémentaires pour {ProductId}", productId);

            var startTime = DateTime.UtcNow;
            var requestId = Guid.NewGuid().ToString();

            // Récupérer les produits complémentaires
            var complementaryProducts = await GetComplementaryProductsAsync(productId, limit);

            var response = new RecommendationResponseDto
            {
                RequestId = requestId,
                UserId = userId,
                Algorithm = "complementary",
                Products = complementaryProducts,
                TotalCount = complementaryProducts.Count,
                ProcessingTimeMs = (int)(DateTime.UtcNow - startTime).TotalMilliseconds,
                Metadata = new Dictionary<string, object>
                {
                    ["algorithm"] = "complementary",
                    ["base_product_id"] = productId,
                    ["strategy"] = "frequently_bought_together"
                }
            };

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la génération des produits complémentaires pour {ProductId}", productId);
            return new RecommendationResponseDto
            {
                RequestId = Guid.NewGuid().ToString(),
                UserId = userId,
                Algorithm = "complementary",
                Products = new List<ProductRecommendationDto>(),
                TotalCount = 0,
                ProcessingTimeMs = 0,
                Error = ex.Message
            };
        }
    }

    public async Task<RecommendationResponseDto> GetAlternativeProductsAsync(int productId, string? userId = null, int limit = 5)
    {
        try
        {
            _logger.LogInformation("Génération de recommandations d'alternatives pour {ProductId}", productId);

            var startTime = DateTime.UtcNow;
            var requestId = Guid.NewGuid().ToString();

            // Récupérer les produits alternatifs
            var alternativeProducts = await GetAlternativeProductsAsync(productId, limit);

            var response = new RecommendationResponseDto
            {
                RequestId = requestId,
                UserId = userId,
                Algorithm = "alternatives",
                Products = alternativeProducts,
                TotalCount = alternativeProducts.Count,
                ProcessingTimeMs = (int)(DateTime.UtcNow - startTime).TotalMilliseconds,
                Metadata = new Dictionary<string, object>
                {
                    ["algorithm"] = "alternatives",
                    ["base_product_id"] = productId,
                    ["strategy"] = "similar_products_different_brands"
                }
            };

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la génération des alternatives pour {ProductId}", productId);
            return new RecommendationResponseDto
            {
                RequestId = Guid.NewGuid().ToString(),
                UserId = userId,
                Algorithm = "alternatives",
                Products = new List<ProductRecommendationDto>(),
                TotalCount = 0,
                ProcessingTimeMs = 0,
                Error = ex.Message
            };
        }
    }
    public Task<bool> RecordBulkInteractionsAsync(List<UserInteractionDto> interactions) => throw new NotImplementedException();
    public Task<List<UserInteractionDto>> GetUserInteractionsAsync(string userId, InteractionType? type = null, int limit = 100) => throw new NotImplementedException();
    public Task<bool> UpdateInteractionWeightAsync(int interactionId, double newWeight) => throw new NotImplementedException();
    public Task<int> CleanupOldInteractionsAsync(int daysToKeep = 90) => throw new NotImplementedException();
    public Task<bool> UpdateUserPreferencesAsync(UserPreferenceDto preferences) => throw new NotImplementedException();
    public Task<bool> RecalculateUserPreferencesAsync(string userId) => throw new NotImplementedException();
    public Task<List<SimilarProductDto>> GetSimilarUsersAsync(string userId, int limit = 10) => throw new NotImplementedException();
    public Task<bool> UpdatePersonalizationProfileAsync(PersonalizationProfileDto profile) => throw new NotImplementedException();
    public Task<int> CreateRecommendationModelAsync(RecommendationModelDto model) => throw new NotImplementedException();
    public Task<List<RecommendationModelDto>> GetRecommendationModelsAsync() => throw new NotImplementedException();
    public Task<RecommendationModelDto?> GetRecommendationModelAsync(int modelId) => throw new NotImplementedException();
    public Task<bool> UpdateRecommendationModelAsync(RecommendationModelDto model) => throw new NotImplementedException();
    public Task<bool> DeleteRecommendationModelAsync(int modelId) => throw new NotImplementedException();
    public Task<bool> TrainModelAsync(int modelId, Dictionary<string, object>? parameters = null) => throw new NotImplementedException();
    public Task<bool> DeployModelAsync(int modelId) => throw new NotImplementedException();
    public Task<Dictionary<string, object>> EvaluateModelAsync(int modelId) => throw new NotImplementedException();
    public Task<int> CreateRecommendationCampaignAsync(RecommendationCampaignDto campaign) => throw new NotImplementedException();
    public Task<List<RecommendationCampaignDto>> GetRecommendationCampaignsAsync(bool? isActive = null) => throw new NotImplementedException();
    public Task<RecommendationCampaignDto?> GetRecommendationCampaignAsync(int campaignId) => throw new NotImplementedException();
    public Task<bool> UpdateRecommendationCampaignAsync(RecommendationCampaignDto campaign) => throw new NotImplementedException();
    public Task<bool> DeleteRecommendationCampaignAsync(int campaignId) => throw new NotImplementedException();
    public Task<bool> ActivateCampaignAsync(int campaignId) => throw new NotImplementedException();
    public Task<bool> DeactivateCampaignAsync(int campaignId) => throw new NotImplementedException();
    public Task<RecommendationCampaignStats> GetCampaignStatsAsync(int campaignId) => throw new NotImplementedException();
    public Task<int> CreateABTestAsync(ABTestRecommendationDto abTest) => throw new NotImplementedException();
    public Task<List<ABTestRecommendationDto>> GetABTestsAsync(ABTestStatus? status = null) => throw new NotImplementedException();
    public Task<ABTestRecommendationDto?> GetABTestAsync(int abTestId) => throw new NotImplementedException();
    public Task<bool> UpdateABTestAsync(ABTestRecommendationDto abTest) => throw new NotImplementedException();
    public Task<bool> StartABTestAsync(int abTestId) => throw new NotImplementedException();
    public Task<bool> StopABTestAsync(int abTestId) => throw new NotImplementedException();
    public Task<ABTestResultsDto> GetABTestResultsAsync(int abTestId) => throw new NotImplementedException();
    public Task<string?> GetWinningVariantAsync(int abTestId) => throw new NotImplementedException();
    public Task<RecommendationAnalyticsDto> GetRecommendationAnalyticsAsync(DateTime? startDate = null, DateTime? endDate = null) => throw new NotImplementedException();
    public Task<Dictionary<string, object>> GetAlgorithmPerformanceAsync(string algorithm, DateTime? startDate = null, DateTime? endDate = null) => throw new NotImplementedException();
    public Task<Dictionary<string, double>> GetRecommendationAccuracyAsync(RecommendationType type, DateTime? startDate = null, DateTime? endDate = null) => throw new NotImplementedException();
    public Task<List<TopPerformingProductDto>> GetTopPerformingProductsAsync(int limit = 20, DateTime? startDate = null, DateTime? endDate = null) => throw new NotImplementedException();
    public Task<Dictionary<string, object>> GetUserEngagementMetricsAsync(string userId) => throw new NotImplementedException();
    public Task<Dictionary<string, object>> GetRecommendationCoverageAsync() => throw new NotImplementedException();
    public Task<Dictionary<string, object>> GetDiversityMetricsAsync() => throw new NotImplementedException();
    public Task<List<ProductRecommendationDto>> SearchRecommendationsAsync(string query, string? userId = null, int limit = 20) => throw new NotImplementedException();
    public Task<RecommendationResponseDto> GetFilteredRecommendationsAsync(RecommendationRequestDto request, Dictionary<string, object> filters) => throw new NotImplementedException();
    public Task<List<ProductRecommendationDto>> GetRecommendationsByTagsAsync(List<string> tags, string? userId = null, int limit = 10) => throw new NotImplementedException();
    public Task<RecommendationResponseDto> GetLocationBasedRecommendationsAsync(string userId, string location, int limit = 10) => throw new NotImplementedException();
    public Task<RecommendationResponseDto> GetTimeBasedRecommendationsAsync(string userId, DateTime targetTime, int limit = 10) => throw new NotImplementedException();
    public Task<bool> OptimizeRecommendationParametersAsync(RecommendationType type) => throw new NotImplementedException();
    public Task<Dictionary<string, object>> GetFeatureImportanceAsync(int modelId) => throw new NotImplementedException();
    public Task<bool> UpdateModelWeightsAsync(int modelId, Dictionary<string, double> weights) => throw new NotImplementedException();
    public Task<List<string>> GetRecommendationExplanationsAsync(string userId, int productId) => throw new NotImplementedException();
    public Task<double> CalculateRecommendationConfidenceAsync(string userId, int productId) => Task.FromResult(0.8);
    public Task<bool> FeedbackLearningAsync(string userId, int productId, bool wasRelevant) => throw new NotImplementedException();
    public Task<bool> SyncUserDataAsync(string userId) => throw new NotImplementedException();
    public Task<bool> SyncProductDataAsync(int productId) => throw new NotImplementedException();
    public Task<bool> ImportUserInteractionsAsync(List<UserInteractionDto> interactions) => throw new NotImplementedException();
    public Task<bool> ExportRecommendationDataAsync(DateTime startDate, DateTime endDate, string format = "json") => throw new NotImplementedException();
    public Task<bool> SyncWithExternalSystemAsync(string systemId, Dictionary<string, object> config) => throw new NotImplementedException();
    public Task<Dictionary<string, object>> GetRecommendationConfigAsync() => throw new NotImplementedException();
    public Task<bool> UpdateRecommendationConfigAsync(Dictionary<string, object> config) => throw new NotImplementedException();
    public Task<bool> RefreshRecommendationCacheAsync(string? userId = null) => throw new NotImplementedException();
    public Task<bool> RebuildRecommendationIndexAsync() => throw new NotImplementedException();
    public Task<Dictionary<string, bool>> GetServiceHealthAsync() => throw new NotImplementedException();
    public Task<bool> TestRecommendationServiceAsync() => throw new NotImplementedException();
    public Task<RecommendationResponseDto> GetRealTimeRecommendationsAsync(string userId, string context, Dictionary<string, object>? sessionData = null) => throw new NotImplementedException();
    public Task<bool> UpdateRealTimeContextAsync(string userId, Dictionary<string, object> context) => throw new NotImplementedException();
    public Task<List<ProductRecommendationDto>> GetInstantRecommendationsAsync(string userId, List<int> currentProductIds, int limit = 5) => throw new NotImplementedException();
    public Task<RecommendationResponseDto> GetStreamingRecommendationsAsync(string userId, string streamId) => throw new NotImplementedException();
    public Task<RecommendationResponseDto> GetSocialRecommendationsAsync(string userId, int limit = 10) => throw new NotImplementedException();
    public Task<RecommendationResponseDto> GetFriendsRecommendationsAsync(string userId, List<string> friendIds, int limit = 10) => throw new NotImplementedException();
    public Task<RecommendationResponseDto> GetInfluencerRecommendationsAsync(string userId, List<string> influencerIds, int limit = 10) => throw new NotImplementedException();
    public Task<List<ProductRecommendationDto>> GetTrendingInNetworkAsync(string userId, int limit = 10) => throw new NotImplementedException();
    public Task<RecommendationResponseDto> GetContentBasedRecommendationsAsync(int productId, string? userId = null, int limit = 10) => throw new NotImplementedException();
    public Task<List<SimilarProductDto>> GetProductSimilarityAsync(int productId, SimilarityType similarityType, int limit = 10) => throw new NotImplementedException();
    public Task<double> CalculateProductSimilarityAsync(int productId1, int productId2, SimilarityType similarityType) => throw new NotImplementedException();
    public Task<bool> UpdateProductFeaturesAsync(int productId, Dictionary<string, object> features) => throw new NotImplementedException();
    public Task<Dictionary<string, double>> GetProductFeatureVectorAsync(int productId) => Task.FromResult(new Dictionary<string, double>());

    // Méthodes d'aide privées pour les recommandations de base
    private async Task<List<ProductRecommendationDto>> GetTrendingProductsFromInteractionsAsync(int limit)
    {
        // Simuler la récupération des produits tendance
        // Dans une vraie implémentation, cela interrogerait la base de données des interactions
        var trendingProducts = new List<ProductRecommendationDto>();

        for (int i = 1; i <= limit; i++)
        {
            trendingProducts.Add(new ProductRecommendationDto
            {
                ProductId = i,
                Name = $"Produit Tendance {i}",
                Price = 25000 + (i * 5000), // Prix en GNF
                ImageUrl = $"/images/trending-product-{i}.jpg",
                Score = 0.9 - (i * 0.05),
                Reason = "Très populaire cette semaine",
                CategoryId = (i % 5) + 1,
                CategoryName = $"Catégorie {(i % 5) + 1}",
                Brand = $"Marque {i}",
                Rating = 4.5 - (i * 0.1),
                ReviewCount = 100 - (i * 5),
                IsInStock = true,
                DiscountPercentage = i % 3 == 0 ? 10 : 0
            });
        }

        return trendingProducts;
    }

    private async Task<List<ProductRecommendationDto>> GetBestSellersFromPurchasesAsync(int? categoryId, int limit)
    {
        // Simuler la récupération des meilleures ventes
        var bestSellers = new List<ProductRecommendationDto>();

        for (int i = 1; i <= limit; i++)
        {
            bestSellers.Add(new ProductRecommendationDto
            {
                ProductId = i + 100,
                Name = $"Best-seller {i}",
                Price = 30000 + (i * 7000), // Prix en GNF
                ImageUrl = $"/images/bestseller-{i}.jpg",
                Score = 0.95 - (i * 0.03),
                Reason = "Produit le plus vendu",
                CategoryId = categoryId ?? ((i % 5) + 1),
                CategoryName = $"Catégorie {categoryId ?? ((i % 5) + 1)}",
                Brand = $"Marque Premium {i}",
                Rating = 4.8 - (i * 0.05),
                ReviewCount = 200 - (i * 10),
                IsInStock = true,
                DiscountPercentage = 0,
                SalesCount = 500 - (i * 20)
            });
        }

        return bestSellers;
    }

    private async Task<List<ProductRecommendationDto>> GetNewArrivalsFromCatalogAsync(int? categoryId, int limit)
    {
        // Simuler la récupération des nouveaux arrivages
        var newArrivals = new List<ProductRecommendationDto>();

        for (int i = 1; i <= limit; i++)
        {
            newArrivals.Add(new ProductRecommendationDto
            {
                ProductId = i + 200,
                Name = $"Nouveau Produit {i}",
                Price = 20000 + (i * 4000), // Prix en GNF
                ImageUrl = $"/images/new-arrival-{i}.jpg",
                Score = 0.8,
                Reason = "Nouveau sur NafaPlace",
                CategoryId = categoryId ?? ((i % 5) + 1),
                CategoryName = $"Catégorie {categoryId ?? ((i % 5) + 1)}",
                Brand = $"Nouvelle Marque {i}",
                Rating = 4.0,
                ReviewCount = i * 2, // Peu d'avis car nouveau
                IsInStock = true,
                DiscountPercentage = i % 2 == 0 ? 15 : 0, // Promotion de lancement
                IsNew = true,
                CreatedAt = DateTime.UtcNow.AddDays(-i)
            });
        }

        return newArrivals;
    }

    private async Task<List<ProductRecommendationDto>> GetRecentlyViewedFromInteractionsAsync(string userId, int limit)
    {
        // Simuler la récupération des produits récemment vus
        var recentlyViewed = new List<ProductRecommendationDto>();

        for (int i = 1; i <= limit; i++)
        {
            recentlyViewed.Add(new ProductRecommendationDto
            {
                ProductId = i + 300,
                Name = $"Produit Vu {i}",
                Price = 15000 + (i * 3000), // Prix en GNF
                ImageUrl = $"/images/recently-viewed-{i}.jpg",
                Score = 1.0 - (i * 0.1), // Score basé sur la récence
                Reason = $"Vu il y a {i} jour(s)",
                CategoryId = (i % 5) + 1,
                CategoryName = $"Catégorie {(i % 5) + 1}",
                Brand = $"Marque {i}",
                Rating = 4.2,
                ReviewCount = 50 + (i * 5),
                IsInStock = true,
                DiscountPercentage = 0,
                LastViewedAt = DateTime.UtcNow.AddDays(-i)
            });
        }

        return recentlyViewed;
    }

    private async Task<List<ProductRecommendationDto>> GetUpSellProductsAsync(int productId, int limit)
    {
        // Simuler la récupération des produits de gamme supérieure
        var upSellProducts = new List<ProductRecommendationDto>();

        for (int i = 1; i <= limit; i++)
        {
            upSellProducts.Add(new ProductRecommendationDto
            {
                ProductId = productId + (i * 10),
                Name = $"Version Premium {i}",
                Price = 50000 + (i * 15000), // Prix plus élevé pour up-sell
                ImageUrl = $"/images/upsell-{productId}-{i}.jpg",
                Score = 0.85 - (i * 0.05),
                Reason = "Version améliorée avec plus de fonctionnalités",
                CategoryId = (productId % 5) + 1,
                CategoryName = $"Catégorie Premium",
                Brand = $"Marque Premium",
                Rating = 4.7,
                ReviewCount = 80 + (i * 10),
                IsInStock = true,
                DiscountPercentage = 0,
                IsPremium = true,
                Features = new List<string> { "Qualité supérieure", "Garantie étendue", "Support premium" }
            });
        }

        return upSellProducts;
    }

    private async Task<List<ProductRecommendationDto>> GetComplementaryProductsAsync(int productId, int limit)
    {
        // Simuler la récupération des produits complémentaires
        var complementaryProducts = new List<ProductRecommendationDto>();

        for (int i = 1; i <= limit; i++)
        {
            complementaryProducts.Add(new ProductRecommendationDto
            {
                ProductId = productId + (i * 100),
                Name = $"Accessoire {i}",
                Price = 8000 + (i * 2000), // Prix plus bas pour les accessoires
                ImageUrl = $"/images/accessory-{productId}-{i}.jpg",
                Score = 0.9,
                Reason = "Souvent acheté avec ce produit",
                CategoryId = ((productId + i) % 5) + 1,
                CategoryName = $"Accessoires",
                Brand = $"Marque Compatible",
                Rating = 4.3,
                ReviewCount = 60 + (i * 5),
                IsInStock = true,
                DiscountPercentage = i % 2 == 0 ? 5 : 0,
                IsAccessory = true,
                CompatibleWith = new List<int> { productId }
            });
        }

        return complementaryProducts;
    }

    private async Task<List<ProductRecommendationDto>> GetAlternativeProductsAsync(int productId, int limit)
    {
        // Simuler la récupération des produits alternatifs
        var alternativeProducts = new List<ProductRecommendationDto>();

        for (int i = 1; i <= limit; i++)
        {
            alternativeProducts.Add(new ProductRecommendationDto
            {
                ProductId = productId + (i * 50),
                Name = $"Alternative {i}",
                Price = 22000 + (i * 3000), // Prix similaire
                ImageUrl = $"/images/alternative-{productId}-{i}.jpg",
                Score = 0.8 - (i * 0.02),
                Reason = "Produit similaire d'une autre marque",
                CategoryId = (productId % 5) + 1,
                CategoryName = $"Catégorie {(productId % 5) + 1}",
                Brand = $"Marque Alternative {i}",
                Rating = 4.1 + (i * 0.1),
                ReviewCount = 70 + (i * 8),
                IsInStock = true,
                DiscountPercentage = i % 3 == 0 ? 8 : 0,
                IsAlternative = true,
                SimilarityScore = 0.9 - (i * 0.05)
            });
        }

        return alternativeProducts;
    }
    public Task<RecommendationResponseDto> GetCollaborativeFilteringRecommendationsAsync(string userId, int limit = 10) => throw new NotImplementedException();
    public Task<List<string>> GetSimilarUsersAsync(string userId, double similarityThreshold = 0.5, int limit = 20) => throw new NotImplementedException();
    public Task<double> CalculateUserSimilarityAsync(string userId1, string userId2) => throw new NotImplementedException();
    public Task<Dictionary<int, double>> GetUserProductRatingsAsync(string userId) => throw new NotImplementedException();
    public Task<bool> UpdateUserRatingAsync(string userId, int productId, double rating) => throw new NotImplementedException();
    public Task<RecommendationResponseDto> GetHybridRecommendationsAsync(string userId, Dictionary<string, double> algorithmWeights, int limit = 10) => throw new NotImplementedException();
    public Task<Dictionary<string, double>> OptimizeHybridWeightsAsync(string userId) => throw new NotImplementedException();
    public Task<RecommendationResponseDto> GetEnsembleRecommendationsAsync(string userId, List<string> algorithms, int limit = 10) => throw new NotImplementedException();
    public Task<bool> CalibrateRecommendationScoresAsync(RecommendationType type) => throw new NotImplementedException();
    public Task<List<string>> GetRecommendationReasonsAsync(string userId, int productId) => throw new NotImplementedException();
    public Task<Dictionary<string, object>> GetRecommendationMetadataAsync(string requestId) => throw new NotImplementedException();
    public Task<bool> LogRecommendationDecisionAsync(string userId, int productId, Dictionary<string, object> decisionFactors) => throw new NotImplementedException();
    public Task<List<string>> GetAlgorithmExplanationAsync(string algorithm, Dictionary<string, object> parameters) => throw new NotImplementedException();
    public Task<Dictionary<string, object>> DetectRecommendationBiasAsync(string userId) => throw new NotImplementedException();
    public Task<bool> ApplyFairnessConstraintsAsync(RecommendationType type, Dictionary<string, object> constraints) => throw new NotImplementedException();
    public Task<RecommendationResponseDto> GetDiversifiedRecommendationsAsync(string userId, double diversityWeight = 0.3, int limit = 10) => throw new NotImplementedException();
    public Task<Dictionary<string, double>> CalculateRecommendationDiversityAsync(List<ProductRecommendationDto> recommendations) => throw new NotImplementedException();
    public Task<RecommendationResponseDto> GetWeatherBasedRecommendationsAsync(string userId, string weatherCondition, int limit = 10) => throw new NotImplementedException();
    public Task<RecommendationResponseDto> GetEventBasedRecommendationsAsync(string userId, string eventType, int limit = 10) => throw new NotImplementedException();
    public Task<RecommendationResponseDto> GetMoodBasedRecommendationsAsync(string userId, string mood, int limit = 10) => throw new NotImplementedException();
    public Task<RecommendationResponseDto> GetOccasionBasedRecommendationsAsync(string userId, string occasion, int limit = 10) => throw new NotImplementedException();

    // Méthodes d'aide privées simulées
    private async Task<List<SimilarProductDto>> FilterByUserPreferencesAsync(List<FrequentItemDto> items, UserPreferenceDto preferences)
    {
        await Task.Delay(10);
        return items.Select(i => new SimilarProductDto 
        { 
            ProductId = i.ProductId, 
            SimilarityScore = i.Confidence 
        }).ToList();
    }
}


