using System.Net.Http.Headers;
using Blazored.LocalStorage;

namespace NafaPlace.SellerPortal.Services;

public class AuthenticatedHttpClientService
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ILocalStorageService _localStorage;

    public AuthenticatedHttpClientService(IHttpClientFactory httpClientFactory, ILocalStorageService localStorage)
    {
        _httpClientFactory = httpClientFactory;
        _localStorage = localStorage;
    }

    public async Task<HttpClient> GetAuthenticatedClientAsync(string clientName)
    {
        var client = _httpClientFactory.CreateClient(clientName);

        try
        {
            var token = await _localStorage.GetItemAsync<string>("authToken");

            Console.WriteLine($"[DEBUG] Token récupéré: {(string.IsNullOrEmpty(token) ? "VIDE" : "PRÉSENT")}");

            if (!string.IsNullOrEmpty(token))
            {
                // Nettoyer le token des guillemets éventuels
                token = token.Trim('"');
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                Console.WriteLine($"[DEBUG] Header Authorization ajouté pour client: {clientName}");
            }
            else
            {
                Console.WriteLine($"[DEBUG] ATTENTION: Aucun token trouvé pour client: {clientName}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération du token: {ex.Message}");
        }

        return client;
    }

    public async Task<HttpClient> GetAuthenticatedClientAsync()
    {
        return await GetAuthenticatedClientAsync("DefaultClient");
    }
}
