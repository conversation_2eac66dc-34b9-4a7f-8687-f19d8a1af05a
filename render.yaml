databases:
  - name: nafaplace-postgres
    databaseName: nafaplace
    user: nafaplace
    region: frankfurt

services:
  # API Gateway
  - type: web
    name: nafaplace-api-gateway
    env: docker
    dockerfilePath: ./src/ApiGateways/Web/NafaPlace.ApiGateway/Dockerfile
    dockerContext: .
    region: frankfurt
    plan: starter
    envVars:
      - key: ASPNETCORE_ENVIRONMENT
        value: Production
      - key: ASPNETCORE_URLS
        value: http://+:10000
      - key: IdentityUrl
        value: https://nafaplace-identity-api.onrender.com
      - key: CatalogUrl
        value: https://nafaplace-catalog-api.onrender.com
      - key: CartUrl
        value: https://nafaplace-cart-api.onrender.com
      - key: OrderUrl
        value: https://nafaplace-order-api.onrender.com
      - key: PaymentUrl
        value: https://nafaplace-payment-api.onrender.com
      - key: ReviewsUrl
        value: https://nafaplace-reviews-api.onrender.com
      - key: NotificationsUrl
        value: https://nafaplace-notifications-api.onrender.com
      - key: WishlistUrl
        value: https://nafaplace-wishlist-api.onrender.com
      - key: InventoryUrl
        value: https://nafaplace-inventory-api.onrender.com
      - key: CouponUrl
        value: https://nafaplace-coupon-api.onrender.com
      - key: DeliveryUrl
        value: https://nafaplace-delivery-api.onrender.com
      - key: AnalyticsUrl
        value: https://nafaplace-analytics-api.onrender.com

  # Identity Service
  - type: web
    name: nafaplace-identity-api
    env: docker
    dockerfilePath: ./src/Services/Identity/Dockerfile
    dockerContext: .
    region: frankfurt
    plan: starter
    envVars:
      - key: ASPNETCORE_ENVIRONMENT
        value: Production
      - key: ASPNETCORE_URLS
        value: http://+:10000
      - key: ConnectionStrings__DefaultConnection
        fromDatabase:
          name: nafaplace-postgres
          property: connectionString
      - key: JwtSettings__SecretKey
        generateValue: true
      - key: JwtSettings__Issuer
        value: https://nafaplace-identity-api.onrender.com
      - key: JwtSettings__Audience
        value: nafaplace-clients
      - key: AllowedOrigins__0
        value: https://nafaplace-web.onrender.com
      - key: AllowedOrigins__1
        value: https://nafaplace-admin-portal.onrender.com
      - key: AllowedOrigins__2
        value: https://nafaplace-seller-portal.onrender.com

  # Catalog Service
  - type: web
    name: nafaplace-catalog-api
    env: docker
    dockerfilePath: ./src/Services/Catalog/Dockerfile
    dockerContext: .
    region: frankfurt
    plan: starter
    envVars:
      - key: ASPNETCORE_ENVIRONMENT
        value: Production
      - key: ASPNETCORE_URLS
        value: http://+:10000
      - key: ConnectionStrings__DefaultConnection
        fromDatabase:
          name: nafaplace-postgres
          property: connectionString
      - key: IdentityUrl
        value: https://nafaplace-identity-api.onrender.com
      - key: CloudinarySettings__CloudName
        sync: false
      - key: CloudinarySettings__ApiKey
        sync: false
      - key: CloudinarySettings__ApiSecret
        sync: false
      - key: JWT_AUDIENCE
        sync: false
      - key: JWT_ISSUER
        sync: false
      - key: JWT_SECRET_KEY
        sync: false

  # Cart Service
  - type: web
    name: nafaplace-cart-api
    env: docker
    dockerfilePath: ./src/Services/Cart/Dockerfile
    dockerContext: .
    region: frankfurt
    plan: starter
    envVars:
      - key: ASPNETCORE_ENVIRONMENT
        value: Production
      - key: ASPNETCORE_URLS
        value: http://+:10000
      - key: ConnectionStrings__DefaultConnection
        fromDatabase:
          name: nafaplace-postgres
          property: connectionString
      - key: IdentityUrl
        value: https://nafaplace-identity-api.onrender.com
      - key: JWT_AUDIENCE
        sync: false
      - key: JWT_ISSUER
        sync: false
      - key: JWT_SECRET_KEY
        sync: false

  # Identity Service (CRITIQUE - manquant)
  - type: web
    name: nafaplace-identity-api
    env: docker
    dockerfilePath: ./src/Services/Identity/Dockerfile
    dockerContext: .
    region: frankfurt
    plan: starter
    envVars:
      - key: ASPNETCORE_ENVIRONMENT
        value: Production
      - key: ASPNETCORE_URLS
        value: http://+:10000
      - key: ConnectionStrings__DefaultConnection
        fromDatabase:
          name: nafaplace-postgres
          property: connectionString
      - key: JWT_SECRET_KEY
        sync: false
      - key: JWT_ISSUER
        sync: false
      - key: JWT_AUDIENCE
        sync: false

  # Cart Service (CRITIQUE - manquant)
  - type: web
    name: nafaplace-cart-api
    env: docker
    dockerfilePath: ./src/Services/Cart/NafaPlace.Cart.API/Dockerfile
    dockerContext: .
    region: frankfurt
    plan: starter
    envVars:
      - key: ASPNETCORE_ENVIRONMENT
        value: Production
      - key: ASPNETCORE_URLS
        value: http://+:10000
      - key: ConnectionStrings__DefaultConnection
        fromDatabase:
          name: nafaplace-postgres
          property: connectionString
      - key: IdentityUrl
        value: https://nafaplace-identity-api.onrender.com
      - key: JWT_SECRET_KEY
        sync: false
      - key: JWT_ISSUER
        sync: false
      - key: JWT_AUDIENCE
        sync: false

  # Order Service
  - type: web
    name: nafaplace-order-api
    env: docker
    dockerfilePath: ./src/Services/Order/Dockerfile
    dockerContext: .
    region: frankfurt
    plan: starter
    envVars:
      - key: ASPNETCORE_ENVIRONMENT
        value: Production
      - key: ASPNETCORE_URLS
        value: http://+:10000
      - key: ConnectionStrings__DefaultConnection
        fromDatabase:
          name: nafaplace-postgres
          property: connectionString
      - key: IdentityUrl
        value: https://nafaplace-identity-api.onrender.com
      - key: JWT_Audience
        sync: false
      - key: JWT_Issuer
        sync: false
      - key: JWT_TokenLifetimeInMinutes
        value: "60"
      - key: JWT_SECRET_KEY
        sync: false
      - key: STRIPE_PUBLISHABLE_KEY
        sync: false
      - key: STRIPE_SECRET_KEY
        sync: false

  # Payment Service (CRITIQUE - manquant)
  - type: web
    name: nafaplace-payment-api
    env: docker
    dockerfilePath: ./src/Services/Payment/NafaPlace.Payment.API/Dockerfile
    dockerContext: .
    region: frankfurt
    plan: starter
    envVars:
      - key: ASPNETCORE_ENVIRONMENT
        value: Production
      - key: ASPNETCORE_URLS
        value: http://+:10000
      - key: ConnectionStrings__DefaultConnection
        fromDatabase:
          name: nafaplace-postgres
          property: connectionString
      - key: IdentityUrl
        value: https://nafaplace-identity-api.onrender.com
      - key: JWT_SECRET_KEY
        sync: false
      - key: JWT_ISSUER
        sync: false
      - key: JWT_AUDIENCE
        sync: false
      - key: STRIPE_PUBLISHABLE_KEY
        sync: false
      - key: STRIPE_SECRET_KEY
        sync: false

  # Payment Service
  - type: web
    name: nafaplace-payment-api
    env: docker
    dockerfilePath: ./src/Services/Payment/Dockerfile
    dockerContext: .
    region: frankfurt
    plan: starter
    envVars:
      - key: ASPNETCORE_ENVIRONMENT
        value: Production
      - key: ASPNETCORE_URLS
        value: http://+:10000
      - key: ConnectionStrings__DefaultConnection
        fromDatabase:
          name: nafaplace-postgres
          property: connectionString
      - key: IdentityUrl
        value: https://nafaplace-identity-api.onrender.com
      - key: ApiSettings__BaseUrl
        value: https://nafaplace-api-gateway.onrender.com
      - key: JWT_AUDIENCE
        sync: false
      - key: JWT_ISSUER
        sync: false
      - key: JWT_SECRET_KEY
        sync: false
      - key: STRIPE_PUBLISHABLE_KEY
        sync: false
      - key: STRIPE_SECRET_KEY
        sync: false
      - key: JWT_SECRET_KEY
        sync: false
      - key: OrangeMoney__MerchantId
        sync: false
      - key: OrangeMoney__SecretKey
        sync: false

  # Reviews Service
  - type: web
    name: nafaplace-reviews-api
    env: docker
    dockerfilePath: ./src/Services/Reviews/Dockerfile
    dockerContext: .
    region: frankfurt
    plan: starter
    envVars:
      - key: ASPNETCORE_ENVIRONMENT
        value: Production
      - key: ASPNETCORE_URLS
        value: http://+:10000
      - key: ConnectionStrings__DefaultConnection
        fromDatabase:
          name: nafaplace-postgres
          property: connectionString
      - key: IdentityUrl
        value: https://nafaplace-identity-api.onrender.com
      - key: JWT_AUDIENCE
        sync: false
      - key: JWT_ISSUER
        sync: false
      - key: JWT_SECRET_KEY
        sync: false

  # Notifications Service
  - type: web
    name: nafaplace-notifications-api
    env: docker
    dockerfilePath: ./src/Services/Notifications/Dockerfile
    dockerContext: .
    region: frankfurt
    plan: starter
    envVars:
      - key: ASPNETCORE_ENVIRONMENT
        value: Production
      - key: ASPNETCORE_URLS
        value: http://+:10000
      - key: ConnectionStrings__DefaultConnection
        fromDatabase:
          name: nafaplace-postgres
          property: connectionString
      - key: IdentityUrl
        value: https://nafaplace-identity-api.onrender.com
      - key: JWT_AUDIENCE
        sync: false
      - key: JWT_ISSUER
        sync: false
      - key: JWT_SECRET_KEY
        sync: false

  # Wishlist Service
  - type: web
    name: nafaplace-wishlist-api
    env: docker
    dockerfilePath: ./src/Services/Wishlist/Dockerfile
    dockerContext: .
    region: frankfurt
    plan: starter
    envVars:
      - key: ASPNETCORE_ENVIRONMENT
        value: Production
      - key: ASPNETCORE_URLS
        value: http://+:10000
      - key: ConnectionStrings__DefaultConnection
        fromDatabase:
          name: nafaplace-postgres
          property: connectionString
      - key: IdentityUrl
        value: https://nafaplace-identity-api.onrender.com
      - key: JWT_AUDIENCE
        sync: false
      - key: JWT_ISSUER
        sync: false
      - key: JWT_SECRET_KEY
        sync: false

  # Inventory Service
  - type: web
    name: nafaplace-inventory-api-v2
    env: docker
    dockerfilePath: ./src/Services/Inventory/NafaPlace.Inventory.API/Dockerfile
    dockerContext: .
    region: frankfurt
    plan: starter
    envVars:
      - key: ASPNETCORE_ENVIRONMENT
        value: Production
      - key: ASPNETCORE_URLS
        value: http://+:10000
      - key: ConnectionStrings__DefaultConnection
        fromDatabase:
          name: nafaplace-postgres
          property: connectionString
      - key: IdentityUrl
        value: https://nafaplace-identity-api.onrender.com
      - key: JWT_AUDIENCE
        sync: false
      - key: JWT_ISSUER
        sync: false
      - key: JWT_SECRET_KEY
        sync: false

  # Coupon Service
  - type: web
    name: nafaplace-coupon-api
    env: docker
    dockerfilePath: ./src/Services/Coupon/Dockerfile
    dockerContext: .
    region: frankfurt
    plan: starter
    envVars:
      - key: ASPNETCORE_ENVIRONMENT
        value: Production
      - key: ASPNETCORE_URLS
        value: http://+:10000
      - key: ConnectionStrings__DefaultConnection
        fromDatabase:
          name: nafaplace-postgres
          property: connectionString
      - key: IdentityUrl
        value: https://nafaplace-identity-api.onrender.com
      - key: JWT_AUDIENCE
        sync: false
      - key: JWT_ISSUER
        sync: false
      - key: JWT_SECRET_KEY
        sync: false

  # Delivery Service
  - type: web
    name: nafaplace-delivery-api
    env: docker
    dockerfilePath: ./src/Services/Delivery/Dockerfile
    dockerContext: .
    region: frankfurt
    plan: starter
    envVars:
      - key: ASPNETCORE_ENVIRONMENT
        value: Production
      - key: ASPNETCORE_URLS
        value: http://+:10000
      - key: ConnectionStrings__DefaultConnection
        fromDatabase:
          name: nafaplace-postgres
          property: connectionString
      - key: IdentityUrl
        value: https://nafaplace-identity-api.onrender.com
      - key: JWT_AUDIENCE
        sync: false
      - key: JWT_ISSUER
        sync: false
      - key: JWT_SECRET_KEY
        sync: false

  # Analytics Service
  - type: web
    name: nafaplace-analytics-api
    env: docker
    dockerfilePath: ./src/Services/Analytics/Dockerfile
    dockerContext: .
    region: frankfurt
    plan: starter
    envVars:
      - key: ASPNETCORE_ENVIRONMENT
        value: Production
      - key: ASPNETCORE_URLS
        value: http://+:10000
      - key: ConnectionStrings__DefaultConnection
        fromDatabase:
          name: nafaplace-postgres
          property: connectionString
      - key: IdentityUrl
        value: https://nafaplace-identity-api.onrender.com
      - key: JWT_AUDIENCE
        sync: false
      - key: JWT_ISSUER
        sync: false
      - key: JWT_SECRET_KEY
        sync: false

  # Main Web Portal
  - type: web
    name: nafaplace-web
    env: docker
    dockerfilePath: ./src/Web/Dockerfile.production
    dockerContext: .
    region: frankfurt
    plan: starter
    envVars:
      - key: ASPNETCORE_ENVIRONMENT
        value: Production
      - key: ASPNETCORE_URLS
        value: http://+:10000
      - key: ApiGatewayUrl
        value: https://nafaplace-api-gateway.onrender.com

  # Admin Portal
  - type: web
    name: nafaplace-admin-portal
    env: docker
    dockerfilePath: ./src/Web/AdminPortal/Dockerfile
    dockerContext: .
    region: frankfurt
    plan: starter
    envVars:
      - key: ASPNETCORE_ENVIRONMENT
        value: Production
      - key: ASPNETCORE_URLS
        value: http://+:10000
      - key: ApiGatewayUrl
        value: https://nafaplace-api-gateway.onrender.com

  # Seller Portal
  - type: web
    name: nafaplace-seller-portal
    env: docker
    dockerfilePath: ./src/Web/SellerPortal/Dockerfile
    dockerContext: .
    region: frankfurt
    plan: starter
    envVars:
      - key: ASPNETCORE_ENVIRONMENT
        value: Production
      - key: ASPNETCORE_URLS
        value: http://+:10000
      - key: ApiGatewayUrl
        value: https://nafaplace-api-gateway.onrender.com
