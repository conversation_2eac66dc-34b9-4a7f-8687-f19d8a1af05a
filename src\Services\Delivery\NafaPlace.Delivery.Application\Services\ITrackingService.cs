using NafaPlace.Delivery.Domain.DTOs;
using NafaPlace.Delivery.Application.DTOs;

namespace NafaPlace.Delivery.Application.Services;

/// <summary>
/// Interface pour le service de suivi GPS en temps réel
/// Gère la géolocalisation des livreurs et le suivi des livraisons
/// </summary>
public interface ITrackingService
{
    /// <summary>
    /// Met à jour la position GPS d'un livreur
    /// </summary>
    Task<TrackingResult> UpdateDeliveryPersonPositionAsync(UpdatePositionDto positionDto);

    /// <summary>
    /// Récupère la position actuelle d'une livraison
    /// </summary>
    Task<DeliveryPositionDto?> GetDeliveryCurrentPositionAsync(int deliveryId);

    /// <summary>
    /// Récupère l'historique complet de suivi d'une livraison
    /// </summary>
    Task<DeliveryTrackingHistoryDto> GetDeliveryTrackingHistoryAsync(int deliveryId);

    /// <summary>
    /// Démarre le suivi pour une livraison
    /// </summary>
    Task<TrackingResult> StartDeliveryTrackingAsync(StartTrackingDto startDto);

    /// <summary>
    /// Termine le suivi d'une livraison
    /// </summary>
    Task<TrackingResult> CompleteDeliveryTrackingAsync(CompleteTrackingDto completeDto);

    /// <summary>
    /// Met à jour le statut d'une livraison avec géolocalisation
    /// </summary>
    Task<TrackingResult> UpdateDeliveryStatusWithLocationAsync(UpdateDeliveryStatusDto statusDto);

    /// <summary>
    /// Calcule le temps estimé d'arrivée basé sur la position actuelle
    /// </summary>
    Task<EstimatedArrivalDto?> CalculateEstimatedArrivalTimeAsync(int deliveryId);

    /// <summary>
    /// Récupère toutes les livraisons actives pour un livreur
    /// </summary>
    Task<List<ActiveDeliveryDto>> GetActiveDeliveriesForPersonAsync(string deliveryPersonId);

    /// <summary>
    /// Vérifie si un utilisateur peut accéder aux informations de suivi d'une livraison
    /// </summary>
    Task<bool> CanUserAccessDeliveryAsync(string userId, int deliveryId);

    /// <summary>
    /// Calcule la distance entre deux points GPS
    /// </summary>
    double CalculateDistance(double lat1, double lon1, double lat2, double lon2);

    /// <summary>
    /// Optimise l'itinéraire pour plusieurs livraisons
    /// </summary>
    Task<List<ActiveDeliveryDto>> OptimizeDeliveryRouteAsync(string deliveryPersonId, List<int> deliveryIds);

    /// <summary>
    /// Récupère les métriques de performance d'un livreur
    /// </summary>
    Task<DeliveryPersonMetricsDto> GetDeliveryPersonMetricsAsync(string deliveryPersonId, DateTime fromDate, DateTime toDate);

    /// <summary>
    /// Détecte les anomalies dans les données de suivi (arrêts prolongés, déviations d'itinéraire, etc.)
    /// </summary>
    Task<List<TrackingAnomalyDto>> DetectTrackingAnomaliesAsync(int deliveryId);

    /// <summary>
    /// Envoie des notifications géolocalisées (quand le livreur approche)
    /// </summary>
    Task<bool> CheckProximityNotificationsAsync(string deliveryPersonId, PositionDto currentPosition);

    /// <summary>
    /// Sauvegarde un point d'intérêt (POI) pour optimisation future
    /// </summary>
    Task<TrackingResult> SavePointOfInterestAsync(string deliveryPersonId, PointOfInterestDto poi);

    /// <summary>
    /// Récupère les zones de livraison disponibles autour d'une position
    /// </summary>
    Task<List<DeliveryZoneDto>> GetNearbyDeliveryZonesAsync(double latitude, double longitude, double radiusKm);
}

/// <summary>
/// DTO pour les métriques de performance d'un livreur
/// </summary>
public class DeliveryPersonMetricsDto
{
    public string DeliveryPersonId { get; set; } = "";
    public int TotalDeliveries { get; set; }
    public int CompletedDeliveries { get; set; }
    public int CancelledDeliveries { get; set; }
    public double SuccessRate => TotalDeliveries > 0 ? (double)CompletedDeliveries / TotalDeliveries * 100 : 0;
    public double AverageDeliveryTime { get; set; } // en minutes
    public double TotalDistanceTraveled { get; set; } // en km
    public double AverageSpeed { get; set; } // en km/h
    public int OnTimeDeliveries { get; set; }
    public double OnTimeRate => CompletedDeliveries > 0 ? (double)OnTimeDeliveries / CompletedDeliveries * 100 : 0;
    public decimal TotalEarnings { get; set; }
    public double CustomerRating { get; set; } // 0-5
    public int TotalRatings { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
}

/// <summary>
/// DTO pour les anomalies de suivi
/// </summary>
public class TrackingAnomalyDto
{
    public string Type { get; set; } = "";
    public string Description { get; set; } = "";
    public DateTime DetectedAt { get; set; }
    public PositionDto Position { get; set; } = new();
    public string Severity { get; set; } = "low"; // low, medium, high, critical
    public string? Recommendation { get; set; }
    public bool RequiresAction { get; set; }
}

/// <summary>
/// DTO pour les points d'intérêt
/// </summary>
public class PointOfInterestDto
{
    public string Name { get; set; } = "";
    public string Description { get; set; } = "";
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string Category { get; set; } = ""; // parking, restaurant, depot, etc.
    public Dictionary<string, object>? Metadata { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}

