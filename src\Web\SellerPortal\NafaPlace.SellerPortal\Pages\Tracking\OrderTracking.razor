@page "/tracking/orders"
@using NafaPlace.SellerPortal.Models.Orders
@using NafaPlace.SellerPortal.Models.Delivery
@using NafaPlace.SellerPortal.Services
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.JSInterop
@using System.IdentityModel.Tokens.Jwt
@inject IOrderService OrderService
@inject IDeliveryTrackingService DeliveryTrackingService
@inject IJSRuntime JSRuntime
@inject IAuthService AuthService
@inject NotificationService NotificationService
@inject ILogger<OrderTracking> Logger
@attribute [Authorize]

<PageTitle>Suivi des Commandes - Vendeur</PageTitle>

<div class="container-fluid px-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-route text-primary me-2"></i>
                        Su<PERSON><PERSON> des <PERSON>
                    </h1>
                    <p class="text-muted mb-0">Su<PERSON>z vos commandes en temps réel</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" @onclick="RefreshOrders">
                        <i class="fas fa-sync-alt me-2"></i>Actualiser
                    </button>
                    <button class="btn btn-primary" @onclick="ShowTrackingModal">
                        <i class="fas fa-plus me-2"></i>Ajouter Suivi
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques de livraison -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                                <i class="fas fa-shipping-fast text-primary fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">En Transit</h6>
                            <h4 class="mb-0">@inTransitCount</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-opacity-10 rounded-3 p-3">
                                <i class="fas fa-check-circle text-success fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Livrées</h6>
                            <h4 class="mb-0">@deliveredCount</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                                <i class="fas fa-clock text-warning fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">En Retard</h6>
                            <h4 class="mb-0">@delayedCount</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-opacity-10 rounded-3 p-3">
                                <i class="fas fa-percentage text-info fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Taux Livraison</h6>
                            <h4 class="mb-0">@deliveryRate.ToString("F1")%</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres et recherche -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Rechercher</label>
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="N° commande, client..." @bind="searchTerm" @oninput="FilterOrders">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Statut</label>
                    <select class="form-select" @bind="selectedStatus" @bind:after="FilterOrders">
                        <option value="">Tous</option>
                        <option value="pending">En attente</option>
                        <option value="processing">En traitement</option>
                        <option value="shipped">Expédiée</option>
                        <option value="in_transit">En transit</option>
                        <option value="delivered">Livrée</option>
                        <option value="delayed">En retard</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Transporteur</label>
                    <select class="form-select" @bind="selectedCarrier" @bind:after="FilterOrders">
                        <option value="">Tous</option>
                        <option value="dhl">DHL</option>
                        <option value="fedex">FedEx</option>
                        <option value="ups">UPS</option>
                        <option value="local">Transport Local</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Date début</label>
                    <input type="date" class="form-control" @bind="startDate" @bind:after="FilterOrders">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Date fin</label>
                    <input type="date" class="form-control" @bind="endDate" @bind:after="FilterOrders">
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button class="btn btn-outline-secondary w-100" @onclick="ClearFilters">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des commandes -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-transparent border-0">
            <h5 class="card-title mb-0">
                <i class="fas fa-list me-2"></i>Commandes (@filteredOrders.Count)
            </h5>
        </div>
        <div class="card-body p-0">
            @if (isLoading)
            {
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            }
            else if (filteredOrders?.Any() == true)
            {
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Commande</th>
                                <th>Client</th>
                                <th>Produits</th>
                                <th>Statut</th>
                                <th>Transporteur</th>
                                <th>Date Expédition</th>
                                <th>Livraison Prévue</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var order in filteredOrders)
                            {
                                <tr>
                                    <td>
                                        <div>
                                            <strong>#@order.OrderNumber</strong>
                                            <br>
                                            <small class="text-muted">@FormatCurrency(order.TotalAmount)</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>@order.CustomerName</strong>
                                            <br>
                                            <small class="text-muted">@order.ShippingAddress</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">@order.ItemCount produit(s)</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-@GetStatusColor(order.Status)">
                                            @GetStatusText(order.Status)
                                        </span>
                                        @if (order.IsDelayed)
                                        {
                                            <br><small class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Retard</small>
                                        }
                                    </td>
                                    <td>
                                        @if (!string.IsNullOrEmpty(order.Carrier))
                                        {
                                            <div class="d-flex align-items-center">
                                                <img src="/images/carriers/@(order.Carrier.ToLower()).png" alt="@order.Carrier" class="me-2" style="width: 20px; height: 20px;" onerror="this.style.display='none'">
                                                <span>@order.Carrier.ToUpper()</span>
                                            </div>
                                            @if (!string.IsNullOrEmpty(order.TrackingNumber))
                                            {
                                                <small class="text-muted">@order.TrackingNumber</small>
                                            }
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>
                                    <td>
                                        @if (order.ShippedDate.HasValue)
                                        {
                                            <span>@order.ShippedDate.Value.ToString("dd/MM/yyyy")</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>
                                    <td>
                                        @if (order.EstimatedDelivery.HasValue)
                                        {
                                            <span class="@(order.IsDelayed ? "text-danger" : "")">
                                                @order.EstimatedDelivery.Value.ToString("dd/MM/yyyy")
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" @onclick="() => ViewOrderDetails(order)" title="Voir détails">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            @if (!string.IsNullOrEmpty(order.TrackingNumber))
                                            {
                                                <button class="btn btn-outline-info" @onclick="() => TrackOrder(order)" title="Suivre">
                                                    <i class="fas fa-route"></i>
                                                </button>
                                            }
                                            <button class="btn btn-outline-success" @onclick="() => UpdateStatus(order)" title="Mettre à jour">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-shipping-fast fs-1 text-muted mb-3"></i>
                    <h5 class="text-muted">Aucune commande trouvée</h5>
                    <p class="text-muted">Aucune commande ne correspond aux critères de recherche</p>
                </div>
            }
        </div>
    </div>
</div>

@code {
    private bool isLoading = false;
    private int _sellerId = 0;

    // Statistiques
    private DeliveryStatsDto deliveryStats = new();
    private int inTransitCount => deliveryStats.InTransitCount;
    private int deliveredCount => deliveryStats.DeliveredCount;
    private int delayedCount => deliveryStats.DelayedCount;
    private decimal deliveryRate => deliveryStats.DeliveryRate;

    // Filtres
    private string searchTerm = "";
    private string selectedStatus = "";
    private string selectedCarrier = "";
    private DateTime? startDate;
    private DateTime? endDate;

    // Données
    private List<DeliveryOrderDto> orders = new();
    private List<DeliveryOrderDto> filteredOrders = new();

    protected override async Task OnInitializedAsync()
    {
        await GetCurrentSeller();
        await LoadOrders();
        await LoadDeliveryStats();
    }

    private async Task GetCurrentSeller()
    {
        try
        {
            var user = await AuthService.GetCurrentUserAsync();
            if (user != null)
            {
                _sellerId = user.Id;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de la récupération du vendeur");
        }
    }

    private async Task LoadOrders()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            // Charger les commandes de livraison depuis l'API
            orders = await DeliveryTrackingService.GetDeliveryOrdersAsync(_sellerId);
            filteredOrders = orders;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du chargement des commandes");
            NotificationService.Error("Erreur lors du chargement des commandes");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadDeliveryStats()
    {
        try
        {
            deliveryStats = await DeliveryTrackingService.GetDeliveryStatsAsync(_sellerId);
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du chargement des statistiques");
        }
    }

    private void FilterOrders()
    {
        filteredOrders = orders.Where(o =>
            (string.IsNullOrEmpty(searchTerm) ||
             o.OrderNumber.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
             o.CustomerName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) &&
            (string.IsNullOrEmpty(selectedStatus) || o.Status == selectedStatus) &&
            (string.IsNullOrEmpty(selectedCarrier) || o.Carrier == selectedCarrier) &&
            (!startDate.HasValue || o.ShippedDate >= startDate) &&
            (!endDate.HasValue || o.ShippedDate <= endDate)
        ).ToList();

        StateHasChanged();
    }

    private void ClearFilters()
    {
        searchTerm = "";
        selectedStatus = "";
        selectedCarrier = "";
        startDate = null;
        endDate = null;
        filteredOrders = orders;
        StateHasChanged();
    }

    private async Task RefreshOrders()
    {
        await LoadOrders();
        await LoadDeliveryStats();
        NotificationService.Success("Commandes actualisées");
    }

    private async Task ShowTrackingModal()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Fonctionnalité d'ajout de suivi en cours de développement");
    }

    private async Task ViewOrderDetails(DeliveryOrderDto order)
    {
        await JSRuntime.InvokeVoidAsync("alert", $"Détails de la commande {order.OrderNumber}");
    }

    private async Task TrackOrder(DeliveryOrderDto order)
    {
        try
        {
            var trackingEvents = await DeliveryTrackingService.GetTrackingEventsAsync(order.Id);

            if (trackingEvents.Any())
            {
                var lastEvent = trackingEvents.OrderByDescending(e => e.EventDate).First();
                await JSRuntime.InvokeVoidAsync("alert",
                    $"Suivi de la commande {order.OrderNumber}\n" +
                    $"Numéro de suivi: {order.TrackingNumber}\n" +
                    $"Dernier événement: {lastEvent.Description}\n" +
                    $"Date: {lastEvent.EventDate:dd/MM/yyyy HH:mm}");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert",
                    $"Suivi de la commande {order.OrderNumber}\n" +
                    $"Numéro de suivi: {order.TrackingNumber}\n" +
                    $"Aucun événement de suivi disponible");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du suivi de la commande {OrderId}", order.Id);
            NotificationService.Error("Erreur lors du suivi de la commande");
        }
    }

    private async Task UpdateStatus(DeliveryOrderDto order)
    {
        try
        {
            // Pour l'instant, utiliser une boîte de dialogue simple
            // Dans une vraie application, cela ouvrirait un modal avec les options de statut
            var newStatus = await JSRuntime.InvokeAsync<string>("prompt",
                $"Nouveau statut pour {order.OrderNumber}:", order.Status);

            if (!string.IsNullOrEmpty(newStatus) && newStatus != order.Status)
            {
                var success = await DeliveryTrackingService.UpdateDeliveryStatusAsync(order.Id, newStatus);

                if (success)
                {
                    order.Status = newStatus;
                    StateHasChanged();
                    NotificationService.Success("Statut mis à jour avec succès");
                }
                else
                {
                    NotificationService.Error("Échec de la mise à jour du statut");
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de la mise à jour du statut pour {OrderId}", order.Id);
            NotificationService.Error("Erreur lors de la mise à jour du statut");
        }
    }

    private string FormatCurrency(decimal amount)
    {
        return amount.ToString("N0") + " GNF";
    }

    private string GetStatusColor(string status)
    {
        return status switch
        {
            "pending" => "secondary",
            "processing" => "info",
            "shipped" => "primary",
            "in_transit" => "warning",
            "delivered" => "success",
            "delayed" => "danger",
            _ => "light"
        };
    }

    private string GetStatusText(string status)
    {
        return status switch
        {
            "pending" => "En attente",
            "processing" => "En traitement",
            "shipped" => "Expédiée",
            "in_transit" => "En transit",
            "delivered" => "Livrée",
            "delayed" => "En retard",
            _ => status
        };
    }

}
