using NafaPlace.ChatEcommerce.Domain.Entities;
using NafaPlace.ChatEcommerce.Application.DTOs;

namespace NafaPlace.ChatEcommerce.Application.Repositories;

public interface IConversationRepository
{
    // Gestion des conversations
    Task<int> CreateAsync(Conversation conversation);
    Task<Conversation?> GetByIdAsync(int id);
    Task<List<Conversation>> GetAllAsync();
    Task<List<Conversation>> GetByFilterAsync(ConversationFilterDto filter);
    Task<List<Conversation>> GetByUserIdAsync(string userId);
    Task<List<Conversation>> GetBySellerIdAsync(string sellerId);
    Task<List<Conversation>> GetByProductIdAsync(int productId);
    Task<List<Conversation>> GetByOrderIdAsync(int orderId);
    Task<bool> UpdateAsync(Conversation conversation);
    Task<bool> DeleteAsync(int id);
    
    // Recherche et filtrage
    Task<List<Conversation>> SearchAsync(string query, ConversationFilterDto? filter = null);
    Task<int> CountAsync(ConversationFilterDto? filter = null);
    Task<int> CountUnreadAsync(string userId);
    
    // Statistiques
    Task<Dictionary<string, int>> GetConversationsByStatusAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<Dictionary<string, int>> GetConversationsByTypeAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<double> GetAverageResponseTimeAsync(string? sellerId = null, DateTime? startDate = null, DateTime? endDate = null);
    Task<double> GetAverageResolutionTimeAsync(string? sellerId = null, DateTime? startDate = null, DateTime? endDate = null);
}

public interface IMessageRepository
{
    // Gestion des messages
    Task<int> CreateAsync(Message message);
    Task<Message?> GetByIdAsync(int id);
    Task<List<Message>> GetByConversationIdAsync(int conversationId, int page = 1, int pageSize = 50);
    Task<bool> UpdateAsync(Message message);
    Task<bool> DeleteAsync(int id);
    
    // Marquer comme lu
    Task<bool> MarkAsReadAsync(int messageId, string userId);
    Task<bool> MarkConversationAsReadAsync(int conversationId, string userId);
    
    // Recherche
    Task<List<Message>> SearchAsync(string query, int? conversationId = null);
    Task<int> CountUnreadAsync(string userId);
    Task<int> CountByConversationAsync(int conversationId);
}

public interface IQuickReplyRepository
{
    Task<List<QuickReply>> GetAllAsync();
    Task<List<QuickReply>> GetByCategoryAsync(string category);
    Task<List<QuickReply>> GetActiveAsync();
    Task<QuickReply?> GetByIdAsync(int id);
    Task<int> CreateAsync(QuickReply quickReply);
    Task<bool> UpdateAsync(QuickReply quickReply);
    Task<bool> DeleteAsync(int id);
}

public interface IFAQRepository
{
    Task<List<FAQ>> GetAllAsync();
    Task<List<FAQ>> GetByCategoryAsync(string category);
    Task<List<FAQ>> GetActiveAsync();
    Task<List<FAQ>> SearchAsync(string query);
    Task<FAQ?> GetByIdAsync(int id);
    Task<int> CreateAsync(FAQ faq);
    Task<bool> UpdateAsync(FAQ faq);
    Task<bool> DeleteAsync(int id);
    Task<bool> RecordFeedbackAsync(int faqId, bool isHelpful);
}

public interface IChatSessionRepository
{
    Task<ChatSession?> GetBySessionIdAsync(string sessionId);
    Task<ChatSession?> GetByUserIdAsync(string userId);
    Task<List<ChatSession>> GetOnlineUsersAsync();
    Task<int> CreateAsync(ChatSession session);
    Task<bool> UpdateAsync(ChatSession session);
    Task<bool> UpdatePresenceAsync(string userId, bool isOnline);
    Task<bool> DeleteAsync(int id);
    Task<bool> CleanupInactiveSessionsAsync(TimeSpan inactiveThreshold);
}
