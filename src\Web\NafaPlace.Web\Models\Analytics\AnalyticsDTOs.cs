using System.ComponentModel.DataAnnotations;

namespace NafaPlace.Web.Models.Analytics
{
    public class ComprehensiveDashboardDto
    {
        public SalesPredictionDto SalesPrediction { get; set; } = new();
        public CustomerChurnAnalysisDto ChurnAnalysis { get; set; } = new();
        public List<AIInsightDto> AIInsights { get; set; } = new();
        public RealTimeTrendsDto RealTimeTrends { get; set; } = new();
        public AICustomerSegmentationDto CustomerSegmentation { get; set; } = new();
        public BusinessOptimizationDto BusinessOptimization { get; set; } = new();

        // Additional properties used in components
        public decimal TotalRevenue { get; set; }
        public decimal RevenueGrowth { get; set; }
        public int TotalOrders { get; set; }
        public decimal OrderGrowth { get; set; }
        public int ActiveCustomers { get; set; }
        public decimal CustomerGrowth { get; set; }
        public decimal AverageOrderValue { get; set; }
        public decimal ConversionRate { get; set; }
        public decimal CustomerLifetimeValue { get; set; }
        public decimal CustomerRetentionRate { get; set; }
        public decimal ChurnRate { get; set; }
        public decimal NetPromoterScore { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public DateTime GeneratedAt { get; set; }

        public List<CategoryPerformanceDto> CategoryPerformance { get; set; } = new();
        public List<TopProductDto> TopProducts { get; set; } = new();
        public List<GeographicPerformanceDto> GeographicData { get; set; } = new();
        public List<RevenueTimeSeriesDto> RevenueTimeSeries { get; set; } = new();
    }

    public class SalesPredictionDto
    {
        public decimal PredictedRevenue { get; set; }
        public decimal PredictedGrowth { get; set; }
        public List<MonthlyPredictionDto> MonthlyPredictions { get; set; } = new();
        public string Confidence { get; set; } = string.Empty;
        public DateTime PredictionDate { get; set; }
        public decimal LowerBound { get; set; }
        public decimal UpperBound { get; set; }
        public int PredictedOrders { get; set; }
        public decimal ConfidenceLevel { get; set; }
        public string Trend { get; set; } = string.Empty;
    }

    public class MonthlyPredictionDto
    {
        public string Month { get; set; } = string.Empty;
        public decimal Revenue { get; set; }
        public decimal Growth { get; set; }
    }

    public class CustomerChurnAnalysisDto
    {
        public double ChurnRate { get; set; }
        public int AtRiskCustomers { get; set; }
        public List<ChurnFactorDto> ChurnFactors { get; set; } = new();
        public List<RetentionRecommendationDto> Recommendations { get; set; } = new();
        public decimal OverallChurnRate { get; set; }
        public List<ChurnRiskCustomerDto> HighRiskCustomers { get; set; } = new();
    }

    public class ChurnFactorDto
    {
        public string Factor { get; set; } = string.Empty;
        public double Impact { get; set; }
        public decimal Importance { get; set; }
    }

    public class RetentionRecommendationDto
    {
        public string Recommendation { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
    }

    public class AIInsightDto
    {
        public List<InsightDto> Insights { get; set; } = new();
        public List<RecommendationDto> Recommendations { get; set; } = new();
        public double ConfidenceScore { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public decimal Confidence { get; set; }
    }

    public class InsightDto
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public double Impact { get; set; }
    }

    public class RecommendationDto
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
    }

    public class RealTimeTrendsDto
    {
        public List<TrendDto> Trends { get; set; } = new();
        public List<AlertDto> Alerts { get; set; } = new();
        public DateTime LastUpdated { get; set; }
        public List<TrendingProductDto> TrendingProducts { get; set; } = new();
        public List<SearchTrendDto> SearchTrends { get; set; } = new();
        public List<AlertDto> ActiveAlerts { get; set; } = new();
    }

    public class TrendDto
    {
        public string Name { get; set; } = string.Empty;
        public string Direction { get; set; } = string.Empty;
        public double Change { get; set; }
        public string Period { get; set; } = string.Empty;
    }



    public class AICustomerSegmentationDto
    {
        public List<CustomerSegmentDto> Segments { get; set; } = new();
        public List<SegmentInsightDto> Insights { get; set; } = new();
    }

    public class CustomerSegmentDto
    {
        public string Name { get; set; } = string.Empty;
        public int CustomerCount { get; set; }
        public decimal AverageValue { get; set; }
        public string Characteristics { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal Percentage { get; set; }
        public decimal AverageOrderValue { get; set; }
        public decimal TotalRevenue { get; set; }
    }

    public class SegmentInsightDto
    {
        public string Segment { get; set; } = string.Empty;
        public string Insight { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty;
    }

    public class BusinessOptimizationDto
    {
        public List<OptimizationOpportunityDto> Opportunities { get; set; } = new();
        public decimal PotentialSavings { get; set; }
        public decimal PotentialRevenue { get; set; }
        public string Area { get; set; } = string.Empty;
        public string Recommendation { get; set; } = string.Empty;
        public decimal EstimatedImpact { get; set; }
        public string Priority { get; set; } = string.Empty;
        public List<string> Actions { get; set; } = new();
    }

    public class OptimizationOpportunityDto
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal Impact { get; set; }
        public string Difficulty { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
    }

    public class ChurnRiskCustomerDto
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public decimal RiskScore { get; set; }
        public string RiskLevel { get; set; } = string.Empty;
        public decimal ChurnProbability { get; set; }
        public decimal CustomerValue { get; set; }
        public int DaysSinceLastOrder { get; set; }
    }

    public class TrendingProductDto
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public int TrendScore { get; set; }
        public decimal GrowthRate { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
    }

    public class SearchTrendDto
    {
        public string SearchTerm { get; set; } = string.Empty;
        public int SearchCount { get; set; }
        public decimal GrowthRate { get; set; }
        public string Term { get; set; } = string.Empty;
        public int Volume { get; set; }
    }

    public class AlertDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Severity { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
    }

    public class CategoryPerformanceDto
    {
        public int CategoryId { get; set; }
        public string CategoryName { get; set; } = string.Empty;
        public decimal Revenue { get; set; }
        public decimal GrowthRate { get; set; }
        public int ProductCount { get; set; }
        public string Category { get; set; } = string.Empty;
        public int Orders { get; set; }
    }

    public class TopProductDto
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public decimal Revenue { get; set; }
        public int SalesCount { get; set; }
        public decimal GrowthRate { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int UnitsSold { get; set; }
    }

    public class GeographicPerformanceDto
    {
        public string Region { get; set; } = string.Empty;
        public decimal Revenue { get; set; }
        public int OrderCount { get; set; }
        public decimal GrowthRate { get; set; }
        public int Customers { get; set; }
        public int Orders { get; set; }
    }

    public class RevenueTimeSeriesDto
    {
        public DateTime Date { get; set; }
        public decimal Revenue { get; set; }
        public int OrderCount { get; set; }
    }
}
