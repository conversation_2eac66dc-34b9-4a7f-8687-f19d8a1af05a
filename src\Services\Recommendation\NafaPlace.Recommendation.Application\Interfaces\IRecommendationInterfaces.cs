using NafaPlace.Recommendation.Application.DTOs;

namespace NafaPlace.Recommendation.Application.Interfaces;

// Interfaces des services spécialisés
public interface ICollaborativeFilteringService
{
    Task<List<SimilarProductDto>> GetRecommendationsAsync(string userId, int limit);
    Task<List<SimilarProductDto>> GetSimilarProductsAsync(int productId, string userId, int limit);
}

public interface IContentBasedService
{
    Task<List<SimilarProductDto>> GetRecommendationsAsync(string userId, int limit);
    Task<List<SimilarProductDto>> GetSimilarProductsAsync(int productId, int limit);
}

public interface IMLModelService
{
    Task UpdateModelsWithInteractionAsync(UserInteractionDto interaction);
    Task<Dictionary<string, double>> PredictUserPreferencesAsync(string userId);
}

public interface IRecommendationRepository
{
    Task<bool> RecordInteractionAsync(UserInteractionDto interaction);
    Task<UserPreferenceDto?> GetUserPreferencesAsync(string userId);
    Task<PersonalizationProfileDto?> GetPersonalizationProfileAsync(string userId);
    Task<ProductRecommendationDto?> GetProductDetailsAsync(int productId);
    Task<List<FrequentItemDto>> GetFrequentlyBoughtTogetherAsync(int productId, int limit);
    Task<bool> UpdateUserPreferencesAsync(UserPreferenceDto preferences);
    Task<bool> CreateUserPreferencesAsync(UserPreferenceDto preferences);
    Task<bool> CreatePersonalizationProfileAsync(PersonalizationProfileDto profile);
    Task<bool> InvalidateUserCacheAsync(string userId);
}
