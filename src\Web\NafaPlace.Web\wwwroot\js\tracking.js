// Service de suivi GPS et cartographie pour NafaPlace
// Gère la géolocalisation, les cartes et la navigation en temps réel

class TrackingService {
    constructor() {
        this.maps = new Map();
        this.watchId = null;
        this.isWatching = false;
        this.currentPosition = null;
        this.markers = new Map();
        this.routes = new Map();

        // Configuration pour Conakry, Guinée
        this.defaultCenter = { lat: 9.6412, lng: -13.5784 };
        this.defaultZoom = 13;

        this.initializeGoogleMaps();
    }

    // Initialiser Google Maps
    async initializeGoogleMaps() {
        if (typeof google === 'undefined') {
            console.warn('Google Maps non disponible, chargement de Leaflet en fallback');
            this.useLeaflet = true;
            await this.loadLeaflet();
        } else {
            this.useLeaflet = false;
        }
    }

    // Charger Leaflet comme alternative
    async loadLeaflet() {
        if (typeof L === 'undefined') {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
            document.head.appendChild(link);

            const script = document.createElement('script');
            script.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
            document.head.appendChild(script);

            await new Promise(resolve => {
                script.onload = resolve;
            });
        }
    }

    // Initialiser une carte de suivi pour les clients
    async initializeTrackingMap(containerId) {
        try {
            const container = document.getElementById(containerId);
            if (!container) {
                console.error(`Container ${containerId} not found`);
                return;
            }

            let map;

            if (this.useLeaflet) {
                map = L.map(containerId).setView([this.defaultCenter.lat, this.defaultCenter.lng], this.defaultZoom);

                // Ajouter la couche de tuiles OpenStreetMap
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors'
                }).addTo(map);

                // Styles personnalisés pour Conakry
                this.addConakryOverlays(map);

            } else {
                map = new google.maps.Map(container, {
                    zoom: this.defaultZoom,
                    center: this.defaultCenter,
                    mapTypeId: google.maps.MapTypeId.ROADMAP,
                    styles: this.getConakryMapStyles(),
                    mapTypeControl: true,
                    streetViewControl: false,
                    fullscreenControl: true,
                    zoomControl: true
                });
            }

            this.maps.set(containerId, map);
            console.log(`Carte initialisée pour ${containerId}`);

            return map;
        } catch (error) {
            console.error('Erreur lors de l\'initialisation de la carte:', error);
            throw error;
        }
    }

    // Initialiser une carte pour les livreurs
    async initializeDeliveryPersonMap(containerId) {
        const map = await this.initializeTrackingMap(containerId);

        if (map) {
            // Ajouter des fonctionnalités spécifiques aux livreurs
            this.addDeliveryPersonFeatures(map, containerId);
        }

        return map;
    }

    // Ajouter des fonctionnalités pour les livreurs
    addDeliveryPersonFeatures(map, containerId) {
        if (this.useLeaflet) {
            // Ajouter contrôles personnalisés pour Leaflet
            const locationControl = L.control({ position: 'topright' });
            locationControl.onAdd = function() {
                const div = L.DomUtil.create('div', 'leaflet-bar leaflet-control-custom');
                div.innerHTML = '<button onclick="trackingService.centerOnCurrentLocation(\'' + containerId + '\')">📍</button>';
                div.style.backgroundColor = 'white';
                div.style.width = '40px';
                div.style.height = '40px';
                div.style.cursor = 'pointer';
                return div;
            };
            locationControl.addTo(map);

        } else {
            // Ajouter contrôles personnalisés pour Google Maps
            const locationButton = document.createElement('button');
            locationButton.textContent = '📍 Ma position';
            locationButton.classList.add('btn', 'btn-primary', 'btn-sm');
            locationButton.style.margin = '10px';

            locationButton.addEventListener('click', () => {
                this.centerOnCurrentLocation(containerId);
            });

            map.controls[google.maps.ControlPosition.TOP_RIGHT].push(locationButton);
        }
    }

    // Obtenir les styles de carte personnalisés pour Conakry
    getConakryMapStyles() {
        return [
            {
                featureType: 'water',
                elementType: 'geometry',
                stylers: [{ color: '#a2daf2' }]
            },
            {
                featureType: 'landscape.natural',
                elementType: 'geometry',
                stylers: [{ color: '#f5f5f2' }]
            },
            {
                featureType: 'road',
                elementType: 'geometry',
                stylers: [{ color: '#ffffff' }]
            },
            {
                featureType: 'poi',
                elementType: 'labels',
                stylers: [{ visibility: 'simplified' }]
            }
        ];
    }

    // Ajouter des overlays pour Conakry avec Leaflet
    addConakryOverlays(map) {
        // Marquer les zones importantes de Conakry
        const importantAreas = [
            { name: 'Kaloum', lat: 9.5092, lng: -13.7122 },
            { name: 'Matam', lat: 9.6355, lng: -13.5784 },
            { name: 'Dixinn', lat: 9.6823, lng: -13.6311 },
            { name: 'Ratoma', lat: 9.6962, lng: -13.6266 },
            { name: 'Matoto', lat: 9.6540, lng: -13.6203 }
        ];

        importantAreas.forEach(area => {
            L.circle([area.lat, area.lng], {
                color: '#667eea',
                fillColor: '#667eea',
                fillOpacity: 0.1,
                radius: 2000
            }).addTo(map).bindPopup(`Commune de ${area.name}`);
        });
    }

    // Mettre à jour la position du livreur
    async updateDeliveryPersonPosition(containerId, latitude, longitude, options = {}) {
        const map = this.maps.get(containerId);
        if (!map) return;

        const position = { lat: latitude, lng: longitude };

        try {
            if (this.useLeaflet) {
                // Gérer avec Leaflet
                let marker = this.markers.get(`${containerId}_delivery_person`);

                if (!marker) {
                    // Créer un nouveau marqueur pour le livreur
                    const deliveryIcon = L.divIcon({
                        html: '<div class="delivery-person-marker">🚴</div>',
                        className: 'custom-div-icon',
                        iconSize: [30, 30],
                        iconAnchor: [15, 15]
                    });

                    marker = L.marker([latitude, longitude], { icon: deliveryIcon }).addTo(map);
                    this.markers.set(`${containerId}_delivery_person`, marker);

                    // Ajouter popup d'informations
                    marker.bindPopup(`
                        <div class="delivery-person-popup">
                            <strong>Votre livreur</strong><br>
                            Position: ${latitude.toFixed(4)}, ${longitude.toFixed(4)}<br>
                            ${options.speed ? `Vitesse: ${options.speed} km/h<br>` : ''}
                            Mise à jour: ${new Date().toLocaleTimeString()}
                        </div>
                    `);
                } else {
                    // Mettre à jour la position existante
                    marker.setLatLng([latitude, longitude]);

                    // Animation fluide
                    marker.setLatLng([latitude, longitude], {
                        duration: 1000
                    });
                }

            } else {
                // Gérer avec Google Maps
                let marker = this.markers.get(`${containerId}_delivery_person`);

                if (!marker) {
                    marker = new google.maps.Marker({
                        position: position,
                        map: map,
                        title: 'Votre livreur',
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="15" cy="15" r="12" fill="#28a745" stroke="#fff" stroke-width="2"/>
                                    <text x="15" y="20" text-anchor="middle" fill="white" font-size="12">🚴</text>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(30, 30),
                            anchor: new google.maps.Point(15, 15)
                        }
                    });

                    this.markers.set(`${containerId}_delivery_person`, marker);

                    // InfoWindow avec détails
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div class="delivery-person-popup">
                                <strong>Votre livreur</strong><br>
                                Position: ${latitude.toFixed(4)}, ${longitude.toFixed(4)}<br>
                                ${options.speed ? `Vitesse: ${options.speed} km/h<br>` : ''}
                                Mise à jour: ${new Date().toLocaleTimeString()}
                            </div>
                        `
                    });

                    marker.addListener('click', () => {
                        infoWindow.open(map, marker);
                    });
                } else {
                    // Animation fluide vers la nouvelle position
                    this.animateMarker(marker, position);
                }
            }

            // Sauvegarder la position actuelle
            this.currentPosition = { latitude, longitude };

        } catch (error) {
            console.error('Erreur lors de la mise à jour de position:', error);
        }
    }

    // Animer un marqueur vers une nouvelle position (Google Maps)
    animateMarker(marker, newPosition) {
        const startPosition = marker.getPosition();
        const startLat = startPosition.lat();
        const startLng = startPosition.lng();

        const deltaLat = newPosition.lat - startLat;
        const deltaLng = newPosition.lng - startLng;

        let step = 0;
        const steps = 20;
        const stepTime = 50; // 50ms par étape = 1 seconde d'animation

        const animate = () => {
            step++;
            const progress = step / steps;

            const currentLat = startLat + (deltaLat * progress);
            const currentLng = startLng + (deltaLng * progress);

            marker.setPosition({ lat: currentLat, lng: currentLng });

            if (step < steps) {
                setTimeout(animate, stepTime);
            }
        };

        animate();
    }

    // Centrer la carte sur la position du livreur
    async centerMapOnPosition(containerId, latitude, longitude) {
        const map = this.maps.get(containerId);
        if (!map) return;

        const position = { lat: latitude, lng: longitude };

        if (this.useLeaflet) {
            map.setView([latitude, longitude], this.defaultZoom, {
                animate: true,
                duration: 1
            });
        } else {
            map.panTo(position);
            map.setZoom(16);
        }
    }

    // Centrer sur la position actuelle
    async centerOnCurrentLocation(containerId) {
        if (this.currentPosition) {
            await this.centerMapOnPosition(
                containerId,
                this.currentPosition.latitude,
                this.currentPosition.longitude
            );
        } else {
            this.getCurrentPosition()
                .then(position => {
                    this.centerMapOnPosition(containerId, position.latitude, position.longitude);
                })
                .catch(error => {
                    console.error('Impossible d\'obtenir la position actuelle:', error);
                });
        }
    }

    // Afficher l'itinéraire complet
    async showFullRoute(containerId, waypoints = []) {
        const map = this.maps.get(containerId);
        if (!map) return;

        try {
            if (this.useLeaflet) {
                // Utiliser Leaflet Routing Machine
                if (typeof L.Routing !== 'undefined') {
                    const routingControl = L.Routing.control({
                        waypoints: waypoints.map(wp => L.latLng(wp.lat, wp.lng)),
                        routeWhileDragging: false,
                        addWaypoints: false,
                        draggableWaypoints: false,
                        fitSelectedRoutes: true,
                        showAlternatives: false
                    }).addTo(map);

                    this.routes.set(containerId, routingControl);
                }
            } else {
                // Utiliser Google Maps Directions
                const directionsService = new google.maps.DirectionsService();
                const directionsRenderer = new google.maps.DirectionsRenderer({
                    draggable: false,
                    panel: null
                });

                directionsRenderer.setMap(map);

                if (waypoints.length >= 2) {
                    const origin = waypoints[0];
                    const destination = waypoints[waypoints.length - 1];
                    const intermediateWaypoints = waypoints.slice(1, -1).map(wp => ({
                        location: wp,
                        stopover: true
                    }));

                    directionsService.route({
                        origin: origin,
                        destination: destination,
                        waypoints: intermediateWaypoints,
                        travelMode: google.maps.TravelMode.DRIVING,
                        optimizeWaypoints: true
                    }, (result, status) => {
                        if (status === 'OK') {
                            directionsRenderer.setDirections(result);
                            this.routes.set(containerId, directionsRenderer);
                        }
                    });
                }
            }
        } catch (error) {
            console.error('Erreur lors de l\'affichage de l\'itinéraire:', error);
        }
    }

    // Basculer l'affichage du trafic
    async toggleTrafficLayer(containerId, show) {
        const map = this.maps.get(containerId);
        if (!map) return;

        if (!this.useLeaflet && google.maps.TrafficLayer) {
            let trafficLayer = this.markers.get(`${containerId}_traffic`);

            if (show && !trafficLayer) {
                trafficLayer = new google.maps.TrafficLayer();
                trafficLayer.setMap(map);
                this.markers.set(`${containerId}_traffic`, trafficLayer);
            } else if (!show && trafficLayer) {
                trafficLayer.setMap(null);
                this.markers.delete(`${containerId}_traffic`);
            }
        }
    }

    // Mettre à jour la position de la carte du livreur
    async updateDeliveryPersonMapPosition(containerId, latitude, longitude) {
        await this.updateDeliveryPersonPosition(containerId, latitude, longitude);

        // Centrer automatiquement sur la nouvelle position
        await this.centerMapOnPosition(containerId, latitude, longitude);
    }

    // Géolocalisation HTML5
    async getCurrentPosition() {
        return new Promise((resolve, reject) => {
            if (!navigator.geolocation) {
                reject(new Error('Géolocalisation non supportée'));
                return;
            }

            navigator.geolocation.getCurrentPosition(
                position => {
                    resolve({
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude,
                        accuracy: position.coords.accuracy,
                        speed: position.coords.speed,
                        heading: position.coords.heading
                    });
                },
                error => {
                    reject(new Error(`Erreur de géolocalisation: ${error.message}`));
                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 60000
                }
            );
        });
    }

    // Surveiller la position en continu
    startWatchingPosition(onSuccess, onError) {
        if (!navigator.geolocation) {
            onError('Géolocalisation non supportée');
            return;
        }

        if (this.isWatching) {
            this.stopWatchingPosition();
        }

        this.watchId = navigator.geolocation.watchPosition(
            position => {
                const positionData = {
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude,
                    accuracy: position.coords.accuracy,
                    speed: position.coords.speed,
                    heading: position.coords.heading,
                    timestamp: position.timestamp
                };

                this.currentPosition = positionData;
                onSuccess(positionData.latitude, positionData.longitude, positionData.accuracy);
            },
            error => {
                onError(`Erreur de géolocalisation: ${error.message}`);
            },
            {
                enableHighAccuracy: true,
                timeout: 15000,
                maximumAge: 30000
            }
        );

        this.isWatching = true;
    }

    // Arrêter la surveillance de position
    stopWatchingPosition() {
        if (this.watchId !== null) {
            navigator.geolocation.clearWatch(this.watchId);
            this.watchId = null;
            this.isWatching = false;
        }
    }

    // Navigation externe
    async openNavigation(latitude, longitude, address) {
        const destination = `${latitude},${longitude}`;

        // Détecter le type d'appareil et l'application de navigation préférée
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        const isAndroid = /Android/.test(navigator.userAgent);

        let navigationUrl;

        if (isIOS) {
            // Essayer d'ouvrir Apple Maps en premier
            navigationUrl = `maps://maps.google.com/maps?daddr=${destination}`;

            // Fallback vers Google Maps
            setTimeout(() => {
                window.open(`https://maps.google.com/maps?daddr=${destination}`, '_blank');
            }, 500);

        } else if (isAndroid) {
            // Google Maps sur Android
            navigationUrl = `google.navigation:q=${destination}`;

            // Fallback vers Google Maps web
            setTimeout(() => {
                window.open(`https://maps.google.com/maps?daddr=${destination}`, '_blank');
            }, 500);

        } else {
            // Navigateur de bureau - ouvrir Google Maps
            navigationUrl = `https://maps.google.com/maps?daddr=${destination}`;
        }

        try {
            window.open(navigationUrl, '_blank');
        } catch (error) {
            // Fallback final
            window.open(`https://maps.google.com/maps?daddr=${destination}`, '_blank');
        }
    }

    // Effectuer un appel téléphonique
    makePhoneCall(phoneNumber) {
        try {
            window.open(`tel:${phoneNumber}`, '_self');
        } catch (error) {
            console.error('Impossible d\'effectuer l\'appel:', error);

            // Copier le numéro dans le presse-papiers comme fallback
            if (navigator.clipboard) {
                navigator.clipboard.writeText(phoneNumber).then(() => {
                    alert(`Numéro copié: ${phoneNumber}`);
                });
            } else {
                alert(`Numéro de téléphone: ${phoneNumber}`);
            }
        }
    }

    // Calculer la distance entre deux points (formule de Haversine)
    calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 6371; // Rayon de la Terre en km
        const dLat = this.toRadians(lat2 - lat1);
        const dLon = this.toRadians(lon2 - lon1);

        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                  Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
                  Math.sin(dLon / 2) * Math.sin(dLon / 2);

        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return R * c; // Distance en kilomètres
    }

    // Convertir degrés en radians
    toRadians(degrees) {
        return degrees * (Math.PI / 180);
    }

    // Nettoyer les ressources
    cleanup(containerId) {
        if (containerId) {
            this.maps.delete(containerId);
            this.markers.delete(`${containerId}_delivery_person`);
            this.markers.delete(`${containerId}_traffic`);
            this.routes.delete(containerId);
        } else {
            // Nettoyer tout
            this.maps.clear();
            this.markers.clear();
            this.routes.clear();
            this.stopWatchingPosition();
        }
    }
}

// Créer une instance globale du service de suivi
window.trackingService = new TrackingService();

// Fonctions globales pour l'interopérabilité avec Blazor
window.initializeTrackingMap = (containerId) => {
    return window.trackingService.initializeTrackingMap(containerId);
};

window.initializeDeliveryPersonMap = (containerId) => {
    return window.trackingService.initializeDeliveryPersonMap(containerId);
};

window.updateDeliveryPersonPosition = (containerId, latitude, longitude, options) => {
    return window.trackingService.updateDeliveryPersonPosition(containerId, latitude, longitude, options);
};

window.updateDeliveryPersonMapPosition = (containerId, latitude, longitude) => {
    return window.trackingService.updateDeliveryPersonMapPosition(containerId, latitude, longitude);
};

window.centerMapOnPosition = (containerId, latitude, longitude) => {
    return window.trackingService.centerMapOnPosition(containerId, latitude, longitude);
};

window.showFullRoute = (containerId, waypoints) => {
    return window.trackingService.showFullRoute(containerId, waypoints);
};

window.toggleTrafficLayer = (containerId, show) => {
    return window.trackingService.toggleTrafficLayer(containerId, show);
};

window.openNavigation = (latitude, longitude, address) => {
    return window.trackingService.openNavigation(latitude, longitude, address);
};

window.makePhoneCall = (phoneNumber) => {
    return window.trackingService.makePhoneCall(phoneNumber);
};

// Service de géolocalisation pour Blazor
window.geolocationService = {
    getCurrentPosition: () => window.trackingService.getCurrentPosition(),
    startWatching: (onSuccess, onError) => window.trackingService.startWatchingPosition(onSuccess, onError),
    stopWatching: () => window.trackingService.stopWatchingPosition()
};

console.log('Service de suivi GPS NafaPlace initialisé ✅');