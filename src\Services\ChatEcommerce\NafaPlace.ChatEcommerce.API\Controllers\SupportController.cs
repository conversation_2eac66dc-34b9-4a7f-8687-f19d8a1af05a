using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.ChatEcommerce.Application.DTOs;
using NafaPlace.ChatEcommerce.Application.Services;

namespace NafaPlace.ChatEcommerce.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class SupportController : ControllerBase
{
    private readonly IChatEcommerceService _chatService;
    private readonly ILogger<SupportController> _logger;

    public SupportController(IChatEcommerceService chatService, ILogger<SupportController> logger)
    {
        _chatService = chatService;
        _logger = logger;
    }

    /// <summary>
    /// Obtenir toutes les FAQs
    /// </summary>
    [HttpGet("faqs")]
    public async Task<ActionResult<List<FAQDto>>> GetFAQs([FromQuery] string? category = null)
    {
        try
        {
            var faqs = await _chatService.GetFAQsAsync(category);
            return Ok(faqs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des FAQs");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Rechercher dans les FAQs
    /// </summary>
    [HttpGet("faqs/search")]
    public async Task<ActionResult<List<FAQDto>>> SearchFAQs([FromQuery] string query)
    {
        try
        {
            var faqs = await _chatService.SearchFAQsAsync(query);
            return Ok(faqs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche des FAQs");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Enregistrer un feedback sur une FAQ
    /// </summary>
    [HttpPost("faqs/{faqId}/feedback")]
    public async Task<ActionResult<bool>> RecordFAQFeedback(int faqId, [FromBody] FAQFeedbackRequest request)
    {
        try
        {
            var result = await _chatService.RecordFAQFeedbackAsync(faqId, request.IsHelpful);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'enregistrement du feedback FAQ {FaqId}", faqId);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtenir les réponses rapides
    /// </summary>
    [HttpGet("quick-replies")]
    [Authorize]
    public async Task<ActionResult<List<QuickReplyDto>>> GetQuickReplies([FromQuery] string? category = null)
    {
        try
        {
            var replies = await _chatService.GetQuickRepliesAsync(category);
            return Ok(replies);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des réponses rapides");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Créer une nouvelle réponse rapide
    /// </summary>
    [HttpPost("quick-replies")]
    [Authorize(Roles = "Seller,Admin")]
    public async Task<ActionResult<int>> CreateQuickReply([FromBody] CreateQuickReplyDto dto)
    {
        try
        {
            var quickReply = new NafaPlace.ChatEcommerce.Domain.Entities.QuickReply
            {
                Title = dto.Title,
                Content = dto.Content,
                Category = dto.Category,
                Tags = dto.Tags,
                IsActive = true,
                SortOrder = 0,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            var id = await _chatService.CreateQuickReplyAsync(quickReply);
            return CreatedAtAction(nameof(GetQuickReplies), new { id }, id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de la réponse rapide");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Mettre à jour une réponse rapide
    /// </summary>
    [HttpPut("quick-replies/{id}")]
    [Authorize(Roles = "Seller,Admin")]
    public async Task<ActionResult<bool>> UpdateQuickReply(int id, [FromBody] UpdateQuickReplyDto dto)
    {
        try
        {
            var quickReply = new NafaPlace.ChatEcommerce.Domain.Entities.QuickReply
            {
                Id = id,
                Title = dto.Title,
                Content = dto.Content,
                Category = dto.Category,
                Tags = dto.Tags,
                IsActive = dto.IsActive,
                SortOrder = dto.SortOrder,
                UpdatedAt = DateTime.UtcNow
            };

            var result = await _chatService.UpdateQuickReplyAsync(quickReply);
            if (!result)
                return NotFound();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour de la réponse rapide {Id}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Supprimer une réponse rapide
    /// </summary>
    [HttpDelete("quick-replies/{id}")]
    [Authorize(Roles = "Seller,Admin")]
    public async Task<ActionResult<bool>> DeleteQuickReply(int id)
    {
        try
        {
            var result = await _chatService.DeleteQuickReplyAsync(id);
            if (!result)
                return NotFound();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de la réponse rapide {Id}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtenir les utilisateurs en ligne
    /// </summary>
    [HttpGet("online-users")]
    [Authorize]
    public async Task<ActionResult<List<ChatSessionDto>>> GetOnlineUsers()
    {
        try
        {
            var users = await _chatService.GetOnlineUsersAsync();
            return Ok(users);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des utilisateurs en ligne");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Vérifier si un utilisateur est en ligne
    /// </summary>
    [HttpGet("users/{userId}/online")]
    [Authorize]
    public async Task<ActionResult<bool>> IsUserOnline(string userId)
    {
        try
        {
            var isOnline = await _chatService.IsUserOnlineAsync(userId);
            return Ok(isOnline);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification du statut en ligne de l'utilisateur {UserId}", userId);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Mettre à jour la présence d'un utilisateur
    /// </summary>
    [HttpPut("users/{userId}/presence")]
    [Authorize]
    public async Task<ActionResult<bool>> UpdateUserPresence(string userId, [FromBody] UpdatePresenceRequest request)
    {
        try
        {
            var result = await _chatService.UpdateUserPresenceAsync(userId, request.IsOnline);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour de la présence de l'utilisateur {UserId}", userId);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtenir les statistiques des conversations
    /// </summary>
    [HttpGet("stats")]
    [Authorize]
    public async Task<ActionResult<ConversationStatsDto>> GetConversationStats(
        [FromQuery] string? userId = null,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var stats = await _chatService.GetConversationStatsAsync(userId, startDate, endDate);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des statistiques");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtenir les conversations par type
    /// </summary>
    [HttpGet("stats/by-type")]
    [Authorize]
    public async Task<ActionResult<Dictionary<string, int>>> GetConversationsByType(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var stats = await _chatService.GetConversationsByTypeAsync(startDate, endDate);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des conversations par type");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtenir les conversations par statut
    /// </summary>
    [HttpGet("stats/by-status")]
    [Authorize]
    public async Task<ActionResult<Dictionary<string, int>>> GetConversationsByStatus(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var stats = await _chatService.GetConversationsByStatusAsync(startDate, endDate);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des conversations par statut");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }
}

// DTOs pour les requêtes
public class FAQFeedbackRequest
{
    public bool IsHelpful { get; set; }
}

public class UpdatePresenceRequest
{
    public bool IsOnline { get; set; }
}

public class CreateQuickReplyDto
{
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string? Tags { get; set; }
}

public class UpdateQuickReplyDto
{
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string? Tags { get; set; }
    public bool IsActive { get; set; }
    public int SortOrder { get; set; }
}
