/* Featured Products Section - Premium Design */
.featured-products-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    position: relative;
    overflow: hidden;
}

/* Section Header Advanced */
.section-header-advanced {
    margin-bottom: 3rem;
}

.section-badge-premium {
    position: relative;
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #FF6B35, #F59E0B);
    color: white;
    padding: 10px 25px;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 700;
    margin-bottom: 1rem;
    box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
    animation: pulse-glow 2s ease-in-out infinite;
}

.badge-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #FF6B35, #F59E0B);
    border-radius: 50px;
    z-index: -1;
    opacity: 0.5;
    filter: blur(8px);
}

.section-title-premium {
    font-size: 2.8rem;
    font-weight: 900;
    color: #003366;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.text-gradient-fire {
    background: linear-gradient(135deg, #FF6B35, #E73C30, #F59E0B);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.section-subtitle-premium {
    font-size: 1.2rem;
    color: #6c757d;
    line-height: 1.6;
    max-width: 500px;
}

/* Countdown Timer */
.countdown-timer {
    background: linear-gradient(135deg, #003366, #004488);
    color: white;
    padding: 1.5rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 51, 102, 0.3);
}

.timer-label {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.timer-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.timer-unit {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.timer-number {
    font-size: 1.8rem;
    font-weight: 800;
    color: #FFD700;
    line-height: 1;
}

.timer-text {
    font-size: 0.7rem;
    opacity: 0.8;
    text-transform: uppercase;
}

.timer-separator {
    font-size: 1.5rem;
    font-weight: bold;
    color: #FFD700;
    animation: blink 1s infinite;
}

/* Products Loading Skeleton */
.products-loading {
    margin-top: 2rem;
}

.loading-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.product-skeleton {
    background: white;
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.skeleton-image {
    width: 100%;
    height: 200px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    border-radius: 15px;
    margin-bottom: 1rem;
    animation: shimmer 1.5s infinite;
}

.skeleton-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.skeleton-line {
    height: 16px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    border-radius: 8px;
    animation: shimmer 1.5s infinite;
}

.skeleton-title {
    width: 80%;
    height: 20px;
}

.skeleton-price {
    width: 60%;
    height: 18px;
}

.skeleton-button {
    width: 100%;
    height: 40px;
    margin-top: 0.5rem;
}

/* Products Grid Premium */
.products-grid-premium {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

/* Product Card Premium */
.product-card-premium {
    background: white;
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.product-card-premium:hover {
    transform: translateY(-12px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.product-card-premium:hover .product-hover-effect {
    opacity: 1;
    transform: scale(1);
}

/* Product Image Container */
.product-image-container {
    position: relative;
    overflow: hidden;
}

.product-image-wrapper {
    position: relative;
    width: 100%;
    height: 250px;
    overflow: hidden;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.4s ease;
}

.product-card-premium:hover .product-image {
    transform: scale(1.1);
}

/* Discount Badge */
.discount-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    z-index: 3;
}

.discount-percent {
    position: relative;
    background: linear-gradient(135deg, #E73C30, #FF6B35);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 700;
    z-index: 2;
    display: block;
    box-shadow: 0 4px 15px rgba(231, 60, 48, 0.4);
}

.discount-bg {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #E73C30, #FF6B35);
    border-radius: 20px;
    z-index: -1;
    opacity: 0.7;
    filter: blur(6px);
}

/* Image Overlay */
.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 51, 102, 0.8), rgba(249, 99, 2, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.product-card-premium:hover .image-overlay {
    opacity: 1;
}

.quick-actions {
    display: flex;
    gap: 1rem;
}

.quick-action-btn {
    width: 45px;
    height: 45px;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    cursor: pointer;
}

.quick-action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Stock Overlay */
.stock-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.stock-text {
    background: #dc3545;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Product Content */
.product-content {
    padding: 1.5rem;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.stars {
    display: flex;
    gap: 2px;
}

.star {
    color: #FFD700;
    font-size: 0.9rem;
}

.rating-count {
    font-size: 0.8rem;
    color: #6c757d;
}

.product-title {
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    line-height: 1.3;
}

.product-title a {
    color: #003366;
    text-decoration: none;
    transition: color 0.3s ease;
}

.product-card-premium:hover .product-title a {
    color: #F96302;
}

.product-description {
    font-size: 0.9rem;
    color: #6c757d;
    line-height: 1.5;
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Product Pricing */
.product-pricing {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.current-price {
    font-size: 1.3rem;
    font-weight: 800;
    color: #E73C30;
}

.old-price {
    font-size: 1rem;
    color: #6c757d;
    text-decoration: line-through;
}

/* Product Actions */
.product-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.btn-add-to-cart {
    flex: 1;
    background: linear-gradient(135deg, #F96302, #E73C30);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 15px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.btn-add-to-cart:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(249, 99, 2, 0.4);
}

.btn-out-of-stock {
    flex: 1;
    background: #6c757d;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 15px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: not-allowed;
    opacity: 0.7;
}

.btn-wishlist {
    width: 45px;
    height: 45px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 15px;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-wishlist:hover {
    background: #E73C30;
    color: white;
    border-color: #E73C30;
    transform: scale(1.05);
}

/* Button Ripple Effect */
.btn-ripple {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    transform: scale(0);
    transition: transform 0.6s ease;
    border-radius: 15px;
}

.btn-add-to-cart:active .btn-ripple {
    transform: scale(1);
}

/* Product Hover Effect */
.product-hover-effect {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(249, 99, 2, 0.03), rgba(231, 60, 48, 0.03));
    opacity: 0;
    transform: scale(0.9);
    transition: all 0.4s ease;
    border-radius: 24px;
    pointer-events: none;
}

/* Section Footer */
.section-footer {
    margin-top: 3rem;
}

.btn-view-all-premium {
    position: relative;
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #003366, #004488);
    color: white;
    padding: 15px 35px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(0, 51, 102, 0.3);
    overflow: hidden;
}

.btn-view-all-premium:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0, 51, 102, 0.4);
    color: white;
}

.btn-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #003366, #004488);
    border-radius: 50px;
    z-index: -1;
    opacity: 0.5;
    filter: blur(10px);
}

/* Animations */
@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
    }
    50% {
        box-shadow: 0 8px 35px rgba(255, 107, 53, 0.6);
    }
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0.3;
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .products-grid-premium {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

@media (max-width: 768px) {
    .section-title-premium {
        font-size: 2.2rem;
    }
    
    .section-subtitle-premium {
        font-size: 1.1rem;
    }
    
    .countdown-timer {
        margin-top: 2rem;
        padding: 1rem;
    }
    
    .timer-number {
        font-size: 1.5rem;
    }
    
    .products-grid-premium {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .product-image-wrapper {
        height: 220px;
    }
    
    .product-content {
        padding: 1.25rem;
    }
}

@media (max-width: 576px) {
    .section-header-advanced {
        text-align: center;
    }
    
    .section-title-premium {
        font-size: 1.8rem;
    }
    
    .countdown-timer {
        padding: 0.75rem;
    }
    
    .timer-display {
        gap: 0.25rem;
    }
    
    .timer-number {
        font-size: 1.2rem;
    }
    
    .product-image-wrapper {
        height: 200px;
    }
    
    .product-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .btn-wishlist {
        width: 100%;
        height: 40px;
    }
}
