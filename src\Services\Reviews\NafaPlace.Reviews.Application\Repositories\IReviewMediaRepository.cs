using NafaPlace.Reviews.Domain.Models;

namespace NafaPlace.Reviews.Application.Repositories;

/// <summary>
/// Repository pour la gestion des médias de reviews
/// </summary>
public interface IReviewMediaRepository
{
    /// <summary>
    /// Ajoute un nouveau média à une review
    /// </summary>
    Task<ReviewMedia> AddAsync(ReviewMedia media);

    /// <summary>
    /// Obtient tous les médias d'une review
    /// </summary>
    Task<List<ReviewMedia>> GetByReviewIdAsync(int reviewId);

    /// <summary>
    /// Obtient un média par son ID
    /// </summary>
    Task<ReviewMedia?> GetByIdAsync(int id);

    /// <summary>
    /// Supprime un média
    /// </summary>
    Task<bool> DeleteAsync(int id);

    /// <summary>
    /// Met à jour un média
    /// </summary>
    Task<ReviewMedia> UpdateAsync(ReviewMedia media);

    /// <summary>
    /// Obtient les médias par type
    /// </summary>
    Task<List<ReviewMedia>> GetByTypeAsync(string type, int skip = 0, int take = 20);

    /// <summary>
    /// Vérifie si un utilisateur peut modifier un média
    /// </summary>
    Task<bool> CanUserModifyAsync(int mediaId, string userId);

    /// <summary>
    /// Obtient plusieurs médias par leurs IDs
    /// </summary>
    Task<List<ReviewMedia>> GetByIdsAsync(List<int> ids);
}

