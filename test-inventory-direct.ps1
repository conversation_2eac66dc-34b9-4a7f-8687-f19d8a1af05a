# Test direct de l'API Inventory (sans API Gateway)
Write-Host "Test direct de l'API Inventory" -ForegroundColor Green

# 1. Connexion pour obtenir un token
Write-Host "1. Connexion..." -ForegroundColor Yellow
$loginBody = @{
    Username = "<EMAIL>"
    Password = "Admin123!"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/auth/login" -Method Post -Body $loginBody -ContentType "application/json"
    $token = $loginResponse.token
    Write-Host "Connexion reussie" -ForegroundColor Green
} catch {
    Write-Host "Erreur de connexion: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. Test direct de l'API Inventory (port 5244)
Write-Host "2. Test direct de l'API Inventory..." -ForegroundColor Yellow
$updateBody = @{
    NewQuantity = 10
    Reason = "Test direct API Inventory"
    Notes = "Test sans API Gateway"
} | ConvertTo-Json

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

try {
    $updateResponse = Invoke-RestMethod -Uri "http://localhost:5244/api/v1/inventory/products/1/stock" -Method Put -Body $updateBody -Headers $headers
    Write-Host "Mise a jour reussie: $($updateResponse.message)" -ForegroundColor Green
} catch {
    Write-Host "Erreur lors de la mise a jour: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

Write-Host "Test termine" -ForegroundColor Green
