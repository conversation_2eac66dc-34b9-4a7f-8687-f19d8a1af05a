using NafaPlace.Payment.Domain.Enums;

namespace NafaPlace.Payment.Application.Services;

/// <summary>
/// Interface pour le service de paiement mobile
/// Gère les paiements via Orange Money, MTN Money et autres
/// </summary>
public interface IMobilePaymentService
{
    /// <summary>
    /// Initie un paiement mobile
    /// </summary>
    Task<MobilePaymentResponseDto> InitiateMobilePaymentAsync(MobilePaymentRequestDto request);

    /// <summary>
    /// Vérifie le statut d'un paiement mobile
    /// </summary>
    Task<MobilePaymentStatusDto> CheckPaymentStatusAsync(string transactionId, MobilePaymentProvider provider);

    /// <summary>
    /// Annule un paiement mobile en attente
    /// </summary>
    Task<bool> CancelMobilePaymentAsync(string transactionId, MobilePaymentProvider provider);

    /// <summary>
    /// Rembourse un paiement mobile
    /// </summary>
    Task<MobileRefundResponseDto> RefundMobilePaymentAsync(string transactionId, decimal amount, string reason);

    /// <summary>
    /// Obtient l'historique des paiements pour un utilisateur
    /// </summary>
    Task<List<MobilePaymentHistoryDto>> GetPaymentHistoryAsync(string userId, int skip = 0, int take = 20);

    /// <summary>
    /// Vérifie si un numéro de téléphone est valide pour un fournisseur
    /// </summary>
    Task<bool> ValidatePhoneNumberAsync(string phoneNumber, MobilePaymentProvider provider);

    /// <summary>
    /// Obtient les frais de transaction pour un montant donné
    /// </summary>
    Task<decimal> CalculateTransactionFeeAsync(decimal amount, MobilePaymentProvider provider);

    /// <summary>
    /// Initie un paiement (alias pour InitiateMobilePaymentAsync)
    /// </summary>
    Task<MobilePaymentResponseDto> InitiatePaymentAsync(MobilePaymentRequestDto request);

    /// <summary>
    /// Traite un callback de paiement
    /// </summary>
    Task<bool> ProcessCallbackAsync(string transactionId, Dictionary<string, object> callbackData);

    /// <summary>
    /// Obtient l'historique des paiements d'un utilisateur
    /// </summary>
    Task<List<MobilePaymentHistoryDto>> GetUserPaymentHistoryAsync(string userId, int skip = 0, int take = 20);

    /// <summary>
    /// Calcule les frais pour un montant et un fournisseur
    /// </summary>
    Task<decimal> CalculateFeesAsync(decimal amount, MobilePaymentProvider provider);
}

public class MobilePaymentRequestDto
{
    public string OrderId { get; set; } = "";
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "GNF";
    public string PhoneNumber { get; set; } = "";
    public MobilePaymentProvider Provider { get; set; }
    public string Description { get; set; } = "";
    public string? CallbackUrl { get; set; }
    public string? CustomData { get; set; }
    public string? UserId { get; set; }
    public string? ReturnUrl { get; set; }
}

public class MobilePaymentResponseDto
{
    public string TransactionId { get; set; } = "";
    public MobilePaymentStatus Status { get; set; }
    public string Message { get; set; } = "";
    public string? PaymentUrl { get; set; }
    public string? QrCode { get; set; }
    public DateTime CreatedAt { get; set; }
    public bool Success { get; set; }
    public string? Instructions { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public string? ErrorMessage { get; set; }
}

public class MobilePaymentStatusDto
{
    public string TransactionId { get; set; } = "";
    public MobilePaymentStatus Status { get; set; }
    public string StatusMessage { get; set; } = "";
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "";
    public DateTime? CompletedAt { get; set; }
    public string? Reference { get; set; }
    public MobilePaymentProvider Provider { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class MobileRefundResponseDto
{
    public string RefundId { get; set; } = "";
    public string TransactionId { get; set; } = "";
    public decimal RefundAmount { get; set; }
    public MobilePaymentStatus Status { get; set; }
    public string Message { get; set; } = "";
    public DateTime CreatedAt { get; set; }
}

public class MobilePaymentHistoryDto
{
    public string TransactionId { get; set; } = "";
    public string OrderId { get; set; } = "";
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "";
    public MobilePaymentProvider Provider { get; set; }
    public MobilePaymentStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string Description { get; set; } = "";
}

/// <summary>
/// Interface pour la gestion des méthodes de paiement
/// </summary>
public interface IPaymentMethodService
{
    /// <summary>
    /// Obtient les méthodes de paiement disponibles pour un utilisateur
    /// </summary>
    Task<List<PaymentMethodDto>> GetPaymentMethodsAsync(string userId);

    /// <summary>
    /// Ajoute une nouvelle méthode de paiement
    /// </summary>
    Task<PaymentMethodDto> AddPaymentMethodAsync(string userId, AddPaymentMethodDto request);

    /// <summary>
    /// Supprime une méthode de paiement
    /// </summary>
    Task<bool> RemovePaymentMethodAsync(string userId, string paymentMethodId);

    /// <summary>
    /// Met à jour une méthode de paiement
    /// </summary>
    Task<PaymentMethodDto> UpdatePaymentMethodAsync(string userId, string paymentMethodId, UpdatePaymentMethodDto request);

    /// <summary>
    /// Définit une méthode de paiement par défaut
    /// </summary>
    Task<bool> SetDefaultPaymentMethodAsync(string userId, string paymentMethodId);

    /// <summary>
    /// Sauvegarde une méthode de paiement mobile
    /// </summary>
    Task<PaymentMethodDto> SaveMobilePaymentMethodAsync(string userId, MobilePaymentMethodDto request);

    /// <summary>
    /// Obtient les méthodes de paiement d'un utilisateur
    /// </summary>
    Task<List<PaymentMethodDto>> GetUserPaymentMethodsAsync(string userId);

    /// <summary>
    /// Supprime une méthode de paiement
    /// </summary>
    Task<bool> DeletePaymentMethodAsync(string userId, string paymentMethodId);
}

public class PaymentMethodDto
{
    public string Id { get; set; } = "";
    public string UserId { get; set; } = "";
    public string Type { get; set; } = "";
    public string DisplayName { get; set; } = "";
    public string PhoneNumber { get; set; } = "";
    public MobilePaymentProvider Provider { get; set; }
    public bool IsDefault { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastUsedAt { get; set; }
}

public class AddPaymentMethodDto
{
    public string Type { get; set; } = "";
    public string PhoneNumber { get; set; } = "";
    public MobilePaymentProvider Provider { get; set; }
    public string? DisplayName { get; set; }
    public bool SetAsDefault { get; set; } = false;
}

public class UpdatePaymentMethodDto
{
    public string? DisplayName { get; set; }
    public string? PhoneNumber { get; set; }
    public bool? IsActive { get; set; }
}

public class MobilePaymentMethodDto
{
    public string PhoneNumber { get; set; } = "";
    public MobilePaymentProvider Provider { get; set; }
    public string? DisplayName { get; set; }
    public bool SetAsDefault { get; set; } = false;
    public string? UserId { get; set; }
    public bool IsDefault { get; set; } = false;
    public string? Alias { get; set; }
}