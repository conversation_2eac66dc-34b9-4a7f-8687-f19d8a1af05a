@using Microsoft.JSInterop
@inject IJSRuntime JSRuntime

<div class="pwa-install-banner @(IsVisible ? "show" : "hide")" id="pwa-install-banner">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-auto">
                <img src="/images/logo.svg" alt="NafaPlace" class="pwa-icon" />
            </div>
            <div class="col">
                <h6 class="mb-1 text-white">Installer NafaPlace</h6>
                <p class="mb-0 text-white-50 small">
                    Accédez rapidement à NafaPlace depuis votre écran d'accueil
                </p>
            </div>
            <div class="col-auto">
                <button class="btn btn-light btn-sm me-2" @onclick="InstallPWA">
                    <i class="fas fa-download me-1"></i>
                    Installer
                </button>
                <button class="btn btn-outline-light btn-sm" @onclick="DismissBanner">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
    .pwa-install-banner {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(135deg, #E73C30, #F96302);
        padding: 1rem 0;
        z-index: 1050;
        transform: translateY(100%);
        transition: transform 0.3s ease-in-out;
        box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
    }

    .pwa-install-banner.show {
        transform: translateY(0);
    }

    .pwa-install-banner.hide {
        transform: translateY(100%);
    }

    .pwa-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
    }

    .btn-light {
        font-weight: 600;
    }

    .btn-outline-light {
        border-color: rgba(255, 255, 255, 0.5);
    }

    .btn-outline-light:hover {
        background-color: rgba(255, 255, 255, 0.1);
        border-color: white;
    }

    @@media (max-width: 768px) {
        .pwa-install-banner .col {
            text-align: center;
            margin: 0.5rem 0;
        }

        .pwa-install-banner .row {
            flex-direction: column;
        }
    }
</style>

@code {
    [Parameter] public bool IsVisible { get; set; }
    [Parameter] public EventCallback<bool> IsVisibleChanged { get; set; }

    private bool _deferredPrompt = false;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JSRuntime.InvokeVoidAsync("pwaHelper.init", DotNetObjectReference.Create(this));
        }
    }

    [JSInvokable]
    public async Task ShowInstallPrompt()
    {
        IsVisible = true;
        _deferredPrompt = true;
        await IsVisibleChanged.InvokeAsync(IsVisible);
        StateHasChanged();
    }

    [JSInvokable]
    public async Task HideInstallPrompt()
    {
        IsVisible = false;
        await IsVisibleChanged.InvokeAsync(IsVisible);
        StateHasChanged();
    }

    private async Task InstallPWA()
    {
        if (_deferredPrompt)
        {
            await JSRuntime.InvokeVoidAsync("pwaHelper.install");
            await DismissBanner();
        }
    }

    private async Task DismissBanner()
    {
        IsVisible = false;
        await IsVisibleChanged.InvokeAsync(IsVisible);
        await JSRuntime.InvokeVoidAsync("localStorage.setItem", "pwa-dismissed", "true");
        StateHasChanged();
    }
}

<script>
    window.pwaHelper = {
        deferredPrompt: null,
        dotNetHelper: null,

        init: function (dotNetHelper) {
            this.dotNetHelper = dotNetHelper;

            // Check if already dismissed
            const dismissed = localStorage.getItem('pwa-dismissed');
            if (dismissed) return;

            // Check if already installed
            if (window.matchMedia('(display-mode: standalone)').matches) {
                return;
            }

            // Listen for beforeinstallprompt event
            window.addEventListener('beforeinstallprompt', (e) => {
                e.preventDefault();
                this.deferredPrompt = e;
                this.dotNetHelper.invokeMethodAsync('ShowInstallPrompt');
            });

            // Listen for appinstalled event
            window.addEventListener('appinstalled', () => {
                this.deferredPrompt = null;
                this.dotNetHelper.invokeMethodAsync('HideInstallPrompt');
            });
        },

        install: async function () {
            if (this.deferredPrompt) {
                this.deferredPrompt.prompt();
                const { outcome } = await this.deferredPrompt.userChoice;

                if (outcome === 'accepted') {
                    console.log('PWA installed');
                } else {
                    console.log('PWA installation declined');
                }

                this.deferredPrompt = null;
                this.dotNetHelper.invokeMethodAsync('HideInstallPrompt');
            }
        }
    };
</script>