# Test des informations utilisateur
Write-Host "Test des informations utilisateur" -ForegroundColor Green

# 1. Connexion pour obtenir un token
Write-Host "1. Connexion..." -ForegroundColor Yellow
$loginBody = @{
    Username = "<EMAIL>"
    Password = "Kouyate92."
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/auth/login" -Method Post -Body $loginBody -ContentType "application/json"
    $token = $loginResponse.token
    Write-Host "Connexion reussie" -ForegroundColor Green
    Write-Host "Token: $($token.Substring(0, 50))..." -ForegroundColor Cyan
} catch {
    Write-Host "Erreur de connexion: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. Decoder le token JWT pour voir les claims
Write-Host "2. Analyse du token JWT..." -ForegroundColor Yellow
try {
    # Decoder la partie payload du JWT (partie du milieu)
    $tokenParts = $token.Split('.')
    if ($tokenParts.Length -eq 3) {
        $payload = $tokenParts[1]
        # Ajouter du padding si necessaire
        while ($payload.Length % 4 -ne 0) {
            $payload += "="
        }
        $decodedBytes = [System.Convert]::FromBase64String($payload)
        $decodedText = [System.Text.Encoding]::UTF8.GetString($decodedBytes)
        $claims = $decodedText | ConvertFrom-Json
        
        Write-Host "Claims du token:" -ForegroundColor Cyan
        Write-Host "- User ID (sub): $($claims.sub)" -ForegroundColor White
        Write-Host "- Username (name): $($claims.'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name')" -ForegroundColor White
        Write-Host "- Roles (role): $($claims.'http://schemas.microsoft.com/ws/2008/06/identity/claims/role')" -ForegroundColor White
        Write-Host "- SellerId: $($claims.SellerId)" -ForegroundColor White
        Write-Host "- Expiration: $($claims.exp)" -ForegroundColor White
        Write-Host "Tous les claims:" -ForegroundColor Yellow
        $claims | ConvertTo-Json -Depth 3
    }
} catch {
    Write-Host "Erreur lors du decodage du token: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. Test d'un endpoint protege
Write-Host "3. Test d'acces a un endpoint protege..." -ForegroundColor Yellow
$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

try {
    $userInfo = Invoke-RestMethod -Uri "http://localhost:5000/api/auth/me" -Method Get -Headers $headers
    Write-Host "Informations utilisateur:" -ForegroundColor Cyan
    Write-Host "- ID: $($userInfo.id)" -ForegroundColor White
    Write-Host "- Username: $($userInfo.username)" -ForegroundColor White
    Write-Host "- Email: $($userInfo.email)" -ForegroundColor White
    Write-Host "- Roles: $($userInfo.roles -join ', ')" -ForegroundColor White
} catch {
    Write-Host "Erreur lors de la recuperation des infos utilisateur: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

Write-Host "Test termine" -ForegroundColor Green
