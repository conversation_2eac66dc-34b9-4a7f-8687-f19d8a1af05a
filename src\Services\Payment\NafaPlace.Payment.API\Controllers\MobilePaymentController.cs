using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Payment.Application.Services;
using NafaPlace.Payment.Domain.Enums;
using NafaPlace.Payment.API.Models;
using System.Security.Claims;
using static NafaPlace.Payment.Application.Services.IMobilePaymentService;

namespace NafaPlace.Payment.API.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class MobilePaymentController : ControllerBase
{
    private readonly IMobilePaymentService _mobilePaymentService;
    private readonly IPaymentMethodService _paymentMethodService;
    private readonly ILogger<MobilePaymentController> _logger;

    public MobilePaymentController(
        IMobilePaymentService mobilePaymentService,
        IPaymentMethodService paymentMethodService,
        ILogger<MobilePaymentController> logger)
    {
        _mobilePaymentService = mobilePaymentService;
        _paymentMethodService = paymentMethodService;
        _logger = logger;
    }

    /// <summary>
    /// Initier un paiement Orange Money
    /// </summary>
    [HttpPost("orange-money/initiate")]
    [Authorize]
    public async Task<IActionResult> InitiateOrangeMoneyPayment([FromBody] OrangeMoneyPaymentRequest request)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            // Validation des données d'entrée
            if (!IsValidGuineanPhoneNumber(request.PhoneNumber))
                return BadRequest(new { Message = "Numéro de téléphone Orange Money invalide" });

            if (request.Amount <= 0)
                return BadRequest(new { Message = "Montant invalide" });

            var paymentRequest = new MobilePaymentRequestDto
            {
                UserId = userId,
                Provider = MobilePaymentProvider.OrangeMoney,
                PhoneNumber = request.PhoneNumber,
                Amount = request.Amount,
                Currency = "GNF",
                OrderId = request.OrderId,
                Description = request.Description ?? $"Paiement NafaPlace #{request.OrderId}",
                CallbackUrl = $"{Request.Scheme}://{Request.Host}/api/v1/mobilepayment/orange-money/callback",
                ReturnUrl = request.ReturnUrl
            };

            var result = await _mobilePaymentService.InitiatePaymentAsync(paymentRequest);

            if (result.Success)
            {
                return Ok(new
                {
                    Success = true,
                    TransactionId = result.TransactionId,
                    PaymentUrl = result.PaymentUrl,
                    Instructions = result.Instructions,
                    ExpiresAt = result.ExpiresAt,
                    Message = "Paiement initié avec succès. Vérifiez votre téléphone pour confirmer."
                });
            }

            return BadRequest(new
            {
                Success = false,
                Message = result.ErrorMessage ?? "Erreur lors de l'initiation du paiement Orange Money"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'initiation du paiement Orange Money pour l'utilisateur {UserId}", User.Identity?.Name);
            return StatusCode(500, new { Success = false, Message = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Initier un paiement MTN Mobile Money
    /// </summary>
    [HttpPost("mtn-money/initiate")]
    [Authorize]
    public async Task<IActionResult> InitiateMtnMoneyPayment([FromBody] MtnMoneyPaymentRequest request)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            // Validation des données d'entrée
            if (!IsValidGuineanPhoneNumber(request.PhoneNumber))
                return BadRequest(new { Message = "Numéro de téléphone MTN Money invalide" });

            if (request.Amount <= 0)
                return BadRequest(new { Message = "Montant invalide" });

            var paymentRequest = new MobilePaymentRequestDto
            {
                UserId = userId,
                Provider = MobilePaymentProvider.MtnMoney,
                PhoneNumber = request.PhoneNumber,
                Amount = request.Amount,
                Currency = "GNF",
                OrderId = request.OrderId,
                Description = request.Description ?? $"Paiement NafaPlace #{request.OrderId}",
                CallbackUrl = $"{Request.Scheme}://{Request.Host}/api/v1/mobilepayment/mtn-money/callback",
                ReturnUrl = request.ReturnUrl
            };

            var result = await _mobilePaymentService.InitiatePaymentAsync(paymentRequest);

            if (result.Success)
            {
                return Ok(new
                {
                    Success = true,
                    TransactionId = result.TransactionId,
                    PaymentUrl = result.PaymentUrl,
                    Instructions = result.Instructions,
                    ExpiresAt = result.ExpiresAt,
                    Message = "Paiement initié avec succès. Vérifiez votre téléphone pour confirmer."
                });
            }

            return BadRequest(new
            {
                Success = false,
                Message = result.ErrorMessage ?? "Erreur lors de l'initiation du paiement MTN Money"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'initiation du paiement MTN Money pour l'utilisateur {UserId}", User.Identity?.Name);
            return StatusCode(500, new { Success = false, Message = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Initier un paiement Moov Money
    /// </summary>
    [HttpPost("moov-money/initiate")]
    [Authorize]
    public async Task<IActionResult> InitiateMoovMoneyPayment([FromBody] MoovMoneyPaymentRequest request)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            // Validation des données d'entrée
            if (!IsValidGuineanPhoneNumber(request.PhoneNumber))
                return BadRequest(new { Message = "Numéro de téléphone Moov Money invalide" });

            if (request.Amount <= 0)
                return BadRequest(new { Message = "Montant invalide" });

            var paymentRequest = new MobilePaymentRequestDto
            {
                UserId = userId,
                Provider = MobilePaymentProvider.MoovMoney,
                PhoneNumber = request.PhoneNumber,
                Amount = request.Amount,
                Currency = "GNF",
                OrderId = request.OrderId,
                Description = request.Description ?? $"Paiement NafaPlace #{request.OrderId}",
                CallbackUrl = $"{Request.Scheme}://{Request.Host}/api/v1/mobilepayment/moov-money/callback",
                ReturnUrl = request.ReturnUrl
            };

            var result = await _mobilePaymentService.InitiatePaymentAsync(paymentRequest);

            if (result.Success)
            {
                return Ok(new
                {
                    Success = true,
                    TransactionId = result.TransactionId,
                    PaymentUrl = result.PaymentUrl,
                    Instructions = result.Instructions,
                    ExpiresAt = result.ExpiresAt,
                    Message = "Paiement initié avec succès. Vérifiez votre téléphone pour confirmer."
                });
            }

            return BadRequest(new
            {
                Success = false,
                Message = result.ErrorMessage ?? "Erreur lors de l'initiation du paiement Moov Money"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'initiation du paiement Moov Money pour l'utilisateur {UserId}", User.Identity?.Name);
            return StatusCode(500, new { Success = false, Message = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Vérifier le statut d'un paiement mobile
    /// </summary>
    [HttpGet("status/{transactionId}")]
    [Authorize]
    public async Task<IActionResult> CheckPaymentStatus(string transactionId)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            var status = await _mobilePaymentService.CheckPaymentStatusAsync(transactionId, MobilePaymentProvider.OrangeMoney);
            if (status == null)
                return NotFound(new { Message = "Transaction non trouvée" });

            return Ok(new
            {
                Success = true,
                TransactionId = status.TransactionId,
                Status = status.Status,
                Amount = status.Amount,
                Currency = status.Currency,
                Provider = status.Provider.ToString(),
                CreatedAt = status.CreatedAt,
                UpdatedAt = status.UpdatedAt,
                Message = GetStatusMessage(status.Status)
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification du statut de paiement {TransactionId}", transactionId);
            return StatusCode(500, new { Success = false, Message = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Callback pour Orange Money
    /// </summary>
    [HttpPost("orange-money/callback")]
    [AllowAnonymous]
    public async Task<IActionResult> OrangeMoneyCallback([FromBody] OrangeMoneyCallbackDto callback)
    {
        try
        {
            _logger.LogInformation("Callback Orange Money reçu: {TransactionId}", callback.TransactionId);

            var callbackData = new Dictionary<string, object>
            {
                ["Status"] = callback.Status,
                ["Amount"] = callback.Amount,
                ["Reference"] = callback.Reference,
                ["AdditionalData"] = callback.AdditionalData
            };
            var result = await _mobilePaymentService.ProcessCallbackAsync(
                callback.TransactionId,
                callbackData
            );

            if (result)
            {
                return Ok(new { Status = "SUCCESS", Message = "Callback traité avec succès" });
            }

            return BadRequest(new { Status = "ERROR", Message = "Erreur lors du traitement du callback" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement du callback Orange Money");
            return StatusCode(500, new { Status = "ERROR", Message = "Erreur interne" });
        }
    }

    /// <summary>
    /// Callback pour MTN Money
    /// </summary>
    [HttpPost("mtn-money/callback")]
    [AllowAnonymous]
    public async Task<IActionResult> MtnMoneyCallback([FromBody] MtnMoneyCallbackDto callback)
    {
        try
        {
            _logger.LogInformation("Callback MTN Money reçu: {TransactionId}", callback.TransactionId);

            var callbackData = new Dictionary<string, object>
            {
                ["Status"] = callback.Status,
                ["Amount"] = callback.Amount,
                ["Reference"] = callback.Reference,
                ["AdditionalData"] = callback.AdditionalData
            };
            var result = await _mobilePaymentService.ProcessCallbackAsync(
                callback.TransactionId,
                callbackData
            );

            if (result)
            {
                return Ok(new { Status = "SUCCESS", Message = "Callback traité avec succès" });
            }

            return BadRequest(new { Status = "ERROR", Message = "Erreur lors du traitement du callback" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement du callback MTN Money");
            return StatusCode(500, new { Status = "ERROR", Message = "Erreur interne" });
        }
    }

    /// <summary>
    /// Callback pour Moov Money
    /// </summary>
    [HttpPost("moov-money/callback")]
    [AllowAnonymous]
    public async Task<IActionResult> MoovMoneyCallback([FromBody] MoovMoneyCallbackDto callback)
    {
        try
        {
            _logger.LogInformation("Callback Moov Money reçu: {TransactionId}", callback.TransactionId);

            var callbackData = new Dictionary<string, object>
            {
                ["Status"] = callback.Status,
                ["Amount"] = callback.Amount,
                ["Reference"] = callback.Reference,
                ["AdditionalData"] = callback.AdditionalData
            };
            var result = await _mobilePaymentService.ProcessCallbackAsync(
                callback.TransactionId,
                callbackData
            );

            if (result)
            {
                return Ok(new { Status = "SUCCESS", Message = "Callback traité avec succès" });
            }

            return BadRequest(new { Status = "ERROR", Message = "Erreur lors du traitement du callback" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement du callback Moov Money");
            return StatusCode(500, new { Status = "ERROR", Message = "Erreur interne" });
        }
    }

    /// <summary>
    /// Obtenir l'historique des paiements mobiles de l'utilisateur
    /// </summary>
    [HttpGet("history")]
    [Authorize]
    public async Task<IActionResult> GetPaymentHistory([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            var history = await _mobilePaymentService.GetUserPaymentHistoryAsync(userId, page, pageSize);
            return Ok(history);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de l'historique des paiements pour l'utilisateur {UserId}", User.Identity?.Name);
            return StatusCode(500, new { Success = false, Message = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Sauvegarder une méthode de paiement mobile
    /// </summary>
    [HttpPost("save-method")]
    [Authorize]
    public async Task<IActionResult> SavePaymentMethod([FromBody] SaveMobilePaymentMethodRequest request)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            if (!IsValidGuineanPhoneNumber(request.PhoneNumber))
                return BadRequest(new { Message = "Numéro de téléphone invalide" });

            var paymentMethod = new MobilePaymentMethodDto
            {
                UserId = userId,
                Provider = request.Provider,
                PhoneNumber = request.PhoneNumber,
                IsDefault = request.IsDefault,
                Alias = request.Alias ?? GetProviderName(request.Provider)
            };

            var result = await _paymentMethodService.SaveMobilePaymentMethodAsync(userId, paymentMethod);

            if (result != null)
            {
                return Ok(new
                {
                    Success = true,
                    PaymentMethodId = result.Id,
                    Message = "Méthode de paiement sauvegardée avec succès"
                });
            }

            return BadRequest(new { Success = false, Message = "Erreur lors de la sauvegarde" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la sauvegarde de la méthode de paiement pour l'utilisateur {UserId}", User.Identity?.Name);
            return StatusCode(500, new { Success = false, Message = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Obtenir les méthodes de paiement sauvegardées de l'utilisateur
    /// </summary>
    [HttpGet("saved-methods")]
    [Authorize]
    public async Task<IActionResult> GetSavedPaymentMethods()
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            var methods = await _paymentMethodService.GetUserPaymentMethodsAsync(userId);
            return Ok(methods);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des méthodes de paiement pour l'utilisateur {UserId}", User.Identity?.Name);
            return StatusCode(500, new { Success = false, Message = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Supprimer une méthode de paiement sauvegardée
    /// </summary>
    [HttpDelete("saved-methods/{methodId}")]
    [Authorize]
    public async Task<IActionResult> DeleteSavedPaymentMethod(int methodId)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            var result = await _paymentMethodService.DeletePaymentMethodAsync(userId, methodId.ToString());

            if (result)
            {
                return Ok(new { Success = true, Message = "Méthode de paiement supprimée avec succès" });
            }

            return BadRequest(new { Success = false, Message = "Erreur lors de la suppression" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de la méthode de paiement {MethodId}", methodId);
            return StatusCode(500, new { Success = false, Message = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Obtenir les frais de transaction pour un montant donné
    /// </summary>
    [HttpGet("fees")]
    public async Task<IActionResult> GetTransactionFees([FromQuery] MobilePaymentProvider provider, [FromQuery] decimal amount)
    {
        try
        {
            var fees = await _mobilePaymentService.CalculateFeesAsync(amount, provider);
            return Ok(new
            {
                Provider = provider.ToString(),
                Amount = amount,
                Fees = fees,
                TotalAmount = amount + fees,
                Currency = "GNF"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du calcul des frais pour {Provider} et montant {Amount}", provider, amount);
            return StatusCode(500, new { Success = false, Message = "Erreur interne du serveur" });
        }
    }

    #region Méthodes privées

    /// <summary>
    /// Valide si un numéro de téléphone est valide en Guinée
    /// </summary>
    private bool IsValidGuineanPhoneNumber(string phoneNumber)
    {
        if (string.IsNullOrEmpty(phoneNumber))
            return false;

        // Nettoyer le numéro (enlever espaces, tirets, etc.)
        var cleanNumber = phoneNumber.Replace(" ", "").Replace("-", "").Replace("+", "");

        // Formats valides en Guinée:
        // +224XXXXXXXX ou 224XXXXXXXX (avec indicatif pays)
        // 6XXXXXXXX ou 7XXXXXXXX (sans indicatif pays)

        if (cleanNumber.StartsWith("224"))
        {
            cleanNumber = cleanNumber.Substring(3);
        }

        // Les numéros de mobile en Guinée commencent par 6 ou 7 et font 9 chiffres
        return cleanNumber.Length == 9 && (cleanNumber.StartsWith("6") || cleanNumber.StartsWith("7"));
    }

    /// <summary>
    /// Obtient le message de statut approprié
    /// </summary>
    private string GetStatusMessage(MobilePaymentStatus status)
    {
        return status switch
        {
            MobilePaymentStatus.Pending => "Paiement en attente de confirmation",
            MobilePaymentStatus.Processing => "Paiement en cours de traitement",
            MobilePaymentStatus.Completed => "Paiement effectué avec succès",
            MobilePaymentStatus.Failed => "Échec du paiement",
            MobilePaymentStatus.Cancelled => "Paiement annulé",
            MobilePaymentStatus.Expired => "Paiement expiré",
            _ => "Statut inconnu"
        };
    }

    /// <summary>
    /// Obtient le nom du fournisseur
    /// </summary>
    private string GetProviderName(MobilePaymentProvider provider)
    {
        return provider switch
        {
            MobilePaymentProvider.OrangeMoney => "Orange Money",
            MobilePaymentProvider.MtnMoney => "MTN Mobile Money",
            MobilePaymentProvider.MoovMoney => "Moov Money",
            _ => provider.ToString()
        };
    }

    #endregion
}

// DTOs pour les requêtes
public class MtnMoneyPaymentRequest
{
    public string PhoneNumber { get; set; } = "";
    public decimal Amount { get; set; }
    public string OrderId { get; set; } = "";
    public string? Description { get; set; }
    public string? ReturnUrl { get; set; }
}

public class MoovMoneyPaymentRequest
{
    public string PhoneNumber { get; set; } = "";
    public decimal Amount { get; set; }
    public string OrderId { get; set; } = "";
    public string? Description { get; set; }
    public string? ReturnUrl { get; set; }
}

public class SaveMobilePaymentMethodRequest
{
    public MobilePaymentProvider Provider { get; set; }
    public string PhoneNumber { get; set; } = "";
    public string? Alias { get; set; }
    public bool IsDefault { get; set; }
}

// DTOs pour les callbacks
public class OrangeMoneyCallbackDto
{
    public string TransactionId { get; set; } = "";
    public string Status { get; set; } = "";
    public decimal Amount { get; set; }
    public string Reference { get; set; } = "";
    public Dictionary<string, object>? AdditionalData { get; set; }
}

public class MtnMoneyCallbackDto
{
    public string TransactionId { get; set; } = "";
    public string Status { get; set; } = "";
    public decimal Amount { get; set; }
    public string Reference { get; set; } = "";
    public Dictionary<string, object>? AdditionalData { get; set; }
}

public class MoovMoneyCallbackDto
{
    public string TransactionId { get; set; } = "";
    public string Status { get; set; } = "";
    public decimal Amount { get; set; }
    public string Reference { get; set; } = "";
    public Dictionary<string, object>? AdditionalData { get; set; }
}