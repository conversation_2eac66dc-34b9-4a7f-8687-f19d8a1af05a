@using Microsoft.AspNetCore.SignalR.Client
@using NafaPlace.Web.Models.Delivery
@implements IAsyncDisposable
@inject IJSRuntime JSRuntime
@inject ILogger<DeliveryPersonDashboard> Logger
@inject GeolocationService GeolocationService

<div class="delivery-dashboard">
    <!-- En-tête avec statut de connexion -->
    <div class="dashboard-header">
        <div class="status-section">
            <h2>Tableau de bord livreur</h2>
            <div class="connection-indicators">
                <div class="indicator @(IsConnected ? "connected" : "disconnected")">
                    @(IsConnected ? "🟢 En ligne" : "🔴 Hors ligne")
                </div>
                <div class="indicator @(isLocationActive ? "location-active" : "location-inactive")">
                    @(isLocationActive ? "📍 GPS actif" : "📍 GPS inactif")
                </div>
            </div>
        </div>

        <div class="quick-actions">
            <button class="btn @(isOnDuty ? "btn-danger" : "btn-success")"
                    @onclick="ToggleDutyStatus">
                @(isOnDuty ? "⏸️ Pause" : "▶️ En service")
            </button>

            <button class="btn btn-outline-primary" @onclick="ToggleLocationSharing">
                @(isLocationActive ? "📍 Arrêter GPS" : "📍 Activer GPS")
            </button>
        </div>
    </div>

    <!-- Statistiques de la journée -->
    <div class="daily-stats">
        <div class="stat-card">
            <div class="stat-number">@completedDeliveries</div>
            <div class="stat-label">Livraisons</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">@Math.Round(totalDistance, 1) km</div>
            <div class="stat-label">Distance</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">@Math.Round(totalEarnings, 0) GNF</div>
            <div class="stat-label">Gains</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">@Math.Round(averageRating, 1) ⭐</div>
            <div class="stat-label">Note</div>
        </div>
    </div>

    <!-- Livraisons actives -->
    <div class="active-deliveries-section">
        <h3>Livraisons en cours (@activeDeliveries.Count)</h3>

        @if (activeDeliveries.Any())
        {
            <div class="deliveries-list">
                @foreach (var delivery in activeDeliveries.OrderBy(d => d.ScheduledTime))
                {
                    <div class="delivery-card @(delivery.Priority == "urgent" ? "urgent" : "")">
                        <div class="delivery-header">
                            <div class="delivery-info">
                                <h4>#@delivery.OrderNumber</h4>
                                <p class="customer-name">👤 @delivery.CustomerName</p>
                                <p class="customer-phone">📱 @delivery.CustomerPhone</p>
                            </div>

                            <div class="delivery-status">
                                <select class="form-select" @onchange="@((e) => UpdateDeliveryStatus(delivery.Id, e.Value?.ToString() ?? ""))">
                                    <option value="assigned" selected="@(delivery.Status == "assigned")">Assignée</option>
                                    <option value="picked_up" selected="@(delivery.Status == "picked_up")">Récupérée</option>
                                    <option value="en_route" selected="@(delivery.Status == "en_route")">En route</option>
                                    <option value="arrived" selected="@(delivery.Status == "arrived")">Arrivé</option>
                                    <option value="delivered" selected="@(delivery.Status == "delivered")">Livrée</option>
                                </select>
                            </div>
                        </div>

                        <div class="delivery-details">
                            <div class="address-section">
                                <p class="address">📍 @delivery.DeliveryAddress</p>
                                <p class="scheduled-time">
                                    ⏰ Prévu: @delivery.ScheduledTime.ToString("HH:mm")
                                </p>
                            </div>

                            <div class="payment-info">
                                <span class="payment-method">@GetPaymentMethodIcon(delivery.PaymentMethod) @delivery.PaymentMethod</span>
                                @if (delivery.PaymentRequired)
                                {
                                    <span class="amount-to-collect">💰 @delivery.DeliveryFee GNF à encaisser</span>
                                }
                            </div>
                        </div>

                        @if (!string.IsNullOrEmpty(delivery.SpecialInstructions))
                        {
                            <div class="special-instructions">
                                <strong>Instructions spéciales:</strong>
                                <p>@delivery.SpecialInstructions</p>
                            </div>
                        }

                        <div class="delivery-items">
                            <h5>Articles (@delivery.Items.Count)</h5>
                            @foreach (var item in delivery.Items)
                            {
                                <div class="item">
                                    <span class="item-name">@item.ProductName</span>
                                    <span class="item-quantity"><EMAIL></span>
                                </div>
                            }
                        </div>

                        <div class="delivery-actions">
                            <button class="btn btn-outline-primary btn-sm"
                                    @onclick="() => NavigateToDelivery(delivery)">
                                🗺️ Naviguer
                            </button>
                            <button class="btn btn-outline-success btn-sm"
                                    @onclick="() => CallCustomer(delivery.CustomerPhone)">
                                📞 Appeler
                            </button>
                            <button class="btn btn-outline-info btn-sm"
                                    @onclick="() => OpenChat(delivery.Id)">
                                💬 Message
                            </button>
                            @if (delivery.Status == "arrived")
                            {
                                <button class="btn btn-success btn-sm"
                                        @onclick="() => CompleteDelivery(delivery.Id)">
                                    ✅ Confirmer livraison
                                </button>
                            }
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <div class="no-deliveries">
                <div class="no-deliveries-content">
                    <div class="no-deliveries-icon">📦</div>
                    <h4>Aucune livraison en cours</h4>
                    <p>Vous recevrez de nouvelles livraisons automatiquement</p>
                </div>
            </div>
        }
    </div>

    <!-- Carte avec position actuelle -->
    <div class="map-section">
        <h3>Votre position</h3>
        <div class="map-container">
            <div id="delivery-person-map" class="delivery-map"></div>
            <div class="map-overlay">
                <div class="current-location">
                    @if (currentPosition != null)
                    {
                        <div class="location-info">
                            <div class="coordinates">
                                📍 @Math.Round(currentPosition.Latitude, 4), @Math.Round(currentPosition.Longitude, 4)
                            </div>
                            @if (currentPosition.Speed > 0)
                            {
                                <div class="speed">
                                    🚴 @Math.Round(currentPosition.Speed, 1) km/h
                                </div>
                            }
                            <div class="last-update">
                                Mise à jour: @currentPosition.Timestamp.ToString("HH:mm:ss")
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="location-info">
                            📍 Position non disponible
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Historique récent -->
    <div class="recent-activity">
        <h3>Activité récente</h3>
        <div class="activity-list">
            @foreach (var activity in recentActivities.Take(5))
            {
                <div class="activity-item">
                    <div class="activity-time">@activity.Timestamp.ToString("HH:mm")</div>
                    <div class="activity-content">
                        <strong>@activity.Title</strong>
                        <p>@activity.Description</p>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Modal de confirmation de livraison -->
@if (showDeliveryConfirmation)
{
    <div class="modal-overlay" @onclick="CloseDeliveryConfirmation">
        <div class="modal-content" @onclick:stopPropagation="true">
            <div class="modal-header">
                <h4>Confirmer la livraison</h4>
                <button class="btn-close" @onclick="CloseDeliveryConfirmation">✕</button>
            </div>

            <div class="modal-body">
                <div class="confirmation-section">
                    <label>Code de confirmation (optionnel)</label>
                    <input type="text" @bind="confirmationCode" class="form-control"
                           placeholder="Entrez le code si fourni par le client" />
                </div>

                <div class="confirmation-section">
                    <label>Notes de livraison</label>
                    <textarea @bind="deliveryNotes" class="form-control" rows="3"
                              placeholder="Ajoutez des notes sur la livraison..."></textarea>
                </div>

                <div class="confirmation-section">
                    <label>Photo de livraison (optionnel)</label>
                    <input type="file" accept="image/*" @ref="photoInput" class="form-control" />
                </div>
            </div>

            <div class="modal-footer">
                <button class="btn btn-secondary" @onclick="CloseDeliveryConfirmation">
                    Annuler
                </button>
                <button class="btn btn-success" @onclick="ConfirmDeliveryCompletion">
                    ✅ Confirmer livraison
                </button>
            </div>
        </div>
    </div>
}

@code {
    private HubConnection? hubConnection;
    private List<ActiveDeliveryDto> activeDeliveries = new();
    private List<Activity> recentActivities = new();
    private PositionDto? currentPosition;

    private bool isOnDuty = false;
    private bool isLocationActive = false;
    private bool showDeliveryConfirmation = false;
    private int selectedDeliveryForCompletion = 0;

    // Statistiques
    private int completedDeliveries = 0;
    private double totalDistance = 0;
    private decimal totalEarnings = 0;
    private double averageRating = 4.2;

    // Modal de confirmation
    private string confirmationCode = "";
    private string deliveryNotes = "";
    private ElementReference photoInput;

    public bool IsConnected =>
        hubConnection?.State == HubConnectionState.Connected;

    protected override async Task OnInitializedAsync()
    {
        await InitializeSignalRConnection();
        await LoadDailyStats();
        await LoadActiveDeliveries();
        await InitializeGeolocation();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await InitializeMap();
        }
    }

    private async Task InitializeSignalRConnection()
    {
        try
        {
            hubConnection = new HubConnectionBuilder()
                .WithUrl("/hubs/tracking")
                .WithAutomaticReconnect()
                .Build();

            // Gestionnaires d'événements
            hubConnection.On<int>("NewDeliveryAssigned", OnNewDeliveryAssigned);
            hubConnection.On<int, string>("DeliveryStatusUpdated", OnDeliveryStatusUpdated);
            hubConnection.On<string>("Error", OnSignalRError);

            await hubConnection.StartAsync();
            Logger.LogInformation("Connexion SignalR établie pour le livreur");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'initialisation de SignalR");
        }
    }

    private async Task LoadActiveDeliveries()
    {
        try
        {
            // Ici, vous appellerez votre service pour récupérer les livraisons actives
            // Pour l'exemple, nous simulons des données
            activeDeliveries = new List<ActiveDeliveryDto>
            {
                new ActiveDeliveryDto
                {
                    Id = 1,
                    OrderNumber = "CMD-2024-001",
                    CustomerName = "Amadou Diallo",
                    CustomerPhone = "+224 601 23 45 67",
                    DeliveryAddress = "Kipé Centre, Conakry",
                    ScheduledTime = DateTime.Now.AddMinutes(30),
                    Status = "assigned",
                    Priority = "normal",
                    DeliveryFee = 15000,
                    PaymentMethod = "Mobile Money",
                    PaymentRequired = true,
                    Items = new List<DeliveryItemDto>
                    {
                        new DeliveryItemDto { ProductName = "Smartphone Samsung", Quantity = 1, Price = 2500000 }
                    }
                }
            };
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du chargement des livraisons actives");
        }
    }

    private async Task LoadDailyStats()
    {
        try
        {
            // Charger les statistiques de la journée
            completedDeliveries = 8;
            totalDistance = 45.6;
            totalEarnings = 120000;
            averageRating = 4.2;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du chargement des statistiques");
        }
    }

    private async Task InitializeGeolocation()
    {
        try
        {
            await GeolocationService.InitializeAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'initialisation de la géolocalisation");
        }
    }

    private async Task InitializeMap()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("initializeDeliveryPersonMap", "delivery-person-map");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'initialisation de la carte");
        }
    }

    private async Task ToggleDutyStatus()
    {
        isOnDuty = !isOnDuty;

        if (isOnDuty)
        {
            await StartLocationSharing();
            recentActivities.Insert(0, new Activity
            {
                Title = "Service démarré",
                Description = "Vous êtes maintenant en service et disponible pour les livraisons",
                Timestamp = DateTime.Now
            });
        }
        else
        {
            await StopLocationSharing();
            recentActivities.Insert(0, new Activity
            {
                Title = "Service arrêté",
                Description = "Vous n'êtes plus disponible pour de nouvelles livraisons",
                Timestamp = DateTime.Now
            });
        }

        StateHasChanged();
    }

    private async Task ToggleLocationSharing()
    {
        if (isLocationActive)
        {
            await StopLocationSharing();
        }
        else
        {
            await StartLocationSharing();
        }
    }

    private async Task StartLocationSharing()
    {
        try
        {
            isLocationActive = true;

            // Démarrer le suivi de position
            await GeolocationService.StartWatchingPosition(OnPositionUpdate, OnPositionError);

            recentActivities.Insert(0, new Activity
            {
                Title = "GPS activé",
                Description = "Partage de position démarré",
                Timestamp = DateTime.Now
            });

            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du démarrage du partage de position");
            isLocationActive = false;
        }
    }

    private async Task StopLocationSharing()
    {
        try
        {
            isLocationActive = false;
            await GeolocationService.StopWatchingPosition();

            recentActivities.Insert(0, new Activity
            {
                Title = "GPS désactivé",
                Description = "Partage de position arrêté",
                Timestamp = DateTime.Now
            });

            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'arrêt du partage de position");
        }
    }

    private async Task OnPositionUpdate(double latitude, double longitude, double? accuracy = null)
    {
        currentPosition = new PositionDto
        {
            Latitude = latitude,
            Longitude = longitude,
            Timestamp = DateTime.UtcNow,
            Accuracy = (decimal)(accuracy ?? 0)
        };

        // Envoyer la position via SignalR
        if (IsConnected && isOnDuty)
        {
            await hubConnection!.SendAsync("SendPosition", latitude, longitude);
        }

        // Mettre à jour la carte
        await JSRuntime.InvokeVoidAsync("updateDeliveryPersonMapPosition",
            "delivery-person-map", latitude, longitude);

        await InvokeAsync(StateHasChanged);
    }

    private Task OnPositionError(string error)
    {
        Logger.LogError("Erreur de géolocalisation: {Error}", error);
        recentActivities.Insert(0, new Activity
        {
            Title = "Erreur GPS",
            Description = $"Erreur de géolocalisation: {error}",
            Timestamp = DateTime.Now
        });
        return Task.CompletedTask;
    }

    private async Task UpdateDeliveryStatus(int deliveryId, string newStatus)
    {
        try
        {
            var delivery = activeDeliveries.FirstOrDefault(d => d.Id == deliveryId);
            if (delivery != null)
            {
                delivery.Status = newStatus;

                // Envoyer la mise à jour via SignalR
                if (IsConnected)
                {
                    await hubConnection!.SendAsync("NotifyStatusChange", deliveryId, newStatus);
                }

                recentActivities.Insert(0, new Activity
                {
                    Title = $"Statut mis à jour",
                    Description = $"Livraison #{delivery.OrderNumber}: {GetStatusText(newStatus)}",
                    Timestamp = DateTime.Now
                });

                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de la mise à jour du statut");
        }
    }

    private async Task NavigateToDelivery(ActiveDeliveryDto delivery)
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("openNavigation",
                delivery.DeliveryLatitude, delivery.DeliveryLongitude, delivery.DeliveryAddress);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'ouverture de la navigation");
        }
    }

    private async Task CallCustomer(string phoneNumber)
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("makePhoneCall", phoneNumber);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'appel");
        }
    }

    private void OpenChat(int deliveryId)
    {
        // Ouvrir l'interface de chat pour cette livraison
        // Implementation à venir
    }

    private void CompleteDelivery(int deliveryId)
    {
        selectedDeliveryForCompletion = deliveryId;
        showDeliveryConfirmation = true;
    }

    private void CloseDeliveryConfirmation()
    {
        showDeliveryConfirmation = false;
        selectedDeliveryForCompletion = 0;
        confirmationCode = "";
        deliveryNotes = "";
    }

    private async Task ConfirmDeliveryCompletion()
    {
        try
        {
            var delivery = activeDeliveries.FirstOrDefault(d => d.Id == selectedDeliveryForCompletion);
            if (delivery != null)
            {
                // Marquer comme livrée
                delivery.Status = "delivered";

                // Retirer de la liste des livraisons actives
                activeDeliveries.Remove(delivery);

                // Mettre à jour les statistiques
                completedDeliveries++;
                totalEarnings += delivery.DeliveryFee;

                recentActivities.Insert(0, new Activity
                {
                    Title = "Livraison terminée",
                    Description = $"Commande #{delivery.OrderNumber} livrée avec succès",
                    Timestamp = DateTime.Now
                });

                CloseDeliveryConfirmation();
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de la confirmation de livraison");
        }
    }

    // Gestionnaires d'événements SignalR
    private async Task OnNewDeliveryAssigned(int deliveryId)
    {
        // Recharger les livraisons actives
        await LoadActiveDeliveries();

        recentActivities.Insert(0, new Activity
        {
            Title = "Nouvelle livraison assignée",
            Description = $"Vous avez reçu une nouvelle livraison",
            Timestamp = DateTime.Now
        });

        await InvokeAsync(StateHasChanged);
    }

    private async Task OnDeliveryStatusUpdated(int deliveryId, string status)
    {
        var delivery = activeDeliveries.FirstOrDefault(d => d.Id == deliveryId);
        if (delivery != null)
        {
            delivery.Status = status;
            await InvokeAsync(StateHasChanged);
        }
    }

    private void OnSignalRError(string error)
    {
        Logger.LogError("Erreur SignalR: {Error}", error);
    }

    // Méthodes utilitaires
    private string GetPaymentMethodIcon(string method) => method.ToLower() switch
    {
        "mobile money" => "📱",
        "cash" => "💵",
        "card" => "💳",
        _ => "💰"
    };

    private string GetStatusText(string status) => status.ToLower() switch
    {
        "assigned" => "Assignée",
        "picked_up" => "Récupérée",
        "en_route" => "En route",
        "arrived" => "Arrivé",
        "delivered" => "Livrée",
        _ => status
    };

    public async ValueTask DisposeAsync()
    {
        if (hubConnection is not null)
        {
            await hubConnection.DisposeAsync();
        }

        await GeolocationService.StopWatchingPosition();
    }

    // Classes helper
    private class Activity
    {
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public DateTime Timestamp { get; set; }
    }
}

<style>
.delivery-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
}

.status-section h2 {
    margin: 0 0 10px 0;
    font-size: 1.8rem;
}

.connection-indicators {
    display: flex;
    gap: 15px;
}

.indicator {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.connected, .location-active {
    background: rgba(40, 167, 69, 0.2);
    border: 1px solid rgba(40, 167, 69, 0.5);
}

.disconnected, .location-inactive {
    background: rgba(220, 53, 69, 0.2);
    border: 1px solid rgba(220, 53, 69, 0.5);
}

.quick-actions {
    display: flex;
    gap: 10px;
}

.daily-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    text-align: center;
    border-left: 4px solid #667eea;
}

.stat-number {
    font-size: 2.2rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.active-deliveries-section {
    margin-bottom: 30px;
}

.active-deliveries-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.4rem;
}

.deliveries-list {
    display: grid;
    gap: 20px;
}

.delivery-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-left: 4px solid #28a745;
    transition: all 0.3s ease;
}

.delivery-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.delivery-card.urgent {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #fff 0%, #fff5f5 100%);
}

.delivery-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.delivery-info h4 {
    margin: 0 0 8px 0;
    color: #2c3e50;
    font-size: 1.3rem;
}

.customer-name, .customer-phone {
    margin: 3px 0;
    color: #6c757d;
    font-size: 0.95rem;
}

.delivery-details {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 15px;
}

.address-section .address {
    color: #2c3e50;
    font-weight: 500;
    margin-bottom: 5px;
}

.scheduled-time {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
}

.payment-info {
    text-align: right;
}

.payment-method {
    display: block;
    margin-bottom: 5px;
    color: #2c3e50;
}

.amount-to-collect {
    background: #e8f5e8;
    color: #155724;
    padding: 4px 8px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 600;
}

.special-instructions {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 15px;
}

.special-instructions strong {
    color: #856404;
}

.special-instructions p {
    margin: 5px 0 0 0;
    color: #856404;
}

.delivery-items {
    margin-bottom: 20px;
}

.delivery-items h5 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1rem;
}

.item {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
}

.item:last-child {
    border-bottom: none;
}

.item-name {
    color: #2c3e50;
}

.item-quantity {
    color: #6c757d;
    font-weight: 600;
}

.delivery-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.no-deliveries {
    background: white;
    padding: 60px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    text-align: center;
}

.no-deliveries-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.no-deliveries h4 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.no-deliveries p {
    color: #6c757d;
}

.map-section {
    margin-bottom: 30px;
}

.map-section h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.map-container {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.delivery-map {
    width: 100%;
    height: 300px;
}

.map-overlay {
    position: absolute;
    top: 15px;
    left: 15px;
    background: rgba(255,255,255,0.95);
    padding: 15px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.location-info {
    font-size: 0.9rem;
}

.coordinates {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.speed, .last-update {
    color: #6c757d;
    margin-bottom: 3px;
}

.recent-activity {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.recent-activity h3 {
    color: #2c3e50;
    margin-bottom: 20px;
}

.activity-list {
    max-height: 250px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    gap: 15px;
    padding: 12px 0;
    border-bottom: 1px solid #eee;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-time {
    font-size: 0.8rem;
    color: #6c757d;
    min-width: 60px;
    font-weight: 600;
}

.activity-content strong {
    color: #2c3e50;
    display: block;
    margin-bottom: 3px;
}

.activity-content p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

/* Modal styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 15px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.modal-header h4 {
    margin: 0;
    color: #2c3e50;
}

.btn-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #6c757d;
}

.modal-body {
    padding: 20px;
}

.confirmation-section {
    margin-bottom: 20px;
}

.confirmation-section label {
    display: block;
    margin-bottom: 8px;
    color: #2c3e50;
    font-weight: 500;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid #eee;
}

/* Responsive Design */
@@media (max-width: 768px) {
    .delivery-dashboard {
        padding: 10px;
    }

    .dashboard-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .daily-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .delivery-header {
        flex-direction: column;
        gap: 15px;
    }

    .delivery-details {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .payment-info {
        text-align: left;
    }

    .delivery-actions {
        justify-content: center;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }
}
</style>