# Test de mise a jour du stock via API Catalog
Write-Host "Test de mise a jour du stock via API Catalog" -ForegroundColor Green

# 1. Connexion pour obtenir un token
Write-Host "1. Connexion..." -ForegroundColor Yellow
$loginBody = @{
    Username = "<EMAIL>"
    Password = "Kouyate92."
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/auth/login" -Method Post -Body $loginBody -ContentType "application/json"
    $token = $loginResponse.token
    Write-Host "Connexion reussie" -ForegroundColor Green
} catch {
    Write-Host "Erreur de connexion: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. Verification du stock actuel
Write-Host "2. Verification du stock actuel du produit 1..." -ForegroundColor Yellow
try {
    $currentStock = Invoke-RestMethod -Uri "http://localhost:5000/api/catalog/products/1/stock" -Method Get
    Write-Host "Stock actuel du produit 1: $currentStock" -ForegroundColor Cyan
} catch {
    Write-Host "Erreur lors de la recuperation du stock: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 3. Mise a jour du stock via l'API Catalog
Write-Host "3. Mise a jour du stock via l'API Catalog..." -ForegroundColor Yellow
$newStock = $currentStock + 3
$updateBody = @{
    Quantity = $newStock
} | ConvertTo-Json

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

try {
    $updateResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/catalog/products/1/stock" -Method Put -Body $updateBody -Headers $headers
    Write-Host "Mise a jour reussie: $($updateResponse.message)" -ForegroundColor Green
} catch {
    Write-Host "Erreur lors de la mise a jour: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

# 4. Verification du nouveau stock
Write-Host "4. Verification du nouveau stock..." -ForegroundColor Yellow
try {
    Start-Sleep -Seconds 2  # Attendre un peu pour la synchronisation
    $newStockValue = Invoke-RestMethod -Uri "http://localhost:5000/api/catalog/products/1/stock" -Method Get
    Write-Host "Nouveau stock du produit 1: $newStockValue" -ForegroundColor Cyan
    
    if ($newStockValue -eq $newStock) {
        Write-Host "Test reussi ! Le stock a ete mis a jour correctement." -ForegroundColor Green
    } else {
        Write-Host "Le stock n'a pas ete mis a jour comme attendu." -ForegroundColor Yellow
    }
} catch {
    Write-Host "Erreur lors de la verification: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Test termine" -ForegroundColor Green
