# Dockerfile spécifique pour Render - Identity Service
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 10000

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copier les fichiers de projet
COPY ["src/Services/Identity/NafaPlace.Identity.API/NafaPlace.Identity.API.csproj", "src/Services/Identity/NafaPlace.Identity.API/"]
COPY ["src/Services/Identity/NafaPlace.Identity.Application/NafaPlace.Identity.Application.csproj", "src/Services/Identity/NafaPlace.Identity.Application/"]
COPY ["src/Services/Identity/NafaPlace.Identity.Domain/NafaPlace.Identity.Domain.csproj", "src/Services/Identity/NafaPlace.Identity.Domain/"]
COPY ["src/Services/Identity/NafaPlace.Identity.Infrastructure/NafaPlace.Identity.Infrastructure.csproj", "src/Services/Identity/NafaPlace.Identity.Infrastructure/"]
COPY ["src/BuildingBlocks/Common/NafaPlace.Common/NafaPlace.Common.csproj", "src/BuildingBlocks/Common/NafaPlace.Common/"]

# Restaurer les dépendances
RUN dotnet restore "src/Services/Identity/NafaPlace.Identity.API/NafaPlace.Identity.API.csproj"

# Copier tout le code source
COPY . .

# Build de l'application
WORKDIR "/src/src/Services/Identity/NafaPlace.Identity.API"
RUN dotnet build "NafaPlace.Identity.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "NafaPlace.Identity.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Configuration pour Render
ENV ASPNETCORE_URLS=http://+:10000
ENV ASPNETCORE_ENVIRONMENT=Production

ENTRYPOINT ["dotnet", "NafaPlace.Identity.API.dll"]
