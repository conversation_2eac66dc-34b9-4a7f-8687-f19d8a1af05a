@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@inject IJSRuntime JSRuntime
@inject ILogger<ChatWidget> Logger
@inject NavigationManager Navigation

<div class="chat-widget @(isOpen ? "open" : "")">
    @if (!isOpen)
    {
        <!-- Bouton flottant -->
        <button class="chat-button" @onclick="ToggleChat" title="Besoin d'aide ?">
            <i class="fas fa-comments"></i>
            @if (unreadCount > 0)
            {
                <span class="badge">@unreadCount</span>
            }
        </button>
    }
    else
    {
        <!-- Fenêtre de chat -->
        <div class="chat-window">
            <!-- En-tête -->
            <div class="chat-header">
                <div class="d-flex align-items-center">
                    <i class="fas fa-headset me-2"></i>
                    <div>
                        <h6 class="mb-0">Support Client</h6>
                        <small class="text-white-50">Nous sommes là pour vous aider</small>
                    </div>
                </div>
                <button class="btn-close btn-close-white" @onclick="ToggleChat"></button>
            </div>

            <!-- Corps -->
            <div class="chat-body">
                @if (currentView == "list")
                {
                    <!-- Liste des conversations -->
                    <div class="conversations-list">
                        @if (conversations.Any())
                        {
                            @foreach (var conv in conversations)
                            {
                                <div class="conversation-item @(conv.HasUnreadMessages ? "unread" : "")" 
                                     @onclick="() => OpenConversation(conv.Id)">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">@conv.Subject</h6>
                                            <small class="text-muted">@conv.SellerName</small>
                                        </div>
                                        @if (conv.UnreadCount > 0)
                                        {
                                            <span class="badge bg-primary">@conv.UnreadCount</span>
                                        }
                                    </div>
                                    <small class="text-muted">@GetRelativeTime(conv.LastMessageAt ?? conv.CreatedAt)</small>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                                <p class="text-muted">Aucune conversation</p>
                            </div>
                        }
                    </div>

                    <!-- Bouton nouvelle conversation -->
                    <div class="p-3 border-top">
                        <button class="btn btn-primary w-100" @onclick="ShowNewConversationForm">
                            <i class="fas fa-plus me-2"></i>
                            Nouvelle conversation
                        </button>
                    </div>
                }
                else if (currentView == "new")
                {
                    <!-- Formulaire nouvelle conversation -->
                    <div class="p-3">
                        <button class="btn btn-link text-decoration-none mb-3 p-0" @onclick="BackToList">
                            <i class="fas fa-arrow-left me-2"></i>
                            Retour
                        </button>

                        <h6 class="mb-3">Nouvelle conversation</h6>

                        <div class="mb-3">
                            <label class="form-label">Sujet</label>
                            <input type="text" class="form-control" @bind="newConversation.Subject" 
                                   placeholder="Ex: Question sur un produit" />
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Type de demande</label>
                            <select class="form-select" @bind="newConversation.Type">
                                <option value="General">Général</option>
                                <option value="ProductInquiry">Question sur un produit</option>
                                <option value="OrderSupport">Support commande</option>
                                <option value="TechnicalSupport">Support technique</option>
                                <option value="Complaint">Réclamation</option>
                                <option value="PreSale">Avant-vente</option>
                                <option value="Return">Retour</option>
                                <option value="Refund">Remboursement</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Message</label>
                            <textarea class="form-control" rows="4" @bind="newConversation.Message" 
                                      placeholder="Décrivez votre demande..."></textarea>
                        </div>

                        <button class="btn btn-primary w-100" @onclick="CreateConversation" disabled="@isCreating">
                            @if (isCreating)
                            {
                                <span class="spinner-border spinner-border-sm me-2"></span>
                            }
                            Envoyer
                        </button>
                    </div>
                }
                else if (currentView == "conversation")
                {
                    <!-- Vue conversation (à implémenter) -->
                    <div class="p-3">
                        <button class="btn btn-link text-decoration-none mb-3 p-0" @onclick="BackToList">
                            <i class="fas fa-arrow-left me-2"></i>
                            Retour
                        </button>
                        <p class="text-muted">Conversation en cours de développement...</p>
                    </div>
                }
            </div>
        </div>
    }
</div>

@code {
    [CascadingParameter]
    private Task<AuthenticationState>? AuthenticationStateTask { get; set; }

    private bool isOpen = false;
    private string currentView = "list"; // "list", "new", "conversation"
    private int unreadCount = 0;
    private bool isCreating = false;

    private List<ConversationDto> conversations = new();
    private NewConversationModel newConversation = new();

    protected override async Task OnInitializedAsync()
    {
        // Charger les conversations si l'utilisateur est connecté
        await LoadConversations();
    }

    private void ToggleChat()
    {
        isOpen = !isOpen;
        if (isOpen)
        {
            currentView = "list";
        }
    }

    private void ShowNewConversationForm()
    {
        currentView = "new";
        newConversation = new NewConversationModel();
    }

    private void BackToList()
    {
        currentView = "list";
    }

    private void OpenConversation(int conversationId)
    {
        currentView = "conversation";
        // TODO: Charger les messages de la conversation
    }

    private async Task LoadConversations()
    {
        try
        {
            // TODO: Appeler l'API pour charger les conversations
            conversations = new List<ConversationDto>();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du chargement des conversations");
        }
    }

    private async Task CreateConversation()
    {
        if (string.IsNullOrWhiteSpace(newConversation.Subject) || 
            string.IsNullOrWhiteSpace(newConversation.Message))
        {
            return;
        }

        isCreating = true;
        try
        {
            // TODO: Appeler l'API pour créer la conversation
            await Task.Delay(1000); // Simulation

            // Retour à la liste
            currentView = "list";
            await LoadConversations();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de la création de la conversation");
        }
        finally
        {
            isCreating = false;
        }
    }

    private string GetRelativeTime(DateTime dateTime)
    {
        var timeSpan = DateTime.UtcNow - dateTime;

        if (timeSpan.TotalMinutes < 1)
            return "À l'instant";
        if (timeSpan.TotalMinutes < 60)
            return $"Il y a {(int)timeSpan.TotalMinutes} min";
        if (timeSpan.TotalHours < 24)
            return $"Il y a {(int)timeSpan.TotalHours}h";
        if (timeSpan.TotalDays < 7)
            return $"Il y a {(int)timeSpan.TotalDays}j";
        
        return dateTime.ToString("dd/MM/yyyy");
    }

    private class ConversationDto
    {
        public int Id { get; set; }
        public string Subject { get; set; } = string.Empty;
        public string SellerName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? LastMessageAt { get; set; }
        public bool HasUnreadMessages { get; set; }
        public int UnreadCount { get; set; }
    }

    private class NewConversationModel
    {
        public string Subject { get; set; } = string.Empty;
        public string Type { get; set; } = "General";
        public string Message { get; set; } = string.Empty;
        public int? ProductId { get; set; }
        public int? OrderId { get; set; }
    }
}

