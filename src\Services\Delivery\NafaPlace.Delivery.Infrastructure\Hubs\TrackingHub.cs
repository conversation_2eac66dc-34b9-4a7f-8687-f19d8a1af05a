using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using NafaPlace.Delivery.Application.Services;
using System.Security.Claims;

namespace NafaPlace.Delivery.Infrastructure.Hubs;

/// <summary>
/// Hub SignalR pour le suivi GPS en temps réel
/// Gère les connexions temps réel entre livreurs, clients et système de suivi
/// </summary>
[Authorize]
public class TrackingHub : Hub
{
    private readonly ITrackingService _trackingService;
    private readonly ILogger<TrackingHub> _logger;

    public TrackingHub(ITrackingService trackingService, ILogger<TrackingHub> logger)
    {
        _trackingService = trackingService;
        _logger = logger;
    }

    /// <summary>
    /// Connexion d'un utilisateur au hub
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        var userRole = Context.User?.FindFirst(ClaimTypes.Role)?.Value;

        if (!string.IsNullOrEmpty(userId))
        {
            // Ajouter l'utilisateur à son groupe personnel
            await Groups.AddToGroupAsync(Context.ConnectionId, $"user_{userId}");

            // Si c'est un livreur, l'ajouter au groupe des livreurs
            if (userRole == "DeliveryPerson")
            {
                await Groups.AddToGroupAsync(Context.ConnectionId, "delivery_persons");

                // Récupérer et joindre les groupes de ses livraisons actives
                try
                {
                    var activeDeliveries = await _trackingService.GetActiveDeliveriesForPersonAsync(userId);
                    foreach (var delivery in activeDeliveries)
                    {
                        await Groups.AddToGroupAsync(Context.ConnectionId, $"delivery_{delivery.Id}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erreur lors de la récupération des livraisons actives pour {UserId}", userId);
                }
            }

            _logger.LogInformation("Utilisateur {UserId} ({Role}) connecté au hub de suivi", userId, userRole);
        }

        await base.OnConnectedAsync();
    }

    /// <summary>
    /// Déconnexion d'un utilisateur du hub
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;

        if (!string.IsNullOrEmpty(userId))
        {
            _logger.LogInformation("Utilisateur {UserId} déconnecté du hub de suivi", userId);
        }

        if (exception != null)
        {
            _logger.LogError(exception, "Déconnexion avec erreur pour l'utilisateur {UserId}", userId);
        }

        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// Rejoindre le groupe de suivi d'une livraison spécifique
    /// </summary>
    public async Task JoinDeliveryGroup(int deliveryId)
    {
        try
        {
            var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return;

            // Vérifier que l'utilisateur peut accéder à cette livraison
            var canAccess = await _trackingService.CanUserAccessDeliveryAsync(userId, deliveryId);
            if (!canAccess)
            {
                await Clients.Caller.SendAsync("Error", "Accès refusé à cette livraison");
                return;
            }

            await Groups.AddToGroupAsync(Context.ConnectionId, $"delivery_{deliveryId}");
            await Clients.Caller.SendAsync("JoinedDeliveryGroup", deliveryId);

            _logger.LogInformation("Utilisateur {UserId} a rejoint le groupe de la livraison {DeliveryId}", userId, deliveryId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ajout au groupe de livraison {DeliveryId}", deliveryId);
            await Clients.Caller.SendAsync("Error", "Erreur lors de l'abonnement aux mises à jour");
        }
    }

    /// <summary>
    /// Quitter le groupe de suivi d'une livraison
    /// </summary>
    public async Task LeaveDeliveryGroup(int deliveryId)
    {
        try
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"delivery_{deliveryId}");
            await Clients.Caller.SendAsync("LeftDeliveryGroup", deliveryId);

            var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            _logger.LogInformation("Utilisateur {UserId} a quitté le groupe de la livraison {DeliveryId}", userId, deliveryId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la sortie du groupe de livraison {DeliveryId}", deliveryId);
        }
    }

    /// <summary>
    /// Envoyer la position en temps réel (pour les livreurs uniquement)
    /// </summary>
    [Authorize(Roles = "DeliveryPerson")]
    public async Task SendPosition(double latitude, double longitude, double? speed = null, double? heading = null)
    {
        try
        {
            var deliveryPersonId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(deliveryPersonId))
                return;

            // Créer l'objet de position
            var positionData = new
            {
                DeliveryPersonId = deliveryPersonId,
                Latitude = latitude,
                Longitude = longitude,
                Speed = speed,
                Heading = heading,
                Timestamp = DateTime.UtcNow
            };

            // Récupérer les livraisons actives pour ce livreur
            var activeDeliveries = await _trackingService.GetActiveDeliveriesForPersonAsync(deliveryPersonId);

            // Diffuser la position à tous les groupes de livraisons actives
            foreach (var delivery in activeDeliveries)
            {
                await Clients.Group($"delivery_{delivery.Id}")
                    .SendAsync("PositionUpdated", new
                    {
                        DeliveryId = delivery.Id,
                        Position = positionData
                    });
            }

            // Confirmer l'envoi au livreur
            await Clients.Caller.SendAsync("PositionSent", positionData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de position");
            await Clients.Caller.SendAsync("Error", "Erreur lors de l'envoi de position");
        }
    }

    /// <summary>
    /// Demander la position actuelle d'une livraison
    /// </summary>
    public async Task RequestCurrentPosition(int deliveryId)
    {
        try
        {
            var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return;

            // Vérifier l'accès
            var canAccess = await _trackingService.CanUserAccessDeliveryAsync(userId, deliveryId);
            if (!canAccess)
            {
                await Clients.Caller.SendAsync("Error", "Accès refusé à cette livraison");
                return;
            }

            // Récupérer la position actuelle
            var currentPosition = await _trackingService.GetDeliveryCurrentPositionAsync(deliveryId);

            if (currentPosition != null)
            {
                await Clients.Caller.SendAsync("CurrentPosition", currentPosition);
            }
            else
            {
                await Clients.Caller.SendAsync("Error", "Position non disponible");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la demande de position pour la livraison {DeliveryId}", deliveryId);
            await Clients.Caller.SendAsync("Error", "Erreur lors de la récupération de position");
        }
    }

    /// <summary>
    /// Notifier un changement de statut de livraison
    /// </summary>
    [Authorize(Roles = "DeliveryPerson")]
    public async Task NotifyStatusChange(int deliveryId, string status, string? message = null)
    {
        try
        {
            var deliveryPersonId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(deliveryPersonId))
                return;

            var statusUpdate = new
            {
                DeliveryId = deliveryId,
                Status = status,
                Message = message,
                Timestamp = DateTime.UtcNow,
                DeliveryPersonId = deliveryPersonId
            };

            // Diffuser le changement de statut
            await Clients.Group($"delivery_{deliveryId}")
                .SendAsync("StatusChanged", statusUpdate);

            // Confirmer au livreur
            await Clients.Caller.SendAsync("StatusChangeConfirmed", statusUpdate);

            _logger.LogInformation("Statut changé pour la livraison {DeliveryId}: {Status}", deliveryId, status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du changement de statut pour la livraison {DeliveryId}", deliveryId);
            await Clients.Caller.SendAsync("Error", "Erreur lors du changement de statut");
        }
    }

    /// <summary>
    /// Envoyer un message du livreur au client
    /// </summary>
    [Authorize(Roles = "DeliveryPerson")]
    public async Task SendMessageToCustomer(int deliveryId, string message)
    {
        try
        {
            var deliveryPersonId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(deliveryPersonId))
                return;

            var messageData = new
            {
                DeliveryId = deliveryId,
                Message = message,
                FromDeliveryPerson = true,
                Timestamp = DateTime.UtcNow,
                DeliveryPersonId = deliveryPersonId
            };

            // Envoyer le message au groupe de la livraison
            await Clients.Group($"delivery_{deliveryId}")
                .SendAsync("MessageReceived", messageData);

            // Confirmer l'envoi
            await Clients.Caller.SendAsync("MessageSent", messageData);

            _logger.LogInformation("Message envoyé du livreur {DeliveryPersonId} pour la livraison {DeliveryId}",
                deliveryPersonId, deliveryId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de message pour la livraison {DeliveryId}", deliveryId);
            await Clients.Caller.SendAsync("Error", "Erreur lors de l'envoi du message");
        }
    }

    /// <summary>
    /// Calculer et envoyer l'ETA mis à jour
    /// </summary>
    public async Task RequestETA(int deliveryId)
    {
        try
        {
            var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return;

            // Vérifier l'accès
            var canAccess = await _trackingService.CanUserAccessDeliveryAsync(userId, deliveryId);
            if (!canAccess)
            {
                await Clients.Caller.SendAsync("Error", "Accès refusé à cette livraison");
                return;
            }

            // Calculer l'ETA
            var eta = await _trackingService.CalculateEstimatedArrivalTimeAsync(deliveryId);

            if (eta != null)
            {
                await Clients.Caller.SendAsync("ETAUpdated", new
                {
                    DeliveryId = deliveryId,
                    ETA = eta
                });
            }
            else
            {
                await Clients.Caller.SendAsync("Error", "Impossible de calculer l'ETA");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du calcul de l'ETA pour la livraison {DeliveryId}", deliveryId);
            await Clients.Caller.SendAsync("Error", "Erreur lors du calcul de l'ETA");
        }
    }

    /// <summary>
    /// Rejoindre le groupe des livreurs (pour la gestion centralisée)
    /// </summary>
    [Authorize(Roles = "Admin,Manager")]
    public async Task JoinDeliveryPersonsGroup()
    {
        try
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, "delivery_persons_monitoring");
            await Clients.Caller.SendAsync("JoinedDeliveryPersonsGroup");

            var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            _logger.LogInformation("Administrateur {UserId} a rejoint le groupe de surveillance des livreurs", userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ajout au groupe de surveillance");
            await Clients.Caller.SendAsync("Error", "Erreur lors de l'abonnement à la surveillance");
        }
    }

    /// <summary>
    /// Méthode pour les tests de connectivité
    /// </summary>
    public async Task Ping()
    {
        await Clients.Caller.SendAsync("Pong", DateTime.UtcNow);
    }
}