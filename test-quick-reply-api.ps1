# Script de test pour l'API Quick Replies
Write-Host "=== Test de l'API Quick Replies ===" -ForegroundColor Cyan

# 1. Login pour obtenir un token JWT
Write-Host "`n1. Connexion pour obtenir un token JWT..." -ForegroundColor Yellow
$loginBody = @{
    username = "admin"
    password = "Admin123!"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-WebRequest -Uri "http://localhost:5000/api/auth/login" `
        -Method POST `
        -ContentType "application/json" `
        -Body $loginBody

    $loginData = $loginResponse.Content | ConvertFrom-Json
    $token = if ($loginData.accessToken) { $loginData.accessToken } else { $loginData.token }

    if ([string]::IsNullOrEmpty($token)) {
        Write-Host "❌ Token non trouvé dans la réponse" -ForegroundColor Red
        Write-Host "Réponse complète: $($loginResponse.Content)" -ForegroundColor Yellow
        exit 1
    }

    Write-Host "✅ Token obtenu: $($token.Substring(0, 50))..." -ForegroundColor Green
} catch {
    Write-Host "❌ Erreur lors de la connexion: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Détails: $responseBody" -ForegroundColor Red
    }
    exit 1
}

# 2. Test GET - Récupérer les réponses rapides
Write-Host "`n2. Test GET - Récupération des réponses rapides..." -ForegroundColor Yellow
try {
    $headers = @{
        "Authorization" = "Bearer $token"
    }
    
    $getResponse = Invoke-WebRequest -Uri "http://localhost:5000/api/chat-ecommerce/api/support/quick-replies" `
        -Method GET `
        -Headers $headers
    
    Write-Host "✅ Status: $($getResponse.StatusCode)" -ForegroundColor Green
    Write-Host "Réponse: $($getResponse.Content)" -ForegroundColor White
} catch {
    Write-Host "❌ Erreur GET: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Détails: $responseBody" -ForegroundColor Red
    }
}

# 3. Test POST - Créer une réponse rapide
Write-Host "`n3. Test POST - Création d'une réponse rapide..." -ForegroundColor Yellow
$quickReplyBody = @{
    title = "Test API - Délai de livraison"
    content = "Nos délais de livraison sont de 2-5 jours ouvrables pour Conakry."
    category = "Livraison"
    tags = '["livraison","délai","conakry"]'
} | ConvertTo-Json

try {
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    $postResponse = Invoke-WebRequest -Uri "http://localhost:5000/api/chat-ecommerce/api/support/quick-replies" `
        -Method POST `
        -Headers $headers `
        -Body $quickReplyBody
    
    Write-Host "✅ Status: $($postResponse.StatusCode)" -ForegroundColor Green
    Write-Host "Réponse: $($postResponse.Content)" -ForegroundColor White
    
    $quickReplyId = $postResponse.Content
    Write-Host "✅ Réponse rapide créée avec ID: $quickReplyId" -ForegroundColor Green
} catch {
    Write-Host "❌ Erreur POST: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Détails: $responseBody" -ForegroundColor Red
    }
}

Write-Host "`n=== Test terminé ===" -ForegroundColor Cyan

