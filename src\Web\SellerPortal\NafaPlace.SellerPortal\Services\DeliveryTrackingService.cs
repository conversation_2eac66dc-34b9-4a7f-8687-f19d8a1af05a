using System.Net.Http.Json;
using System.Text.Json;
using Blazored.LocalStorage;
using System.Net.Http.Headers;
using NafaPlace.SellerPortal.Models.Delivery;

namespace NafaPlace.SellerPortal.Services;

public interface IDeliveryTrackingService
{
    Task<List<DeliveryOrderDto>> GetDeliveryOrdersAsync(int sellerId);
    Task<DeliveryOrderDto?> GetDeliveryOrderAsync(int orderId);
    Task<List<DeliveryTrackingEventDto>> GetTrackingEventsAsync(int orderId);
    Task<bool> UpdateDeliveryStatusAsync(int orderId, string status, string? notes = null);
    Task<DeliveryStatsDto> GetDeliveryStatsAsync(int sellerId);
}

public class DeliveryTrackingService : IDeliveryTrackingService
{
    private readonly HttpClient _httpClient;
    private readonly ILocalStorageService _localStorage;
    private readonly ILogger<DeliveryTrackingService> _logger;
    private readonly string _baseUrl = "http://localhost:5004"; // Order API

    public DeliveryTrackingService(HttpClient httpClient, ILocalStorageService localStorage, ILogger<DeliveryTrackingService> logger)
    {
        _httpClient = httpClient;
        _localStorage = localStorage;
        _logger = logger;
    }

    private async Task<string?> GetAuthTokenAsync()
    {
        try
        {
            return await _localStorage.GetItemAsync<string>("authToken");
        }
        catch
        {
            return null;
        }
    }

    private async Task SetAuthHeaderAsync()
    {
        var token = await GetAuthTokenAsync();
        if (!string.IsNullOrEmpty(token))
        {
            _httpClient.DefaultRequestHeaders.Authorization = 
                new AuthenticationHeaderValue("Bearer", token);
        }
    }

    public async Task<List<DeliveryOrderDto>> GetDeliveryOrdersAsync(int sellerId)
    {
        try
        {
            await SetAuthHeaderAsync();
            
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/orders/seller/{sellerId}");
            
            if (response.IsSuccessStatusCode)
            {
                var orders = await response.Content.ReadFromJsonAsync<List<DeliveryOrderDto>>();
                return orders ?? new List<DeliveryOrderDto>();
            }
            
            _logger.LogWarning("Échec de récupération des commandes de livraison: {StatusCode}", response.StatusCode);
            return new List<DeliveryOrderDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des commandes de livraison");
            return new List<DeliveryOrderDto>();
        }
    }

    public async Task<DeliveryOrderDto?> GetDeliveryOrderAsync(int orderId)
    {
        try
        {
            await SetAuthHeaderAsync();
            
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/orders/{orderId}");
            
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<DeliveryOrderDto>();
            }
            
            _logger.LogWarning("Échec de récupération de la commande de livraison {OrderId}: {StatusCode}", 
                orderId, response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de la commande de livraison {OrderId}", orderId);
            return null;
        }
    }

    public async Task<List<DeliveryTrackingEventDto>> GetTrackingEventsAsync(int orderId)
    {
        try
        {
            await SetAuthHeaderAsync();
            
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/delivery/orders/{orderId}/tracking");
            
            if (response.IsSuccessStatusCode)
            {
                var events = await response.Content.ReadFromJsonAsync<List<DeliveryTrackingEventDto>>();
                return events ?? new List<DeliveryTrackingEventDto>();
            }
            
            _logger.LogWarning("Échec de récupération des événements de suivi: {StatusCode}", response.StatusCode);
            return new List<DeliveryTrackingEventDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des événements de suivi");
            return new List<DeliveryTrackingEventDto>();
        }
    }

    public async Task<bool> UpdateDeliveryStatusAsync(int orderId, string status, string? notes = null)
    {
        try
        {
            await SetAuthHeaderAsync();
            
            var request = new
            {
                Status = status,
                Notes = notes,
                EventDate = DateTime.UtcNow
            };
            
            var response = await _httpClient.PutAsJsonAsync($"{_baseUrl}/api/delivery/orders/{orderId}/status", request);
            
            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Statut de livraison mis à jour avec succès pour la commande {OrderId}", orderId);
                return true;
            }
            
            _logger.LogWarning("Échec de mise à jour du statut de livraison: {StatusCode}", response.StatusCode);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour du statut de livraison");
            return false;
        }
    }

    public async Task<DeliveryStatsDto> GetDeliveryStatsAsync(int sellerId)
    {
        try
        {
            await SetAuthHeaderAsync();
            
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/delivery/stats/seller/{sellerId}");
            
            if (response.IsSuccessStatusCode)
            {
                var stats = await response.Content.ReadFromJsonAsync<DeliveryStatsDto>();
                return stats ?? new DeliveryStatsDto { InTransitCount = 0, DeliveredCount = 0, DelayedCount = 0, DeliveryRate = 0 };
            }
            
            _logger.LogWarning("Échec de récupération des statistiques de livraison: {StatusCode}", response.StatusCode);
            return new DeliveryStatsDto { InTransitCount = 0, DeliveredCount = 0, DelayedCount = 0, DeliveryRate = 0 };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des statistiques de livraison");
            return new DeliveryStatsDto { InTransitCount = 0, DeliveredCount = 0, DelayedCount = 0, DeliveryRate = 0 };
        }
    }


}
