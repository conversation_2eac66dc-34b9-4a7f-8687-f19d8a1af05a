@using Microsoft.JSInterop
@using NafaPlace.Web.Models.Common
@inject IJSRuntime JSRuntime
@inject HttpClient HttpClient

<div class="intelligent-chatbot @(IsVisible ? "visible" : "hidden")">
    <!-- Chat Toggle Button -->
    @if (!IsVisible)
    {
        <button class="chat-toggle-btn @(HasNewMessage ? "has-notification" : "")" @onclick="ToggleChat">
            <i class="fas fa-comments"></i>
            @if (HasNewMessage)
            {
                <span class="notification-dot"></span>
            }
        </button>
    }

    <!-- Chat Window -->
    @if (IsVisible)
    {
        <div class="chat-window">
            <!-- Chat Header -->
            <div class="chat-header">
                <div class="chat-title">
                    <div class="bot-avatar">
                        <img src="/images/chatbot-avatar.svg" alt="NafaBot" />
                    </div>
                    <div class="bot-info">
                        <h6 class="bot-name">NafaBot</h6>
                        <span class="bot-status @(IsOnline ? "online" : "offline")">
                            @(IsOnline ? "En ligne" : "Hors ligne")
                        </span>
                    </div>
                </div>
                <div class="chat-actions">
                    <button class="btn btn-sm btn-outline-light" @onclick="MinimizeChat" title="Réduire">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-light" @onclick="CloseChat" title="Fermer">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <!-- Chat Messages -->
            <div class="chat-messages" @ref="MessagesContainer">
                @if (IsLoading && Messages.Count == 0)
                {
                    <div class="chat-loading">
                        <div class="typing-indicator">
                            <div class="typing-dots">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                            <span class="typing-text">NafaBot se connecte...</span>
                        </div>
                    </div>
                }

                @foreach (var message in Messages)
                {
                    <div class="message @(message.IsFromBot ? "bot-message" : "user-message")">
                        @if (message.IsFromBot)
                        {
                            <div class="message-avatar">
                                <img src="/images/chatbot-avatar.svg" alt="NafaBot" />
                            </div>
                        }

                        <div class="message-content">
                            @if (message.MessageType == "text")
                            {
                                <div class="message-bubble">
                                    @((MarkupString)FormatMessageText(message.Content))
                                </div>
                            }
                            else if (message.MessageType == "quick_replies")
                            {
                                <div class="message-bubble">
                                    @((MarkupString)FormatMessageText(message.Content))
                                </div>
                                <div class="quick-replies">
                                    @foreach (var reply in message.QuickReplies ?? new List<QuickReplyDto>())
                                    {
                                        <button class="quick-reply-btn" @onclick="() => SendQuickReply(reply)">
                                            @if (!string.IsNullOrEmpty(reply.Icon))
                                            {
                                                <i class="@reply.Icon"></i>
                                            }
                                            @reply.Title
                                        </button>
                                    }
                                </div>
                            }
                            else if (message.MessageType == "product_carousel")
                            {
                                <div class="message-bubble">
                                    @((MarkupString)FormatMessageText(message.Content))
                                </div>
                                <div class="product-carousel">
                                    @foreach (var product in message.Products ?? new List<ProductDto>())
                                    {
                                        <div class="product-card" @onclick="() => ViewProduct(product.Id)">
                                            <img src="@product.ImageUrl" alt="@product.Name" class="product-image" />
                                            <div class="product-info">
                                                <h6 class="product-name">@product.Name</h6>
                                                <div class="product-price">@product.Price.ToString("N0") GNF</div>
                                                @if (product.DiscountPercentage > 0)
                                                {
                                                    <div class="product-discount">-@product.DiscountPercentage%</div>
                                                }
                                            </div>
                                        </div>
                                    }
                                </div>
                            }
                            else if (message.MessageType == "order_status")
                            {
                                <div class="order-status-card">
                                    <div class="order-header">
                                        <h6>Commande #@message.OrderInfo?.OrderNumber</h6>
                                        <span class="order-status @message.OrderInfo?.Status?.ToLower()">
                                            @GetOrderStatusText(message.OrderInfo?.Status)
                                        </span>
                                    </div>
                                    <div class="order-details">
                                        <p><strong>Total:</strong> @message.OrderInfo?.Total.ToString("N0") GNF</p>
                                        <p><strong>Date:</strong> @message.OrderInfo?.OrderDate.ToString("dd/MM/yyyy")</p>
                                        @if (!string.IsNullOrEmpty(message.OrderInfo?.TrackingNumber))
                                        {
                                            <p><strong>Suivi:</strong> @message.OrderInfo.TrackingNumber</p>
                                        }
                                    </div>
                                    @if (!string.IsNullOrEmpty(message.OrderInfo?.OrderId))
                                    {
                                        <button class="btn btn-sm btn-primary" @onclick="() => ViewOrder(message.OrderInfo.OrderId)">
                                            Voir la commande
                                        </button>
                                    }
                                </div>
                            }

                            <div class="message-time">
                                @message.Timestamp.ToString("HH:mm")
                            </div>
                        </div>
                    </div>
                }

                @if (IsTyping)
                {
                    <div class="message bot-message">
                        <div class="message-avatar">
                            <img src="/images/chatbot-avatar.svg" alt="NafaBot" />
                        </div>
                        <div class="message-content">
                            <div class="typing-indicator">
                                <div class="typing-dots">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>

            <!-- Suggested Actions -->
            @if (SuggestedActions.Any() && !IsTyping)
            {
                <div class="suggested-actions">
                    @foreach (var action in SuggestedActions)
                    {
                        <button class="action-btn" @onclick="() => ExecuteAction(action)">
                            <i class="@action.Icon"></i>
                            @action.Title
                        </button>
                    }
                </div>
            }

            <!-- Chat Input -->
            <div class="chat-input-container">
                @if (ShowFileUpload)
                {
                    <div class="file-upload-area">
                        <input type="file" id="chatFileUpload" @onchange="HandleFileUpload" accept="image/*,.pdf,.doc,.docx" multiple style="display: none;" />
                        <button class="btn btn-outline-secondary btn-sm" @onclick="@(() => JSRuntime.InvokeVoidAsync("clickElement", "chatFileUpload"))">
                            <i class="fas fa-paperclip me-1"></i>
                            Joindre un fichier
                        </button>
                    </div>
                }

                <div class="chat-input-wrapper">
                    <div class="input-with-suggestions">
                        <input @bind="CurrentMessage"
                               @bind:event="oninput"
                               @onkeypress="HandleKeyPress"
                               @onfocus="OnInputFocus"
                               @onblur="OnInputBlur"
                               class="chat-input"
                               placeholder="Écrivez votre message..."
                               disabled="@(!IsOnline || IsTyping)"
                               maxlength="1000" />

                        @if (ShowSuggestions && InputSuggestions.Any())
                        {
                            <div class="input-suggestions">
                                @foreach (var suggestion in InputSuggestions.Take(5))
                                {
                                    <div class="suggestion-item" @onclick="() => SelectSuggestion(suggestion)">
                                        <i class="fas fa-lightbulb"></i>
                                        @suggestion
                                    </div>
                                }
                            </div>
                        }
                    </div>

                    <div class="input-actions">
                        <button class="btn btn-link btn-sm" @onclick="ToggleFileUpload" title="Joindre un fichier">
                            <i class="fas fa-paperclip"></i>
                        </button>
                        <button class="btn btn-link btn-sm" @onclick="StartVoiceInput" title="Message vocal">
                            <i class="fas fa-microphone @(IsListening ? "listening" : "")"></i>
                        </button>
                        <button class="btn btn-primary btn-sm" @onclick="SendMessage" disabled="@(string.IsNullOrEmpty(CurrentMessage?.Trim()) || IsTyping)">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>

                @if (AttachedFiles.Any())
                {
                    <div class="attached-files">
                        @foreach (var file in AttachedFiles)
                        {
                            <div class="attached-file">
                                <i class="fas fa-file"></i>
                                <span>@file.Name</span>
                                <button class="remove-file" @onclick="() => RemoveAttachedFile(file)">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        }
                    </div>
                }

                <div class="chat-footer">
                    <small class="text-muted">
                        Propulsé par l'IA NafaPlace •
                        <a href="#" @onclick="ShowFeedbackModal">Votre avis</a>
                    </small>
                </div>
            </div>
        </div>
    }
</div>

<!-- Feedback Modal -->
@if (ShowFeedback)
{
    <div class="modal fade show d-block" tabindex="-1" style="background: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title">Évaluer NafaBot</h6>
                    <button type="button" class="btn-close" @onclick="CloseFeedbackModal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <p>Comment évalueriez-vous votre expérience avec NafaBot?</p>
                        <div class="rating-buttons">
                            @for (int i = 1; i <= 5; i++)
                            {
                                var rating = i;
                                <button class="rating-btn @(FeedbackRating == rating ? "active" : "")"
                                        @onclick="() => SetFeedbackRating(rating)">
                                    <i class="fas fa-star"></i>
                                </button>
                            }
                        </div>
                    </div>
                    <div class="mb-3">
                        <textarea @bind="FeedbackComment" class="form-control" rows="3"
                                  placeholder="Commentaire (optionnel)"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary btn-sm" @onclick="CloseFeedbackModal">
                        Annuler
                    </button>
                    <button type="button" class="btn btn-primary btn-sm" @onclick="SubmitFeedback"
                            disabled="@(FeedbackRating == 0 || IsSubmittingFeedback)">
                        @if (IsSubmittingFeedback)
                        {
                            <span class="spinner-border spinner-border-sm me-1"></span>
                        }
                        Envoyer
                    </button>
                </div>
            </div>
        </div>
    </div>
}

<style>
    .intelligent-chatbot {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .chat-toggle-btn {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #E73C30, #F96302);
        color: white;
        border: none;
        font-size: 24px;
        cursor: pointer;
        box-shadow: 0 4px 20px rgba(231, 60, 48, 0.3);
        transition: all 0.3s ease;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .chat-toggle-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 25px rgba(231, 60, 48, 0.4);
    }

    .chat-toggle-btn.has-notification {
        animation: pulse-notification 2s infinite;
    }

    .notification-dot {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 12px;
        height: 12px;
        background: #28a745;
        border-radius: 50%;
        border: 2px solid white;
    }

    @@keyframes pulse-notification {
        0% { box-shadow: 0 4px 20px rgba(231, 60, 48, 0.3); }
        50% { box-shadow: 0 6px 30px rgba(231, 60, 48, 0.6); }
        100% { box-shadow: 0 4px 20px rgba(231, 60, 48, 0.3); }
    }

    .chat-window {
        width: 380px;
        height: 600px;
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        display: flex;
        flex-direction: column;
        overflow: hidden;
        border: 1px solid #e0e0e0;
    }

    .chat-header {
        background: linear-gradient(135deg, #E73C30, #F96302);
        color: white;
        padding: 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .chat-title {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .bot-avatar img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .bot-name {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .bot-status {
        font-size: 0.8rem;
        opacity: 0.9;
    }

    .bot-status.online {
        color: #a8f5a8;
    }

    .bot-status.offline {
        color: #ffcccb;
    }

    .chat-actions {
        display: flex;
        gap: 0.5rem;
    }

    .chat-actions .btn {
        width: 32px;
        height: 32px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-color: rgba(255, 255, 255, 0.3);
    }

    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 1rem;
        background: #f8f9fa;
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .message {
        display: flex;
        gap: 0.75rem;
        max-width: 85%;
    }

    .message.user-message {
        align-self: flex-end;
        flex-direction: row-reverse;
    }

    .message-avatar img {
        width: 32px;
        height: 32px;
        border-radius: 50%;
    }

    .message-content {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .message-bubble {
        padding: 0.75rem 1rem;
        border-radius: 18px;
        max-width: 280px;
        word-wrap: break-word;
        line-height: 1.4;
    }

    .bot-message .message-bubble {
        background: white;
        color: #333;
        border: 1px solid #e0e0e0;
    }

    .user-message .message-bubble {
        background: linear-gradient(135deg, #E73C30, #F96302);
        color: white;
        margin-left: auto;
    }

    .message-time {
        font-size: 0.7rem;
        color: #999;
        margin-top: 0.25rem;
    }

    .user-message .message-time {
        text-align: right;
    }

    .typing-indicator {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        background: white;
        border-radius: 18px;
        border: 1px solid #e0e0e0;
    }

    .typing-dots {
        display: flex;
        gap: 0.25rem;
    }

    .typing-dots span {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: #999;
        animation: typing 1.4s infinite;
    }

    .typing-dots span:nth-child(2) {
        animation-delay: 0.2s;
    }

    .typing-dots span:nth-child(3) {
        animation-delay: 0.4s;
    }

    @@keyframes typing {
        0%, 60%, 100% { transform: translateY(0); }
        30% { transform: translateY(-10px); }
    }

    .quick-replies {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-top: 0.5rem;
    }

    .quick-reply-btn {
        padding: 0.5rem 1rem;
        background: white;
        border: 1px solid #E73C30;
        color: #E73C30;
        border-radius: 20px;
        font-size: 0.85rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .quick-reply-btn:hover {
        background: #E73C30;
        color: white;
    }

    .product-carousel {
        display: flex;
        gap: 0.75rem;
        overflow-x: auto;
        padding: 0.5rem 0;
        margin-top: 0.5rem;
    }

    .product-card {
        min-width: 150px;
        background: white;
        border-radius: 12px;
        border: 1px solid #e0e0e0;
        cursor: pointer;
        transition: transform 0.3s ease;
    }

    .product-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .product-image {
        width: 100%;
        height: 100px;
        object-fit: cover;
        border-radius: 12px 12px 0 0;
    }

    .product-info {
        padding: 0.75rem;
    }

    .product-name {
        font-size: 0.85rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #333;
    }

    .product-price {
        font-size: 0.9rem;
        font-weight: 600;
        color: #E73C30;
    }

    .product-discount {
        font-size: 0.75rem;
        color: #28a745;
        font-weight: 600;
    }

    .order-status-card {
        background: white;
        border-radius: 12px;
        border: 1px solid #e0e0e0;
        padding: 1rem;
        margin-top: 0.5rem;
    }

    .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
    }

    .order-status {
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .order-status.pending {
        background: #fff3cd;
        color: #856404;
    }

    .order-status.processing {
        background: #cce5ff;
        color: #004085;
    }

    .order-status.shipped {
        background: #d4edda;
        color: #155724;
    }

    .order-status.delivered {
        background: #d1ecf1;
        color: #0c5460;
    }

    .suggested-actions {
        padding: 0.75rem 1rem;
        background: white;
        border-top: 1px solid #e0e0e0;
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .action-btn {
        padding: 0.5rem 1rem;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        color: #495057;
        border-radius: 20px;
        font-size: 0.85rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .action-btn:hover {
        background: #E73C30;
        border-color: #E73C30;
        color: white;
    }

    .chat-input-container {
        background: white;
        border-top: 1px solid #e0e0e0;
        padding: 1rem;
    }

    .input-with-suggestions {
        position: relative;
    }

    .chat-input-wrapper {
        display: flex;
        align-items: flex-end;
        gap: 0.75rem;
    }

    .chat-input {
        flex: 1;
        border: 1px solid #dee2e6;
        border-radius: 25px;
        padding: 0.75rem 1rem;
        outline: none;
        resize: none;
        font-size: 0.9rem;
        transition: border-color 0.3s ease;
    }

    .chat-input:focus {
        border-color: #E73C30;
    }

    .input-actions {
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .input-actions .btn {
        width: 36px;
        height: 36px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
    }

    .fa-microphone.listening {
        color: #dc3545;
        animation: pulse 1s infinite;
    }

    .input-suggestions {
        position: absolute;
        bottom: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 12px;
        margin-bottom: 0.5rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        max-height: 150px;
        overflow-y: auto;
    }

    .suggestion-item {
        padding: 0.75rem 1rem;
        cursor: pointer;
        transition: background-color 0.2s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.9rem;
    }

    .suggestion-item:hover {
        background: #f8f9fa;
    }

    .suggestion-item i {
        color: #ffc107;
    }

    .file-upload-area {
        margin-bottom: 0.75rem;
        text-align: center;
    }

    .attached-files {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-top: 0.5rem;
    }

    .attached-file {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: #f8f9fa;
        padding: 0.5rem;
        border-radius: 20px;
        font-size: 0.85rem;
    }

    .remove-file {
        background: none;
        border: none;
        color: #dc3545;
        cursor: pointer;
        padding: 0;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .chat-footer {
        text-align: center;
        margin-top: 0.75rem;
    }

    .chat-footer a {
        color: #E73C30;
        text-decoration: none;
    }

    .rating-buttons {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        margin: 1rem 0;
    }

    .rating-btn {
        background: none;
        border: none;
        font-size: 1.5rem;
        color: #ddd;
        cursor: pointer;
        transition: color 0.3s ease;
    }

    .rating-btn:hover,
    .rating-btn.active {
        color: #ffc107;
    }

    /* Responsive */
    @@media (max-width: 768px) {
        .intelligent-chatbot {
            bottom: 10px;
            right: 10px;
            left: 10px;
        }

        .chat-window {
            width: 100%;
            height: calc(100vh - 20px);
            border-radius: 0;
        }

        .chat-toggle-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            left: auto;
        }
    }

    .hidden {
        display: none;
    }

    .visible {
        display: block;
    }
</style>

@code {
    [Parameter] public string? UserId { get; set; }
    [Parameter] public bool AutoStart { get; set; } = true;

    // State
    private bool IsVisible = false;
    private bool IsOnline = true;
    private bool IsLoading = false;
    private bool IsTyping = false;
    private bool IsListening = false;
    private bool HasNewMessage = false;
    private bool ShowSuggestions = false;
    private bool ShowFileUpload = false;
    private bool ShowFeedback = false;
    private bool IsSubmittingFeedback = false;

    // Messages and conversation
    private List<ChatMessageDto> Messages = new();
    private string CurrentMessage = "";
    private string SessionId = "";
    private List<string> InputSuggestions = new();
    private List<SuggestedActionDto> SuggestedActions = new();
    private List<AttachedFileDto> AttachedFiles = new();

    // Feedback
    private int FeedbackRating = 0;
    private string FeedbackComment = "";

    // References
    private ElementReference MessagesContainer;

    protected override async Task OnInitializedAsync()
    {
        SessionId = Guid.NewGuid().ToString();

        if (AutoStart)
        {
            await Task.Delay(2000); // Wait 2 seconds before showing
            await StartConversation();
        }

        // Load conversation history if user is logged in
        if (!string.IsNullOrEmpty(UserId))
        {
            await LoadConversationHistory();
        }
    }

    private async Task StartConversation()
    {
        try
        {
            IsLoading = true;

            var response = await HttpClient.PostAsJsonAsync("/api/chat/start", new { SessionId, UserId });
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ChatStartResponseDto>();
                if (result != null)
                {
                    Messages.Add(new ChatMessageDto
                    {
                        Content = result.WelcomeMessage,
                        IsFromBot = true,
                        MessageType = "quick_replies",
                        QuickReplies = result.InitialQuickReplies,
                        Timestamp = DateTime.Now
                    });

                    SuggestedActions = result.SuggestedActions ?? new List<SuggestedActionDto>();
                }
            }

            HasNewMessage = !IsVisible;
            StateHasChanged();
            await ScrollToBottom();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error starting conversation: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadConversationHistory()
    {
        try
        {
            var response = await HttpClient.GetAsync($"/api/chat/history?sessionId={SessionId}&userId={UserId}");
            if (response.IsSuccessStatusCode)
            {
                var history = await response.Content.ReadFromJsonAsync<List<ChatMessageDto>>();
                if (history?.Any() == true)
                {
                    Messages = history;
                    StateHasChanged();
                    await ScrollToBottom();
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading conversation history: {ex.Message}");
        }
    }

    private async Task SendMessage()
    {
        if (string.IsNullOrEmpty(CurrentMessage?.Trim()) || IsTyping)
            return;

        var userMessage = CurrentMessage.Trim();
        CurrentMessage = "";
        ShowSuggestions = false;

        // Add user message
        Messages.Add(new ChatMessageDto
        {
            Content = userMessage,
            IsFromBot = false,
            MessageType = "text",
            Timestamp = DateTime.Now
        });

        StateHasChanged();
        await ScrollToBottom();

        // Send to bot
        await ProcessUserMessage(userMessage);
    }

    private async Task ProcessUserMessage(string message)
    {
        try
        {
            IsTyping = true;
            StateHasChanged();

            var requestDto = new ChatRequestDto
            {
                SessionId = SessionId,
                UserId = UserId,
                Message = message,
                AttachedFiles = AttachedFiles.ToList()
            };

            var response = await HttpClient.PostAsJsonAsync("/api/chat/message", requestDto);

            // Simulate typing delay
            await Task.Delay(1500);

            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ChatResponseDto>();
                if (result != null)
                {
                    // Add bot response
                    Messages.Add(new ChatMessageDto
                    {
                        Content = result.Message,
                        IsFromBot = true,
                        MessageType = result.MessageType,
                        QuickReplies = result.QuickReplies,
                        Products = result.Products,
                        OrderInfo = result.OrderInfo,
                        Timestamp = DateTime.Now
                    });

                    // Update suggestions and actions
                    SuggestedActions = result.SuggestedActions ?? new List<SuggestedActionDto>();

                    // Clear attached files
                    AttachedFiles.Clear();
                }
            }
            else
            {
                // Error response
                Messages.Add(new ChatMessageDto
                {
                    Content = "Désolé, j'ai rencontré un problème. Pouvez-vous reformuler votre question?",
                    IsFromBot = true,
                    MessageType = "text",
                    Timestamp = DateTime.Now
                });
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error processing message: {ex.Message}");
            Messages.Add(new ChatMessageDto
            {
                Content = "Une erreur s'est produite. Veuillez réessayer.",
                IsFromBot = true,
                MessageType = "text",
                Timestamp = DateTime.Now
            });
        }
        finally
        {
            IsTyping = false;
            StateHasChanged();
            await ScrollToBottom();
        }
    }

    private async Task SendQuickReply(QuickReplyDto reply)
    {
        CurrentMessage = reply.Payload ?? reply.Title;
        await SendMessage();
    }

    private async Task ExecuteAction(SuggestedActionDto action)
    {
        switch (action.ActionType?.ToLower())
        {
            case "search":
                CurrentMessage = $"Rechercher {action.Parameters?["query"]}";
                break;
            case "order_status":
                CurrentMessage = "Vérifier le statut de ma commande";
                break;
            case "product_info":
                CurrentMessage = $"Informations sur le produit {action.Parameters?["productId"]}";
                break;
            case "help":
                CurrentMessage = "Aide";
                break;
            default:
                CurrentMessage = action.Title;
                break;
        }

        await SendMessage();
    }

    private async Task HandleKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter" && !e.ShiftKey)
        {
            await SendMessage();
        }
    }

    private async Task OnInputFocus()
    {
        if (!string.IsNullOrEmpty(CurrentMessage))
        {
            await LoadInputSuggestions();
        }
    }

    private void OnInputBlur()
    {
        Task.Delay(200).ContinueWith(_ => InvokeAsync(() =>
        {
            ShowSuggestions = false;
            StateHasChanged();
        }));
    }

    private async Task LoadInputSuggestions()
    {
        try
        {
            var response = await HttpClient.GetAsync($"/api/chat/suggestions?query={Uri.EscapeDataString(CurrentMessage)}");
            if (response.IsSuccessStatusCode)
            {
                InputSuggestions = await response.Content.ReadFromJsonAsync<List<string>>() ?? new List<string>();
                ShowSuggestions = InputSuggestions.Any();
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading suggestions: {ex.Message}");
        }
    }

    private void SelectSuggestion(string suggestion)
    {
        CurrentMessage = suggestion;
        ShowSuggestions = false;
        StateHasChanged();
    }

    private async Task StartVoiceInput()
    {
        try
        {
            IsListening = true;
            StateHasChanged();

            var result = await JSRuntime.InvokeAsync<string>("voiceInput.start");
            if (!string.IsNullOrEmpty(result))
            {
                CurrentMessage = result;
                await SendMessage();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Voice input error: {ex.Message}");
        }
        finally
        {
            IsListening = false;
            StateHasChanged();
        }
    }

    private void ToggleFileUpload()
    {
        ShowFileUpload = !ShowFileUpload;
        StateHasChanged();
    }

    private async Task HandleFileUpload(ChangeEventArgs e)
    {
        // Handle file upload logic
        Console.WriteLine("File upload triggered");
    }

    private void RemoveAttachedFile(AttachedFileDto file)
    {
        AttachedFiles.Remove(file);
        StateHasChanged();
    }

    private void ToggleChat()
    {
        IsVisible = !IsVisible;
        HasNewMessage = false;

        if (IsVisible && Messages.Count == 0)
        {
            _ = Task.Run(StartConversation);
        }

        StateHasChanged();
    }

    private void MinimizeChat()
    {
        IsVisible = false;
        StateHasChanged();
    }

    private void CloseChat()
    {
        IsVisible = false;
        Messages.Clear();
        CurrentMessage = "";
        SuggestedActions.Clear();
        AttachedFiles.Clear();
        StateHasChanged();
    }

    private async Task ScrollToBottom()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("scrollToBottom", MessagesContainer);
        }
        catch
        {
            // Ignore scrolling errors
        }
    }

    private void ViewProduct(int productId)
    {
        JSRuntime.InvokeVoidAsync("window.open", $"/product/{productId}", "_blank");
    }

    private void ViewOrder(string orderId)
    {
        JSRuntime.InvokeVoidAsync("window.open", $"/orders/{orderId}", "_blank");
    }

    private void ShowFeedbackModal()
    {
        ShowFeedback = true;
        StateHasChanged();
    }

    private void CloseFeedbackModal()
    {
        ShowFeedback = false;
        FeedbackRating = 0;
        FeedbackComment = "";
        StateHasChanged();
    }

    private void SetFeedbackRating(int rating)
    {
        FeedbackRating = rating;
        StateHasChanged();
    }

    private async Task SubmitFeedback()
    {
        if (FeedbackRating == 0 || IsSubmittingFeedback)
            return;

        try
        {
            IsSubmittingFeedback = true;

            var feedbackDto = new ChatFeedbackDto
            {
                SessionId = SessionId,
                UserId = UserId,
                Rating = FeedbackRating,
                Comment = FeedbackComment,
                Timestamp = DateTime.UtcNow
            };

            var response = await HttpClient.PostAsJsonAsync("/api/chat/feedback", feedbackDto);
            if (response.IsSuccessStatusCode)
            {
                await JSRuntime.InvokeVoidAsync("showToast", "Merci pour votre retour!", "success");
            }

            CloseFeedbackModal();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error submitting feedback: {ex.Message}");
        }
        finally
        {
            IsSubmittingFeedback = false;
            StateHasChanged();
        }
    }

    private string FormatMessageText(string text)
    {
        // Convert markdown-like formatting to HTML
        text = text.Replace("**", "<strong>", StringComparison.Ordinal).Replace("**", "</strong>", StringComparison.Ordinal);
        text = text.Replace("*", "<em>", StringComparison.Ordinal).Replace("*", "</em>", StringComparison.Ordinal);
        text = text.Replace("\n", "<br>");
        return text;
    }

    private string GetOrderStatusText(string? status)
    {
        return status?.ToLower() switch
        {
            "pending" => "En attente",
            "processing" => "En traitement",
            "shipped" => "Expédiée",
            "delivered" => "Livrée",
            "cancelled" => "Annulée",
            _ => "Inconnue"
        };
    }

    // DTOs
    public class ChatMessageDto
    {
        public string Content { get; set; } = "";
        public bool IsFromBot { get; set; }
        public string MessageType { get; set; } = "text";
        public List<QuickReplyDto>? QuickReplies { get; set; }
        public List<ProductDto>? Products { get; set; }
        public OrderInfoDto? OrderInfo { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class ChatStartResponseDto
    {
        public string WelcomeMessage { get; set; } = "";
        public List<QuickReplyDto>? InitialQuickReplies { get; set; }
        public List<SuggestedActionDto>? SuggestedActions { get; set; }
    }

    public class ChatRequestDto
    {
        public string SessionId { get; set; } = "";
        public string? UserId { get; set; }
        public string Message { get; set; } = "";
        public List<AttachedFileDto> AttachedFiles { get; set; } = new();
    }

    public class ChatResponseDto
    {
        public string Message { get; set; } = "";
        public string MessageType { get; set; } = "text";
        public List<QuickReplyDto>? QuickReplies { get; set; }
        public List<ProductDto>? Products { get; set; }
        public OrderInfoDto? OrderInfo { get; set; }
        public List<SuggestedActionDto>? SuggestedActions { get; set; }
    }

    public class QuickReplyDto
    {
        public string Title { get; set; } = "";
        public string? Payload { get; set; }
        public string? Icon { get; set; }
    }

    public class ProductDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public decimal Price { get; set; }
        public string ImageUrl { get; set; } = "";
        public int DiscountPercentage { get; set; }
    }

    public class OrderInfoDto
    {
        public string OrderId { get; set; } = "";
        public string OrderNumber { get; set; } = "";
        public string Status { get; set; } = "";
        public decimal Total { get; set; }
        public DateTime OrderDate { get; set; }
        public string? TrackingNumber { get; set; }
    }

    public class SuggestedActionDto
    {
        public string Title { get; set; } = "";
        public string Icon { get; set; } = "";
        public string? ActionType { get; set; }
        public Dictionary<string, string>? Parameters { get; set; }
    }

    public class AttachedFileDto
    {
        public string Name { get; set; } = "";
        public string Type { get; set; } = "";
        public long Size { get; set; }
        public string Data { get; set; } = "";
    }

    public class ChatFeedbackDto
    {
        public string SessionId { get; set; } = "";
        public string? UserId { get; set; }
        public int Rating { get; set; }
        public string Comment { get; set; } = "";
        public DateTime Timestamp { get; set; }
    }
}

<script>
    window.scrollToBottom = function (element) {
        if (element) {
            element.scrollTop = element.scrollHeight;
        }
    };

    window.voiceInput = {
        start: function () {
            return new Promise((resolve, reject) => {
                if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
                    reject('Speech recognition not supported');
                    return;
                }

                const recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
                recognition.continuous = false;
                recognition.interimResults = false;
                recognition.lang = 'fr-FR';

                recognition.onresult = function (event) {
                    const result = event.results[0][0].transcript;
                    resolve(result);
                };

                recognition.onerror = function (event) {
                    reject(event.error);
                };

                recognition.onend = function () {
                    // Recognition ended
                };

                recognition.start();
            });
        }
    };

    window.showToast = function (message, type) {
        console.log(`${type}: ${message}`);
    };
</script>