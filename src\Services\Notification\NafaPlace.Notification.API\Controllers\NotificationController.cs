using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Notification.Application.DTOs;
using NafaPlace.Notification.Application.Services;
using System.Security.Claims;

namespace NafaPlace.Notification.API.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
[Authorize]
public class NotificationController : ControllerBase
{
    private readonly INotificationService _notificationService;
    private readonly IRealTimeNotificationService _realTimeService;
    private readonly IPushNotificationService _pushService;
    private readonly IEmailService _emailService;
    private readonly ILogger<NotificationController> _logger;

    public NotificationController(
        INotificationService notificationService,
        IRealTimeNotificationService realTimeService,
        IPushNotificationService pushService,
        IEmailService emailService,
        ILogger<NotificationController> logger)
    {
        _notificationService = notificationService;
        _realTimeService = realTimeService;
        _pushService = pushService;
        _emailService = emailService;
        _logger = logger;
    }

    [HttpGet]
    public async Task<ActionResult<List<NotificationDto>>> GetNotifications(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] NotificationType? type = null,
        [FromQuery] bool? isRead = null)
    {
        try
        {
            var userId = GetUserId();
            var filter = new NotificationFilterDto
            {
                RecipientId = userId,
                Type = type,
                IsRead = isRead,
                Page = page,
                PageSize = pageSize
            };

            var notifications = await _notificationService.GetNotificationsAsync(filter);
            return Ok(notifications);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des notifications");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("unread-count")]
    public async Task<ActionResult<int>> GetUnreadCount()
    {
        try
        {
            var userId = GetUserId();
            var count = await _notificationService.GetUnreadCountAsync(userId);
            return Ok(count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du comptage des notifications non lues");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpPost("{id}/mark-read")]
    public async Task<ActionResult> MarkAsRead(int id)
    {
        try
        {
            var userId = GetUserId();
            var success = await _notificationService.MarkAsReadAsync(id, userId);
            
            if (success)
            {
                return Ok(new { message = "Notification marquée comme lue" });
            }
            
            return NotFound(new { error = "Notification non trouvée" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du marquage de la notification {Id} comme lue", id);
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpPost("mark-all-read")]
    public async Task<ActionResult> MarkAllAsRead()
    {
        try
        {
            var userId = GetUserId();
            var success = await _notificationService.MarkAllAsReadAsync(userId);
            
            if (success)
            {
                return Ok(new { message = "Toutes les notifications marquées comme lues" });
            }
            
            return BadRequest(new { error = "Impossible de marquer les notifications comme lues" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du marquage de toutes les notifications comme lues");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteNotification(int id)
    {
        try
        {
            var userId = GetUserId();
            var success = await _notificationService.DeleteNotificationAsync(id, userId);
            
            if (success)
            {
                return Ok(new { message = "Notification supprimée" });
            }
            
            return NotFound(new { error = "Notification non trouvée" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de la notification {Id}", id);
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpPost]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<int>> CreateNotification([FromBody] CreateNotificationDto notification)
    {
        try
        {
            var notificationId = await _notificationService.CreateNotificationAsync(notification);
            
            if (notificationId > 0)
            {
                return Ok(new { id = notificationId, message = "Notification créée avec succès" });
            }
            
            return BadRequest(new { error = "Impossible de créer la notification" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de la notification");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpPost("bulk")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<List<int>>> CreateBulkNotification([FromBody] BulkNotificationDto bulkNotification)
    {
        try
        {
            var notificationIds = await _notificationService.CreateBulkNotificationAsync(bulkNotification);
            
            return Ok(new { 
                ids = notificationIds, 
                count = notificationIds.Count,
                message = $"{notificationIds.Count} notifications créées avec succès" 
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création des notifications en lot");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpPost("send-immediate")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> SendImmediateNotification([FromBody] CreateNotificationDto notification)
    {
        try
        {
            var success = await _notificationService.SendImmediateNotificationAsync(notification);
            
            if (success)
            {
                return Ok(new { message = "Notification envoyée immédiatement" });
            }
            
            return BadRequest(new { error = "Impossible d'envoyer la notification" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi immédiat de la notification");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpPost("push/subscribe")]
    public async Task<ActionResult> SubscribeToPush([FromBody] PushSubscriptionDto subscription)
    {
        try
        {
            var userId = GetUserId();
            var success = await _pushService.SubscribeUserAsync(userId, subscription);
            
            if (success)
            {
                return Ok(new { message = "Abonnement push créé avec succès" });
            }
            
            return BadRequest(new { error = "Impossible de créer l'abonnement push" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création d'abonnement push");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpDelete("push/unsubscribe")]
    public async Task<ActionResult> UnsubscribeFromPush([FromQuery] string endpoint)
    {
        try
        {
            var userId = GetUserId();
            var success = await _pushService.UnsubscribeUserAsync(userId, endpoint);
            
            if (success)
            {
                return Ok(new { message = "Désabonnement push effectué" });
            }
            
            return BadRequest(new { error = "Impossible de se désabonner" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du désabonnement push");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpPost("test-push")]
    public async Task<ActionResult> TestPushNotification()
    {
        try
        {
            var userId = GetUserId();
            var success = await _pushService.SendToUserAsync(userId, 
                "Test de notification", 
                "Votre système de notifications push fonctionne correctement !");
            
            if (success)
            {
                return Ok(new { message = "Notification push de test envoyée" });
            }
            
            return BadRequest(new { error = "Impossible d'envoyer la notification push de test" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du test de notification push");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("preferences")]
    public async Task<ActionResult<List<NotificationPreferenceDto>>> GetPreferences()
    {
        try
        {
            var userId = GetUserId();
            var preferences = await _notificationService.GetUserPreferencesAsync(userId);
            return Ok(preferences);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des préférences");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpPut("preferences")]
    public async Task<ActionResult> UpdatePreferences([FromBody] List<NotificationPreferenceDto> preferences)
    {
        try
        {
            var userId = GetUserId();
            var success = await _notificationService.UpdateUserPreferencesAsync(userId, preferences);
            
            if (success)
            {
                return Ok(new { message = "Préférences mises à jour" });
            }
            
            return BadRequest(new { error = "Impossible de mettre à jour les préférences" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour des préférences");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("stats")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<NotificationStatsDto>> GetStats(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] string? userId = null)
    {
        try
        {
            var stats = await _notificationService.GetNotificationStatsAsync(startDate, endDate, userId);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des statistiques");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    // Endpoints pour les intégrations avec d'autres services
    [HttpPost("webhooks/order-created")]
    [AllowAnonymous] // Sécurisé par API key ou signature
    public async Task<ActionResult> HandleOrderCreated([FromBody] OrderCreatedWebhookDto webhook)
    {
        try
        {
            await _notificationService.HandleOrderCreatedAsync(webhook.OrderId, webhook.UserId, webhook.Amount);
            return Ok(new { message = "Notification de commande créée traitée" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement du webhook order-created");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpPost("webhooks/order-updated")]
    [AllowAnonymous]
    public async Task<ActionResult> HandleOrderUpdated([FromBody] OrderUpdatedWebhookDto webhook)
    {
        try
        {
            await _notificationService.HandleOrderUpdatedAsync(webhook.OrderId, webhook.UserId, webhook.Status);
            return Ok(new { message = "Notification de mise à jour de commande traitée" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement du webhook order-updated");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpPost("webhooks/payment-completed")]
    [AllowAnonymous]
    public async Task<ActionResult> HandlePaymentCompleted([FromBody] PaymentCompletedWebhookDto webhook)
    {
        try
        {
            await _notificationService.HandlePaymentCompletedAsync(webhook.OrderId, webhook.UserId, webhook.Amount);
            return Ok(new { message = "Notification de paiement complété traitée" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement du webhook payment-completed");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpPost("webhooks/product-approved")]
    [AllowAnonymous]
    public async Task<ActionResult> HandleProductApproved([FromBody] ProductApprovedWebhookDto webhook)
    {
        try
        {
            await _notificationService.HandleProductApprovedAsync(webhook.ProductId, webhook.SellerId, webhook.ProductName);
            return Ok(new { message = "Notification de produit approuvé traitée" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement du webhook product-approved");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpPost("webhooks/stock-low")]
    [AllowAnonymous]
    public async Task<ActionResult> HandleStockLow([FromBody] StockLowWebhookDto webhook)
    {
        try
        {
            await _notificationService.HandleStockLowAsync(webhook.ProductId, webhook.SellerId, webhook.ProductName, webhook.CurrentStock);
            return Ok(new { message = "Notification de stock faible traitée" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement du webhook stock-low");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpPost("webhooks/new-review")]
    [AllowAnonymous]
    public async Task<ActionResult> HandleNewReview([FromBody] NewReviewWebhookDto webhook)
    {
        try
        {
            await _notificationService.HandleNewReviewAsync(webhook.ProductId, webhook.SellerId, webhook.ProductName, webhook.Rating);
            return Ok(new { message = "Notification de nouvel avis traitée" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement du webhook new-review");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpPost("webhooks/user-registered")]
    [AllowAnonymous]
    public async Task<ActionResult> HandleUserRegistered([FromBody] UserRegisteredWebhookDto webhook)
    {
        try
        {
            await _notificationService.HandleUserRegisteredAsync(webhook.UserId, webhook.UserName, webhook.Email);
            return Ok(new { message = "Notification d'inscription utilisateur traitée" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement du webhook user-registered");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    // Méthodes d'aide privées
    private string GetUserId()
    {
        return User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? throw new UnauthorizedAccessException("Utilisateur non authentifié");
    }

    private string? GetUserRole()
    {
        return User.FindFirst(ClaimTypes.Role)?.Value;
    }
}

// DTOs pour les webhooks
public class OrderCreatedWebhookDto
{
    public int OrderId { get; set; }
    public string UserId { get; set; } = string.Empty;
    public decimal Amount { get; set; }
}

public class OrderUpdatedWebhookDto
{
    public int OrderId { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
}

public class PaymentCompletedWebhookDto
{
    public int OrderId { get; set; }
    public string UserId { get; set; } = string.Empty;
    public decimal Amount { get; set; }
}

public class ProductApprovedWebhookDto
{
    public int ProductId { get; set; }
    public string SellerId { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
}

public class StockLowWebhookDto
{
    public int ProductId { get; set; }
    public string SellerId { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public int CurrentStock { get; set; }
}

public class NewReviewWebhookDto
{
    public int ProductId { get; set; }
    public string SellerId { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public int Rating { get; set; }
}

public class UserRegisteredWebhookDto
{
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
}
