namespace NafaPlace.Web.Services;

public interface IChatEcommerceService
{
    // Conversation Methods
    Task<List<ConversationDto>> GetCustomerConversationsAsync();
    Task<ConversationDto?> GetConversationByIdAsync(int conversationId);
    Task<int> CreateConversationAsync(CreateConversationDto dto);
    
    // Message Methods
    Task<List<MessageDto>> GetMessagesAsync(int conversationId);
    Task<bool> SendMessageAsync(SendMessageDto message);
    Task<bool> MarkMessagesAsReadAsync(int conversationId);
}

// DTOs
public class ConversationDto
{
    public int Id { get; set; }
    public string CustomerId { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public string CustomerEmail { get; set; } = string.Empty;
    public string? SellerId { get; set; }
    public string? SellerName { get; set; }
    public int? ProductId { get; set; }
    public string? ProductName { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? LastMessageAt { get; set; }
    public int UnreadCount { get; set; }
}

public class MessageDto
{
    public int Id { get; set; }
    public int ConversationId { get; set; }
    public string SenderId { get; set; } = string.Empty;
    public string SenderName { get; set; } = string.Empty;
    public string SenderType { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string MessageType { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public bool IsRead { get; set; }
    public DateTime? ReadAt { get; set; }
    public bool IsEdited { get; set; }
    public DateTime? EditedAt { get; set; }
    public string? AttachmentUrl { get; set; }
    public string? AttachmentType { get; set; }
    public long? AttachmentSize { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class SendMessageDto
{
    public int ConversationId { get; set; }
    public string SenderId { get; set; } = string.Empty;
    public string SenderName { get; set; } = string.Empty;
    public string SenderType { get; set; } = "Customer";
    public string Content { get; set; } = string.Empty;
    public string MessageType { get; set; } = "Text";
    public string? AttachmentUrl { get; set; }
    public string? AttachmentType { get; set; }
    public long? AttachmentSize { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class CreateConversationDto
{
    public string CustomerId { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public string CustomerEmail { get; set; } = string.Empty;
    public string? SellerId { get; set; }
    public int? ProductId { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string Type { get; set; } = "General";
    public string Priority { get; set; } = "Normal";
    public string InitialMessage { get; set; } = string.Empty;
}

