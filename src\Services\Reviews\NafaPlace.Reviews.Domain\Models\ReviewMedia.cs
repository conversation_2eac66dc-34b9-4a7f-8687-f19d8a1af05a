using System.ComponentModel.DataAnnotations;

namespace NafaPlace.Reviews.Domain.Models;

public class ReviewMedia
{
    public int Id { get; set; }
    
    [Required]
    public int ReviewId { get; set; }
    
    [Required]
    [StringLength(255)]
    public string FileName { get; set; } = string.Empty;
    
    [Required]
    [StringLength(255)]
    public string OriginalFileName { get; set; } = string.Empty;
    
    [Required]
    [StringLength(500)]
    public string FilePath { get; set; } = string.Empty;
    
    [StringLength(500)]
    public string FileUrl { get; set; } = string.Empty;
    
    [Required]
    [StringLength(100)]
    public string ContentType { get; set; } = string.Empty;
    
    public long FileSize { get; set; }
    
    [Required]
    public MediaType MediaType { get; set; }
    
    public int? Width { get; set; }
    
    public int? Height { get; set; }
    
    public int? Duration { get; set; } // Pour les vidéos/audio en secondes
    
    [StringLength(500)]
    public string? ThumbnailPath { get; set; }
    
    [StringLength(500)]
    public string? ThumbnailUrl { get; set; }
    
    [StringLength(255)]
    public string? AltText { get; set; }
    
    [StringLength(500)]
    public string? Caption { get; set; }
    
    public int DisplayOrder { get; set; }
    
    public bool IsPublic { get; set; } = true;
    
    public bool IsProcessed { get; set; } = false;
    
    [StringLength(50)]
    public string? ProcessingStatus { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
    
    [StringLength(255)]
    public string? UploadedBy { get; set; }

    // Alternative properties for compatibility
    [StringLength(100)]
    public string MimeType 
    { 
        get => ContentType; 
        set => ContentType = value; 
    }
    
    public DateTime UploadedAt 
    { 
        get => CreatedAt; 
        set => CreatedAt = value; 
    }
    
    // Navigation properties
    public virtual Review Review { get; set; } = null!;
}

public enum MediaType
{
    Image = 1,
    Video = 2,
    Audio = 3
}
