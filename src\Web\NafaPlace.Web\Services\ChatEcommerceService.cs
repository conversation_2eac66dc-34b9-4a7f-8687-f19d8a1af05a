using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using Blazored.LocalStorage;

namespace NafaPlace.Web.Services;

public class ChatEcommerceService : IChatEcommerceService
{
    private readonly HttpClient _httpClient;
    private readonly ILocalStorageService _localStorage;

    public ChatEcommerceService(
        HttpClient httpClient,
        ILocalStorageService localStorage)
    {
        _httpClient = httpClient;
        _localStorage = localStorage;
    }

    private async Task SetAuthorizationHeaderAsync()
    {
        try
        {
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                Console.WriteLine("[ChatEcommerceService] Token récupéré et ajouté aux headers");
            }
            else
            {
                Console.WriteLine("[ChatEcommerceService] Aucun token trouvé dans le localStorage");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[ChatEcommerceService] Erreur lors de la récupération du token: {ex.Message}");
        }
    }

    public async Task<List<ConversationDto>> GetCustomerConversationsAsync()
    {
        try
        {
            await SetAuthorizationHeaderAsync();

            var response = await _httpClient.GetAsync("conversations/customer");

            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var conversations = JsonSerializer.Deserialize<List<ConversationDto>>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                return conversations ?? new List<ConversationDto>();
            }

            Console.WriteLine($"Échec de la récupération des conversations: {response.StatusCode}");
            return new List<ConversationDto>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des conversations: {ex.Message}");
            return new List<ConversationDto>();
        }
    }

    public async Task<ConversationDto?> GetConversationByIdAsync(int conversationId)
    {
        try
        {
            await SetAuthorizationHeaderAsync();

            var response = await _httpClient.GetAsync($"conversations/{conversationId}");

            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var conversation = JsonSerializer.Deserialize<ConversationDto>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                return conversation;
            }

            Console.WriteLine($"Échec de la récupération de la conversation {conversationId}: {response.StatusCode}");
            return null;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération de la conversation {conversationId}: {ex.Message}");
            return null;
        }
    }

    public async Task<int> CreateConversationAsync(CreateConversationDto dto)
    {
        try
        {
            await SetAuthorizationHeaderAsync();

            var json = JsonSerializer.Serialize(dto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("conversations", content);

            if (response.IsSuccessStatusCode)
            {
                var responseJson = await response.Content.ReadAsStringAsync();
                var conversationId = JsonSerializer.Deserialize<int>(responseJson);
                Console.WriteLine($"Conversation créée avec succès: {conversationId}");
                return conversationId;
            }

            Console.WriteLine($"Échec de la création de la conversation: {response.StatusCode}");
            return 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la création de la conversation: {ex.Message}");
            return 0;
        }
    }

    public async Task<List<MessageDto>> GetMessagesAsync(int conversationId)
    {
        try
        {
            await SetAuthorizationHeaderAsync();

            var response = await _httpClient.GetAsync($"messages/conversation/{conversationId}");

            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var messages = JsonSerializer.Deserialize<List<MessageDto>>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                return messages ?? new List<MessageDto>();
            }

            Console.WriteLine($"Échec de la récupération des messages de la conversation {conversationId}: {response.StatusCode}");
            return new List<MessageDto>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des messages de la conversation {conversationId}: {ex.Message}");
            return new List<MessageDto>();
        }
    }

    public async Task<bool> SendMessageAsync(SendMessageDto message)
    {
        try
        {
            await SetAuthorizationHeaderAsync();

            var json = JsonSerializer.Serialize(message);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("messages", content);

            if (response.IsSuccessStatusCode)
            {
                Console.WriteLine($"Message envoyé avec succès à la conversation {message.ConversationId}");
                return true;
            }

            Console.WriteLine($"Échec de l'envoi du message: {response.StatusCode}");
            return false;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'envoi du message: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> MarkMessagesAsReadAsync(int conversationId)
    {
        try
        {
            await SetAuthorizationHeaderAsync();

            // Récupérer l'ID utilisateur du token
            var userId = await GetUserIdFromTokenAsync();
            if (string.IsNullOrEmpty(userId))
            {
                Console.WriteLine("Impossible de marquer les messages comme lus: UserId non trouvé");
                return false;
            }

            var requestBody = new { UserId = userId };
            var json = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Utiliser PUT au lieu de POST pour correspondre à l'API
            var response = await _httpClient.PutAsync($"conversations/{conversationId}/read", content);

            if (response.IsSuccessStatusCode)
            {
                Console.WriteLine($"Messages marqués comme lus pour la conversation {conversationId}");
                return true;
            }

            Console.WriteLine($"Échec du marquage des messages comme lus: {response.StatusCode}");
            return false;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du marquage des messages comme lus: {ex.Message}");
            return false;
        }
    }

    private async Task<string> GetUserIdFromTokenAsync()
    {
        try
        {
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
                return string.Empty;

            // Décoder le JWT pour extraire l'ID utilisateur
            var parts = token.Split('.');
            if (parts.Length != 3)
                return string.Empty;

            var payload = parts[1];
            var paddedPayload = payload.PadRight(payload.Length + (4 - payload.Length % 4) % 4, '=');
            var payloadBytes = Convert.FromBase64String(paddedPayload);
            var payloadJson = Encoding.UTF8.GetString(payloadBytes);
            
            var payloadData = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(payloadJson);
            
            if (payloadData != null && payloadData.ContainsKey("sub"))
            {
                return payloadData["sub"].GetString() ?? string.Empty;
            }

            return string.Empty;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'extraction de l'ID utilisateur du token: {ex.Message}");
            return string.Empty;
        }
    }
}

