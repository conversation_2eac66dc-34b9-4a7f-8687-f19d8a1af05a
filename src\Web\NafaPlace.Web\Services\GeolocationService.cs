using Microsoft.JSInterop;
using System.Text.Json;

namespace NafaPlace.Web.Services;

/// <summary>
/// Service de géolocalisation pour l'interopérabilité avec JavaScript
/// Fournit des méthodes pour accéder à la géolocalisation HTML5 depuis Blazor
/// </summary>
public class GeolocationService
{
    private readonly IJSRuntime _jsRuntime;
    private readonly ILogger<GeolocationService> _logger;
    private DotNetObjectReference<GeolocationService>? _dotNetHelper;
    private TaskCompletionSource<GeolocationPosition>? _positionTaskSource;
    private Func<double, double, double?, Task>? _positionUpdateCallback;
    private Func<string, Task>? _errorCallback;

    public GeolocationService(IJSRuntime jsRuntime, ILogger<GeolocationService> logger)
    {
        _jsRuntime = jsRuntime;
        _logger = logger;
    }

    /// <summary>
    /// Initialise le service de géolocalisation
    /// </summary>
    public async Task InitializeAsync()
    {
        try
        {
            _dotNetHelper = DotNetObjectReference.Create(this);
            await _jsRuntime.InvokeVoidAsync("window.geolocationHelper.initialize", _dotNetHelper);
            _logger.LogInformation("Service de géolocalisation initialisé");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'initialisation du service de géolocalisation");
            throw;
        }
    }

    /// <summary>
    /// Obtient la position actuelle de l'utilisateur
    /// </summary>
    public async Task<GeolocationPosition> GetCurrentPositionAsync(int timeoutMs = 10000)
    {
        try
        {
            _positionTaskSource = new TaskCompletionSource<GeolocationPosition>();

            await _jsRuntime.InvokeVoidAsync("window.geolocationHelper.getCurrentPosition", timeoutMs);

            // Attendre le résultat avec timeout
            var timeoutTask = Task.Delay(timeoutMs + 1000);
            var completedTask = await Task.WhenAny(_positionTaskSource.Task, timeoutTask);

            if (completedTask == timeoutTask)
            {
                throw new TimeoutException("Timeout lors de l'obtention de la position");
            }

            return await _positionTaskSource.Task;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention de la position actuelle");
            throw;
        }
    }

    /// <summary>
    /// Démarre la surveillance continue de la position
    /// </summary>
    public async Task StartWatchingPosition(
        Func<double, double, double?, Task> onPositionUpdate,
        Func<string, Task> onError,
        int timeoutMs = 15000)
    {
        try
        {
            _positionUpdateCallback = onPositionUpdate;
            _errorCallback = onError;

            await _jsRuntime.InvokeVoidAsync("window.geolocationHelper.startWatching", timeoutMs);
            _logger.LogInformation("Surveillance de position démarrée");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du démarrage de la surveillance de position");
            throw;
        }
    }

    /// <summary>
    /// Arrête la surveillance de la position
    /// </summary>
    public async Task StopWatchingPosition()
    {
        try
        {
            await _jsRuntime.InvokeVoidAsync("window.geolocationHelper.stopWatching");
            _positionUpdateCallback = null;
            _errorCallback = null;
            _logger.LogInformation("Surveillance de position arrêtée");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'arrêt de la surveillance de position");
        }
    }

    /// <summary>
    /// Vérifie si la géolocalisation est supportée
    /// </summary>
    public async Task<bool> IsGeolocationSupported()
    {
        try
        {
            return await _jsRuntime.InvokeAsync<bool>("window.geolocationHelper.isSupported");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification du support de géolocalisation");
            return false;
        }
    }

    /// <summary>
    /// Calcule la distance entre deux points GPS
    /// </summary>
    public double CalculateDistance(double lat1, double lon1, double lat2, double lon2)
    {
        const double R = 6371; // Rayon de la Terre en km
        var dLat = ToRadians(lat2 - lat1);
        var dLon = ToRadians(lon2 - lon1);

        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(ToRadians(lat1)) * Math.Cos(ToRadians(lat2)) *
                Math.Sin(dLon / 2) * Math.Sin(dLon / 2);

        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));

        return R * c;
    }

    /// <summary>
    /// Calcule le cap (bearing) entre deux points GPS
    /// </summary>
    public double CalculateBearing(double lat1, double lon1, double lat2, double lon2)
    {
        var dLon = ToRadians(lon2 - lon1);
        var y = Math.Sin(dLon) * Math.Cos(ToRadians(lat2));
        var x = Math.Cos(ToRadians(lat1)) * Math.Sin(ToRadians(lat2)) -
                Math.Sin(ToRadians(lat1)) * Math.Cos(ToRadians(lat2)) * Math.Cos(dLon);

        var bearing = Math.Atan2(y, x);
        return (ToDegrees(bearing) + 360) % 360;
    }

    /// <summary>
    /// Vérifie si un point est dans un rayon donné d'un autre point
    /// </summary>
    public bool IsWithinRadius(double lat1, double lon1, double lat2, double lon2, double radiusKm)
    {
        var distance = CalculateDistance(lat1, lon1, lat2, lon2);
        return distance <= radiusKm;
    }

    // Callbacks JavaScript invoqués depuis le navigateur
    [JSInvokable]
    public async Task OnPositionReceived(double latitude, double longitude, double accuracy, double? speed, double? heading, long timestamp)
    {
        try
        {
            var position = new GeolocationPosition
            {
                Latitude = latitude,
                Longitude = longitude,
                Accuracy = accuracy,
                Speed = speed,
                Heading = heading,
                Timestamp = DateTimeOffset.FromUnixTimeMilliseconds(timestamp).DateTime
            };

            // Résoudre la tâche en attente pour getCurrentPosition
            if (_positionTaskSource != null && !_positionTaskSource.Task.IsCompleted)
            {
                _positionTaskSource.SetResult(position);
            }

            // Appeler le callback pour la surveillance continue
            if (_positionUpdateCallback != null)
            {
                await _positionUpdateCallback(latitude, longitude, accuracy);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement de la position reçue");
        }
    }

    [JSInvokable]
    public async Task OnPositionError(string error)
    {
        try
        {
            _logger.LogWarning("Erreur de géolocalisation: {Error}", error);

            // Résoudre la tâche en attente avec une exception
            if (_positionTaskSource != null && !_positionTaskSource.Task.IsCompleted)
            {
                _positionTaskSource.SetException(new GeolocationException(error));
            }

            // Appeler le callback d'erreur
            if (_errorCallback != null)
            {
                await _errorCallback(error);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du traitement de l'erreur de géolocalisation");
        }
    }

    // Méthodes utilitaires
    private static double ToRadians(double degrees) => degrees * Math.PI / 180.0;
    private static double ToDegrees(double radians) => radians * 180.0 / Math.PI;

    public void Dispose()
    {
        _dotNetHelper?.Dispose();
    }
}

/// <summary>
/// Représente une position géographique avec métadonnées
/// </summary>
public class GeolocationPosition
{
    /// <summary>
    /// Latitude en degrés décimaux
    /// </summary>
    public double Latitude { get; set; }

    /// <summary>
    /// Longitude en degrés décimaux
    /// </summary>
    public double Longitude { get; set; }

    /// <summary>
    /// Précision en mètres
    /// </summary>
    public double Accuracy { get; set; }

    /// <summary>
    /// Vitesse en m/s (optionnel)
    /// </summary>
    public double? Speed { get; set; }

    /// <summary>
    /// Vitesse en km/h
    /// </summary>
    public double? SpeedKmh => Speed.HasValue ? Speed.Value * 3.6 : null;

    /// <summary>
    /// Direction en degrés (0-360, optionnel)
    /// </summary>
    public double? Heading { get; set; }

    /// <summary>
    /// Horodatage de la mesure
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Indique si la position est considérée comme précise
    /// </summary>
    public bool IsAccurate => Accuracy <= 50; // Précision <= 50 mètres

    /// <summary>
    /// Formatage pour l'affichage
    /// </summary>
    public override string ToString()
    {
        return $"Lat: {Latitude:F6}, Lng: {Longitude:F6}, Précision: {Accuracy:F0}m";
    }

    /// <summary>
    /// Convertit en format utilisable par les cartes
    /// </summary>
    public object ToMapFormat()
    {
        return new { lat = Latitude, lng = Longitude };
    }
}

/// <summary>
/// Options pour la géolocalisation
/// </summary>
public class GeolocationOptions
{
    /// <summary>
    /// Demander la haute précision (utilise GPS si disponible)
    /// </summary>
    public bool EnableHighAccuracy { get; set; } = true;

    /// <summary>
    /// Timeout en millisecondes
    /// </summary>
    public int Timeout { get; set; } = 10000;

    /// <summary>
    /// Âge maximum d'une position en cache (millisecondes)
    /// </summary>
    public int MaximumAge { get; set; } = 60000;
}

/// <summary>
/// Exception spécifique à la géolocalisation
/// </summary>
public class GeolocationException : Exception
{
    public GeolocationException(string message) : base(message) { }
    public GeolocationException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// Service helper pour les calculs géographiques
/// </summary>
public static class GeolocationHelper
{
    /// <summary>
    /// Points d'intérêt à Conakry, Guinée
    /// </summary>
    public static readonly Dictionary<string, (double Lat, double Lng)> ConakryLandmarks = new()
    {
        ["Palais du Peuple"] = (9.5092, -13.7122),
        ["Université de Conakry"] = (9.6355, -13.5784),
        ["Aéroport de Conakry"] = (9.5769, -13.6102),
        ["Marché Madina"] = (9.6823, -13.6311),
        ["Stade du 28 Septembre"] = (9.5395, -13.6773),
        ["Port de Conakry"] = (9.5133, -13.7197),
        ["Kipé"] = (9.6962, -13.6266),
        ["Ratoma"] = (9.6962, -13.6266),
        ["Matoto"] = (9.6540, -13.6203),
        ["Dixinn"] = (9.6823, -13.6311)
    };

    /// <summary>
    /// Trouve le point d'intérêt le plus proche
    /// </summary>
    public static string FindNearestLandmark(double latitude, double longitude)
    {
        var service = new GeolocationService(null!, null!);
        string nearest = "";
        double minDistance = double.MaxValue;

        foreach (var landmark in ConakryLandmarks)
        {
            var distance = service.CalculateDistance(latitude, longitude, landmark.Value.Lat, landmark.Value.Lng);
            if (distance < minDistance)
            {
                minDistance = distance;
                nearest = landmark.Key;
            }
        }

        return $"{nearest} ({minDistance:F1} km)";
    }

    /// <summary>
    /// Vérifie si une position est dans Conakry
    /// </summary>
    public static bool IsInConakry(double latitude, double longitude)
    {
        // Limites approximatives de Conakry
        const double minLat = 9.4;
        const double maxLat = 9.8;
        const double minLng = -13.8;
        const double maxLng = -13.4;

        return latitude >= minLat && latitude <= maxLat &&
               longitude >= minLng && longitude <= maxLng;
    }

    /// <summary>
    /// Obtient la commune de Conakry basée sur la position
    /// </summary>
    public static string GetConakryCommune(double latitude, double longitude)
    {
        if (!IsInConakry(latitude, longitude))
            return "Hors Conakry";

        // Zones approximatives des communes
        if (latitude < 9.52 && longitude > -13.72) return "Kaloum";
        if (latitude > 9.68 && longitude > -13.63) return "Ratoma";
        if (latitude > 9.65 && longitude < -13.62) return "Matoto";
        if (latitude > 9.67 && longitude > -13.64) return "Dixinn";

        return "Matam"; // Par défaut
    }
}

/// <summary>
/// Extension pour l'injection de dépendance
/// </summary>
public static class GeolocationServiceExtensions
{
    public static IServiceCollection AddGeolocationService(this IServiceCollection services)
    {
        services.AddScoped<GeolocationService>();
        return services;
    }
}