using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NafaPlace.Notification.Application.DTOs;
using NafaPlace.Notification.Application.Services;
using System.Text;
using System.Text.Json;

namespace NafaPlace.Notification.Infrastructure.Services;

public class PushNotificationService : IPushNotificationService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<PushNotificationService> _logger;
    private readonly HttpClient _httpClient;
    private readonly string _vapidPublicKey;
    private readonly string _vapidPrivateKey;
    private readonly string _fcmServerKey;

    public PushNotificationService(
        IConfiguration configuration,
        ILogger<PushNotificationService> logger,
        HttpClient httpClient)
    {
        _configuration = configuration;
        _logger = logger;
        _httpClient = httpClient;
        _vapidPublicKey = _configuration["Push:Vapid:PublicKey"] ?? "";
        _vapidPrivateKey = _configuration["Push:Vapid:PrivateKey"] ?? "";
        _fcmServerKey = _configuration["Push:FCM:ServerKey"] ?? "";
    }

    public async Task<bool> SendToUserAsync(string userId, string title, string body, Dictionary<string, object>? data = null)
    {
        try
        {
            _logger.LogInformation("Envoi de notification push à {UserId}: {Title}", userId, title);

            var subscriptions = await GetUserSubscriptionsAsync(userId);
            if (!subscriptions.Any())
            {
                _logger.LogWarning("Aucun abonnement push trouvé pour {UserId}", userId);
                return false;
            }

            var successCount = 0;
            foreach (var subscription in subscriptions)
            {
                if (await SendWebPushAsync(subscription, title, body, data))
                {
                    successCount++;
                }
            }

            var success = successCount > 0;
            _logger.LogInformation("Notification push envoyée à {UserId}: {Success}/{Total} abonnements", 
                userId, successCount, subscriptions.Count);

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de notification push à {UserId}", userId);
            return false;
        }
    }

    public async Task<bool> SendToDeviceAsync(string deviceToken, string title, string body, Dictionary<string, object>? data = null)
    {
        try
        {
            return await SendFcmAsync(deviceToken, title, body, data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de notification push au device {DeviceToken}", deviceToken);
            return false;
        }
    }

    public async Task<int> SendToMultipleUsersAsync(List<string> userIds, string title, string body, Dictionary<string, object>? data = null)
    {
        var successCount = 0;

        foreach (var userId in userIds)
        {
            try
            {
                if (await SendToUserAsync(userId, title, body, data))
                {
                    successCount++;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de l'envoi de notification push à {UserId}", userId);
            }
        }

        _logger.LogInformation("Notification push en lot terminée: {Success}/{Total} utilisateurs", 
            successCount, userIds.Count);

        return successCount;
    }

    public async Task<int> SendToMultipleDevicesAsync(List<string> deviceTokens, string title, string body, Dictionary<string, object>? data = null)
    {
        return await SendFcmToMultipleAsync(deviceTokens, title, body, data);
    }

    public async Task<bool> SendToTopicAsync(string topic, string title, string body, Dictionary<string, object>? data = null)
    {
        return await SendFcmToTopicAsync(topic, title, body, data);
    }

    public async Task<bool> SubscribeUserAsync(string userId, PushSubscriptionDto subscription)
    {
        try
        {
            subscription.UserId = userId;
            subscription.CreatedAt = DateTime.UtcNow;
            subscription.IsActive = true;

            // Valider l'abonnement
            if (!await ValidateSubscriptionAsync(subscription))
            {
                _logger.LogWarning("Abonnement push invalide pour {UserId}", userId);
                return false;
            }

            // Simuler la sauvegarde en base de données
            _logger.LogInformation("Abonnement push créé pour {UserId}", userId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création d'abonnement push pour {UserId}", userId);
            return false;
        }
    }

    public async Task<bool> UnsubscribeUserAsync(string userId, string endpoint)
    {
        try
        {
            _logger.LogInformation("Désabonnement push pour {UserId} - Endpoint: {Endpoint}", userId, endpoint);
            // Simuler la suppression en base de données
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du désabonnement push pour {UserId}", userId);
            return false;
        }
    }

    public async Task<bool> UpdateSubscriptionAsync(string userId, PushSubscriptionDto subscription)
    {
        try
        {
            subscription.LastUsed = DateTime.UtcNow;
            _logger.LogInformation("Abonnement push mis à jour pour {UserId}", userId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour d'abonnement push pour {UserId}", userId);
            return false;
        }
    }

    public async Task<List<PushSubscriptionDto>> GetUserSubscriptionsAsync(string userId)
    {
        try
        {
            // Simuler la récupération depuis la base de données
            await Task.Delay(10);
            
            return new List<PushSubscriptionDto>
            {
                new()
                {
                    Id = 1,
                    UserId = userId,
                    Endpoint = $"https://fcm.googleapis.com/fcm/send/{userId}",
                    P256dh = "BNcRdreALRFXTkOOUHK1EtK2wtaz5Ry4YfYCA_0QTpQtUbVlUls0VJXg7A8YlKtxiHiWhHXmzMyfq8QQGpAUBqk",
                    Auth = "tBHItJI5svbpez7KI4CCXg",
                    UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow.AddDays(-7),
                    LastUsed = DateTime.UtcNow.AddHours(-2)
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des abonnements pour {UserId}", userId);
            return new List<PushSubscriptionDto>();
        }
    }

    public async Task<bool> ValidateSubscriptionAsync(PushSubscriptionDto subscription)
    {
        try
        {
            // Valider les champs requis
            if (string.IsNullOrEmpty(subscription.Endpoint) ||
                string.IsNullOrEmpty(subscription.P256dh) ||
                string.IsNullOrEmpty(subscription.Auth))
            {
                return false;
            }

            // Valider le format de l'endpoint
            if (!Uri.TryCreate(subscription.Endpoint, UriKind.Absolute, out _))
            {
                return false;
            }

            await Task.Delay(10);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la validation d'abonnement push");
            return false;
        }
    }

    public async Task<bool> SendWebPushAsync(PushSubscriptionDto subscription, string title, string body, Dictionary<string, object>? data = null)
    {
        try
        {
            var payload = new
            {
                title,
                body,
                icon = "/images/icon-192x192.png",
                badge = "/images/badge-72x72.png",
                data = data ?? new Dictionary<string, object>(),
                actions = new[]
                {
                    new { action = "view", title = "Voir", icon = "/images/view-icon.png" },
                    new { action = "dismiss", title = "Ignorer", icon = "/images/dismiss-icon.png" }
                },
                requireInteraction = false,
                silent = false,
                timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            };

            var payloadJson = JsonSerializer.Serialize(payload);
            
            // Dans une vraie implémentation, on utiliserait WebPush library
            // pour chiffrer et envoyer la notification
            _logger.LogInformation("Web Push envoyé à {Endpoint}: {Title}", subscription.Endpoint, title);
            
            await Task.Delay(100); // Simuler l'envoi
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi Web Push");
            return false;
        }
    }

    public async Task<int> SendWebPushToMultipleAsync(List<PushSubscriptionDto> subscriptions, string title, string body, Dictionary<string, object>? data = null)
    {
        var successCount = 0;

        foreach (var subscription in subscriptions)
        {
            try
            {
                if (await SendWebPushAsync(subscription, title, body, data))
                {
                    successCount++;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de l'envoi Web Push à {Endpoint}", subscription.Endpoint);
            }
        }

        return successCount;
    }

    public async Task<bool> TestWebPushAsync(PushSubscriptionDto subscription)
    {
        return await SendWebPushAsync(subscription, "Test de notification", "Votre abonnement push fonctionne correctement !");
    }

    public async Task<bool> SendFcmAsync(string deviceToken, string title, string body, Dictionary<string, object>? data = null)
    {
        try
        {
            var message = new
            {
                to = deviceToken,
                notification = new
                {
                    title,
                    body,
                    icon = "/images/icon-192x192.png",
                    click_action = "FCM_PLUGIN_ACTIVITY"
                },
                data = data ?? new Dictionary<string, object>()
            };

            var json = JsonSerializer.Serialize(message);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"key={_fcmServerKey}");

            var response = await _httpClient.PostAsync("https://fcm.googleapis.com/fcm/send", content);
            
            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("FCM envoyé avec succès à {DeviceToken}: {Title}", deviceToken, title);
                return true;
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("Erreur FCM: {StatusCode} - {Content}", response.StatusCode, errorContent);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi FCM à {DeviceToken}", deviceToken);
            return false;
        }
    }

    public async Task<int> SendFcmToMultipleAsync(List<string> deviceTokens, string title, string body, Dictionary<string, object>? data = null)
    {
        try
        {
            var message = new
            {
                registration_ids = deviceTokens,
                notification = new
                {
                    title,
                    body,
                    icon = "/images/icon-192x192.png"
                },
                data = data ?? new Dictionary<string, object>()
            };

            var json = JsonSerializer.Serialize(message);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"key={_fcmServerKey}");

            var response = await _httpClient.PostAsync("https://fcm.googleapis.com/fcm/send", content);
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var responseData = JsonSerializer.Deserialize<JsonElement>(responseContent);
                
                var successCount = responseData.GetProperty("success").GetInt32();
                _logger.LogInformation("FCM en lot envoyé: {Success}/{Total} devices", successCount, deviceTokens.Count);
                
                return successCount;
            }
            else
            {
                _logger.LogError("Erreur FCM en lot: {StatusCode}", response.StatusCode);
                return 0;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi FCM en lot");
            return 0;
        }
    }

    public async Task<bool> SendFcmToTopicAsync(string topic, string title, string body, Dictionary<string, object>? data = null)
    {
        try
        {
            var message = new
            {
                to = $"/topics/{topic}",
                notification = new
                {
                    title,
                    body,
                    icon = "/images/icon-192x192.png"
                },
                data = data ?? new Dictionary<string, object>()
            };

            var json = JsonSerializer.Serialize(message);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"key={_fcmServerKey}");

            var response = await _httpClient.PostAsync("https://fcm.googleapis.com/fcm/send", content);
            
            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("FCM topic envoyé avec succès au topic {Topic}: {Title}", topic, title);
                return true;
            }
            else
            {
                _logger.LogError("Erreur FCM topic: {StatusCode}", response.StatusCode);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi FCM au topic {Topic}", topic);
            return false;
        }
    }

    // Méthodes spécialisées pour différents types de notifications
    public async Task SendOrderPushAsync(string userId, int orderId, string status, decimal? amount = null)
    {
        var title = "Mise à jour de commande";
        var body = $"Votre commande #{orderId} est maintenant {status}";
        
        if (amount.HasValue)
        {
            body += $" - Montant: {amount:N0} GNF";
        }

        var data = new Dictionary<string, object>
        {
            ["type"] = "order_update",
            ["orderId"] = orderId,
            ["status"] = status,
            ["actionUrl"] = $"/orders/{orderId}"
        };

        if (amount.HasValue)
        {
            data["amount"] = amount.Value;
        }

        await SendToUserAsync(userId, title, body, data);
    }

    public async Task SendPaymentPushAsync(string userId, int orderId, string paymentStatus, decimal amount)
    {
        var title = paymentStatus == "completed" ? "Paiement confirmé" : "Problème de paiement";
        var body = paymentStatus == "completed" 
            ? $"Votre paiement de {amount:N0} GNF a été confirmé"
            : $"Problème avec le paiement de {amount:N0} GNF";

        var data = new Dictionary<string, object>
        {
            ["type"] = "payment_update",
            ["orderId"] = orderId,
            ["paymentStatus"] = paymentStatus,
            ["amount"] = amount,
            ["actionUrl"] = $"/orders/{orderId}"
        };

        await SendToUserAsync(userId, title, body, data);
    }

    public async Task SendPromotionPushAsync(List<string> userIds, string title, string message, string? actionUrl = null)
    {
        var data = new Dictionary<string, object>
        {
            ["type"] = "promotion",
            ["actionUrl"] = actionUrl ?? "/promotions"
        };

        await SendToMultipleUsersAsync(userIds, title, message, data);
    }

    public async Task SendStockAlertPushAsync(string sellerId, string productName, int currentStock)
    {
        var title = currentStock == 0 ? "Rupture de stock" : "Stock faible";
        var body = currentStock == 0 
            ? $"{productName} est en rupture de stock"
            : $"Stock faible pour {productName}: {currentStock} unités restantes";

        var data = new Dictionary<string, object>
        {
            ["type"] = "stock_alert",
            ["productName"] = productName,
            ["currentStock"] = currentStock,
            ["actionUrl"] = "/seller/inventory"
        };

        await SendToUserAsync(sellerId, title, body, data);
    }

    public async Task SendReviewPushAsync(string sellerId, string productName, int rating, string? reviewText = null)
    {
        var title = "Nouvel avis client";
        var body = $"Nouvel avis {rating}⭐ pour {productName}";
        
        if (!string.IsNullOrEmpty(reviewText))
        {
            body += $": {reviewText.Substring(0, Math.Min(50, reviewText.Length))}...";
        }

        var data = new Dictionary<string, object>
        {
            ["type"] = "new_review",
            ["productName"] = productName,
            ["rating"] = rating,
            ["actionUrl"] = "/seller/reviews"
        };

        await SendToUserAsync(sellerId, title, body, data);
    }

    public async Task SendWelcomePushAsync(string userId, string userName)
    {
        var title = $"Bienvenue {userName} !";
        var body = "Merci de vous être inscrit sur NafaPlace. Découvrez nos produits !";

        var data = new Dictionary<string, object>
        {
            ["type"] = "welcome",
            ["actionUrl"] = "/catalog"
        };

        await SendToUserAsync(userId, title, body, data);
    }

    public async Task SendSystemAlertPushAsync(string message, NotificationPriority priority = NotificationPriority.Normal)
    {
        var title = priority == NotificationPriority.Critical ? "Alerte système critique" : "Information système";

        var data = new Dictionary<string, object>
        {
            ["type"] = "system_alert",
            ["priority"] = priority.ToString()
        };

        // Envoyer à tous les utilisateurs connectés
        // Dans une vraie implémentation, on récupérerait tous les abonnements actifs
        _logger.LogInformation("Alerte système envoyée: {Message}", message);
    }

    // Méthodes non implémentées - à compléter selon les besoins
    public Task<bool> SubscribeToTopicAsync(string userId, string topic) => Task.FromResult(true);
    public Task<bool> UnsubscribeFromTopicAsync(string userId, string topic) => Task.FromResult(true);
    public Task<List<string>> GetUserTopicsAsync(string userId) => Task.FromResult(new List<string>());
    public Task<int> GetTopicSubscriberCountAsync(string topic) => Task.FromResult(0);
    public Task<bool> SendTemplatedPushAsync(string userId, string templateId, Dictionary<string, object> templateData) => throw new NotImplementedException();
    public Task<bool> SendTemplatedPushToMultipleAsync(List<string> userIds, string templateId, Dictionary<string, object> templateData) => throw new NotImplementedException();
    public Task<Dictionary<string, object>> GetPushStatsAsync(DateTime? startDate = null, DateTime? endDate = null) =>
        Task.FromResult(new Dictionary<string, object> { ["total_sent"] = 0, ["success_rate"] = 100.0 });

    public Task<List<PushDeliveryDto>> GetDeliveryHistoryAsync(string userId, int limit = 50) =>
        Task.FromResult(new List<PushDeliveryDto>());

    public Task<double> GetDeliveryRateAsync(DateTime? startDate = null, DateTime? endDate = null) =>
        Task.FromResult(100.0);

    public Task<Dictionary<string, int>> GetPushStatsByPlatformAsync() =>
        Task.FromResult(new Dictionary<string, int> { ["android"] = 0, ["ios"] = 0, ["web"] = 0 });

    public Task<bool> RetryFailedPushAsync(int pushId) => Task.FromResult(false);
    public Task<int> RetryFailedPushesAsync(DateTime? olderThan = null) => Task.FromResult(0);
    public Task<int> CleanupExpiredSubscriptionsAsync(int daysInactive = 30) => Task.FromResult(0);
    public Task<int> CleanupFailedPushesAsync(int daysToKeep = 7) => Task.FromResult(0);
    public Task<bool> TestPushServiceAsync() => Task.FromResult(true);
    public Task<Dictionary<string, bool>> GetServiceHealthAsync() => Task.FromResult(new Dictionary<string, bool> { ["fcm"] = true, ["webpush"] = true });
    public Task<bool> ValidateConfigurationAsync() => Task.FromResult(true);
    public Task<Dictionary<string, object>> GetConfigurationAsync() => Task.FromResult(new Dictionary<string, object>());
    public Task<bool> SchedulePushAsync(string userId, string title, string body, DateTime scheduledAt, Dictionary<string, object>? data = null) => Task.FromResult(false);
    public Task<bool> ScheduleBulkPushAsync(List<string> userIds, string title, string body, DateTime scheduledAt, Dictionary<string, object>? data = null) => Task.FromResult(false);
    public Task<bool> CancelScheduledPushAsync(int scheduledPushId) => Task.FromResult(false);
    public Task ProcessScheduledPushesAsync() => Task.CompletedTask;
    public Task<int> SendToSegmentAsync(string segmentName, string title, string body, Dictionary<string, object>? data = null) => Task.FromResult(0);
    public Task<int> SendToUsersByLocationAsync(string country, string? city, string title, string body, Dictionary<string, object>? data = null) => throw new NotImplementedException();
    public Task<int> SendToUsersByInterestsAsync(List<string> interests, string title, string body, Dictionary<string, object>? data = null) => throw new NotImplementedException();
    public Task<int> SendToInactiveUsersAsync(int daysSinceLastActivity, string title, string body, Dictionary<string, object>? data = null) => throw new NotImplementedException();
}
