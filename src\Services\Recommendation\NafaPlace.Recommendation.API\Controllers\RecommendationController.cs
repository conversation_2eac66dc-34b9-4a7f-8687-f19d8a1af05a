using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Recommendation.Application.DTOs;
using NafaPlace.Recommendation.Application.Services;

namespace NafaPlace.Recommendation.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class RecommendationController : ControllerBase
{
    private readonly IRecommendationService _recommendationService;
    private readonly ILogger<RecommendationController> _logger;

    public RecommendationController(
        IRecommendationService recommendationService,
        ILogger<RecommendationController> logger)
    {
        _recommendationService = recommendationService;
        _logger = logger;
    }

    /// <summary>
    /// Obtenir des recommandations personnalisées pour un utilisateur
    /// </summary>
    [HttpPost("personalized")]
    public async Task<ActionResult<RecommendationResponseDto>> GetPersonalizedRecommendations(
        [FromBody] RecommendationRequestDto request)
    {
        try
        {
            var result = await _recommendationService.GetPersonalizedRecommendationsAsync(request);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la génération des recommandations personnalisées");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Obtenir des produits similaires à un produit donné
    /// </summary>
    [HttpGet("similar/{productId}")]
    public async Task<ActionResult<RecommendationResponseDto>> GetSimilarProducts(
        int productId,
        [FromQuery] string? userId = null,
        [FromQuery] int limit = 10)
    {
        try
        {
            var result = await _recommendationService.GetSimilarProductsAsync(productId, userId, limit);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des produits similaires pour {ProductId}", productId);
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Obtenir les produits tendance
    /// </summary>
    [HttpGet("trending")]
    public async Task<ActionResult<RecommendationResponseDto>> GetTrendingProducts(
        [FromQuery] string? userId = null,
        [FromQuery] int limit = 10)
    {
        try
        {
            var result = await _recommendationService.GetTrendingProductsAsync(userId, limit);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des produits tendance");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Obtenir les meilleures ventes
    /// </summary>
    [HttpGet("bestsellers")]
    public async Task<ActionResult<RecommendationResponseDto>> GetBestSellers(
        [FromQuery] int? categoryId = null,
        [FromQuery] int limit = 10)
    {
        try
        {
            var result = await _recommendationService.GetBestSellersAsync(categoryId, limit);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des meilleures ventes");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Obtenir les nouveaux arrivages
    /// </summary>
    [HttpGet("new-arrivals")]
    public async Task<ActionResult<RecommendationResponseDto>> GetNewArrivals(
        [FromQuery] int? categoryId = null,
        [FromQuery] int limit = 10)
    {
        try
        {
            var result = await _recommendationService.GetNewArrivalsAsync(categoryId, limit);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des nouveaux arrivages");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Obtenir les produits récemment vus par un utilisateur
    /// </summary>
    [HttpGet("recently-viewed/{userId}")]
    public async Task<ActionResult<RecommendationResponseDto>> GetRecentlyViewed(
        string userId,
        [FromQuery] int limit = 10)
    {
        try
        {
            var result = await _recommendationService.GetRecentlyViewedAsync(userId, limit);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des produits récemment vus pour {UserId}", userId);
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Obtenir des recommandations up-sell pour un produit
    /// </summary>
    [HttpGet("upsell/{productId}")]
    public async Task<ActionResult<RecommendationResponseDto>> GetUpSellRecommendations(
        int productId,
        [FromQuery] string? userId = null,
        [FromQuery] int limit = 3)
    {
        try
        {
            var result = await _recommendationService.GetUpSellRecommendationsAsync(productId, userId, limit);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la génération des recommandations up-sell pour {ProductId}", productId);
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Obtenir des produits complémentaires
    /// </summary>
    [HttpGet("complementary/{productId}")]
    public async Task<ActionResult<RecommendationResponseDto>> GetComplementaryProducts(
        int productId,
        [FromQuery] string? userId = null,
        [FromQuery] int limit = 5)
    {
        try
        {
            var result = await _recommendationService.GetComplementaryProductsAsync(productId, userId, limit);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des produits complémentaires pour {ProductId}", productId);
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Obtenir des produits alternatifs
    /// </summary>
    [HttpGet("alternatives/{productId}")]
    public async Task<ActionResult<RecommendationResponseDto>> GetAlternativeProducts(
        int productId,
        [FromQuery] string? userId = null,
        [FromQuery] int limit = 5)
    {
        try
        {
            var result = await _recommendationService.GetAlternativeProductsAsync(productId, userId, limit);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des alternatives pour {ProductId}", productId);
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Obtenir les produits fréquemment achetés ensemble
    /// </summary>
    [HttpGet("frequently-bought-together/{productId}")]
    public async Task<ActionResult<RecommendationResponseDto>> GetFrequentlyBoughtTogether(
        int productId,
        [FromQuery] string? userId = null,
        [FromQuery] int limit = 5)
    {
        try
        {
            var result = await _recommendationService.GetFrequentlyBoughtTogetherAsync(productId, userId, limit);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des produits fréquemment achetés ensemble pour {ProductId}", productId);
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Enregistrer une interaction utilisateur pour améliorer les recommandations
    /// </summary>
    [HttpPost("interaction")]
    public async Task<ActionResult> RecordInteraction([FromBody] UserInteractionDto interaction)
    {
        try
        {
            var result = await _recommendationService.RecordInteractionAsync(interaction);
            if (result)
            {
                return Ok(new { message = "Interaction enregistrée avec succès" });
            }
            return BadRequest(new { error = "Impossible d'enregistrer l'interaction" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'enregistrement de l'interaction");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Obtenir les statistiques des recommandations
    /// </summary>
    [HttpGet("stats")]
    public async Task<ActionResult<Dictionary<string, object>>> GetRecommendationStats()
    {
        try
        {
            var stats = await _recommendationService.GetRecommendationStatsAsync();
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des statistiques");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Vérifier la santé du service de recommandations
    /// </summary>
    [HttpGet("health")]
    public async Task<ActionResult<Dictionary<string, bool>>> GetServiceHealth()
    {
        try
        {
            var health = await _recommendationService.GetServiceHealthAsync();
            return Ok(health);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification de la santé du service");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }
}
