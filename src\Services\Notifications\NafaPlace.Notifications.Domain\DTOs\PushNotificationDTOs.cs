namespace NafaPlace.Notifications.Domain.DTOs;

/// <summary>
/// DTO pour les notifications push
/// </summary>
public class PushNotificationDto
{
    public string Title { get; set; } = "";
    public string Body { get; set; } = "";
    public string? ImageUrl { get; set; }
    public string? Icon { get; set; }
    public string? Sound { get; set; } = "default";
    public string? ClickAction { get; set; }
    public string? Url { get; set; }
    public Dictionary<string, string> Data { get; set; } = new();
    public string? Tag { get; set; }
    public string? Color { get; set; }
    public int? Badge { get; set; }
    public bool? RequireInteraction { get; set; }
    public DateTime? ScheduledAt { get; set; }
    public int? Priority { get; set; } = 1; // 1=Normal, 2=High
    public string? Category { get; set; }
    public List<NotificationActionDto> Actions { get; set; } = new();
}

/// <summary>
/// DTO pour les actions de notification
/// </summary>
public class NotificationActionDto
{
    public string Action { get; set; } = "";
    public string Title { get; set; } = "";
    public string? Icon { get; set; }
}

/// <summary>
/// DTO pour les statistiques de notifications
/// </summary>
public class NotificationStatsDto
{
    public int TotalSent { get; set; }
    public int TotalDelivered { get; set; }
    public int TotalOpened { get; set; }
    public int TotalClicked { get; set; }
    public int TotalFailed { get; set; }
    public decimal DeliveryRate { get; set; }
    public decimal OpenRate { get; set; }
    public decimal ClickRate { get; set; }
    public Dictionary<string, int> DeviceBreakdown { get; set; } = new();
    public Dictionary<string, int> PlatformBreakdown { get; set; } = new();
    public List<DailyNotificationStatsDto> DailyStats { get; set; } = new();
}

/// <summary>
/// DTO pour les statistiques quotidiennes
/// </summary>
public class DailyNotificationStatsDto
{
    public DateTime Date { get; set; }
    public int Sent { get; set; }
    public int Delivered { get; set; }
    public int Opened { get; set; }
    public int Clicked { get; set; }
    public int Failed { get; set; }
}

/// <summary>
/// DTO pour les rapports de notifications
/// </summary>
public class NotificationReportDto
{
    public string ReportId { get; set; } = "";
    public string Title { get; set; } = "";
    public string Description { get; set; } = "";
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string ReportType { get; set; } = ""; // Summary, Detailed, Performance
    public NotificationStatsDto Stats { get; set; } = new();
    public List<NotificationCampaignDto> Campaigns { get; set; } = new();
    public List<TopPerformingNotificationDto> TopPerforming { get; set; } = new();
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    public string GeneratedBy { get; set; } = "";
}

/// <summary>
/// DTO pour les campagnes de notifications
/// </summary>
public class NotificationCampaignDto
{
    public string Id { get; set; } = "";
    public string Name { get; set; } = "";
    public string Description { get; set; } = "";
    public PushNotificationDto Template { get; set; } = new();
    public List<string> TargetUsers { get; set; } = new();
    public string TargetCriteria { get; set; } = "";
    public DateTime? ScheduledAt { get; set; }
    public string Status { get; set; } = ""; // Draft, Scheduled, Sent, Cancelled
    public NotificationStatsDto Stats { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = "";
}

/// <summary>
/// DTO pour les notifications les plus performantes
/// </summary>
public class TopPerformingNotificationDto
{
    public string Id { get; set; } = "";
    public string Title { get; set; } = "";
    public string Type { get; set; } = "";
    public int TotalSent { get; set; }
    public decimal OpenRate { get; set; }
    public decimal ClickRate { get; set; }
    public DateTime SentAt { get; set; }
    public string? CampaignId { get; set; }
    public string? CampaignName { get; set; }
}

// DTOs additionnels pour le Controller
public class PushSubscriptionDto
{
    public string Endpoint { get; set; } = "";
    public string P256dh { get; set; } = "";
    public string Auth { get; set; } = "";
    public string? UserId { get; set; }
    public string? DeviceType { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
    public DateTime CreatedAt { get; set; }
}

public class UpdatePushSubscriptionDto
{
    public string? UserId { get; set; }
    public string? DeviceType { get; set; }
    public bool IsEnabled { get; set; } = true;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class NotificationPreferencesDto
{
    public string? UserId { get; set; }
    public bool PushEnabled { get; set; } = true;
    public bool EmailEnabled { get; set; } = true;
    public bool SMSEnabled { get; set; } = false;
    public Dictionary<string, bool> CategoryPreferences { get; set; } = new();
    public string? QuietHoursStart { get; set; }
    public string? QuietHoursEnd { get; set; }
    public string? TimeZone { get; set; }
}

/// <summary>
/// Interface pour le service de gestion des souscriptions
/// </summary>
public interface INotificationSubscriptionService
{
    Task<bool> SubscribeAsync(PushSubscriptionDto subscription);
    Task<bool> UnsubscribeAsync(string endpoint);
    Task<bool> UpdateSubscriptionAsync(string endpoint, UpdatePushSubscriptionDto update);
    Task<List<PushSubscriptionDto>> GetUserSubscriptionsAsync(string userId);
    Task<bool> UpdatePreferencesAsync(string userId, NotificationPreferencesDto preferences);
    Task<NotificationPreferencesDto> GetPreferencesAsync(string userId);

    // Méthodes compatibles avec le contrôleur
    Task<ServiceResult> CreateSubscriptionAsync(PushSubscriptionDto subscription);
    Task<bool> RemoveSubscriptionAsync(string endpoint);
    Task<NotificationPreferencesDto> GetUserPreferencesAsync(string userId);
    Task<bool> UpdateUserPreferencesAsync(string userId, NotificationPreferencesDto preferences);
}

public class ServiceResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public string? Message { get; set; }
}