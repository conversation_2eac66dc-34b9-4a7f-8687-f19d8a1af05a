@using Microsoft.JSInterop
@using NafaPlace.Web.Models.Common
@using System.Linq
@inject IJSRuntime JSRuntime
@inject HttpClient HttpClient

<div class="one-page-checkout">
    <div class="container-fluid">
        <div class="row">
            <!-- Left Column - Checkout Steps -->
            <div class="col-lg-8">
                <div class="checkout-progress mb-4">
                    <div class="progress-steps">
                        <div class="step @(CurrentStep >= 1 ? "active" : "") @(CurrentStep > 1 ? "completed" : "")">
                            <div class="step-number">1</div>
                            <div class="step-title">Livraison</div>
                        </div>
                        <div class="step @(CurrentStep >= 2 ? "active" : "") @(CurrentStep > 2 ? "completed" : "")">
                            <div class="step-number">2</div>
                            <div class="step-title">Paiement</div>
                        </div>
                        <div class="step @(CurrentStep >= 3 ? "active" : "") @(CurrentStep > 3 ? "completed" : "")">
                            <div class="step-number">3</div>
                            <div class="step-title">Confirmation</div>
                        </div>
                    </div>
                </div>

                <!-- Step 1: Shipping Information -->
                <div class="checkout-step @(CurrentStep == 1 ? "active" : "")">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-truck me-2"></i>
                                Informations de livraison
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Prénom *</label>
                                        <input @bind="ShippingInfo.FirstName" class="form-control" placeholder="Votre prénom" />
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Nom *</label>
                                        <input @bind="ShippingInfo.LastName" class="form-control" placeholder="Votre nom" />
                                    </div>
                                </div>
                            </div>
                            <div class="form-group mb-3">
                                <label class="form-label">Adresse *</label>
                                <input @bind="ShippingInfo.Address" class="form-control" placeholder="Votre adresse complète" />
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Ville *</label>
                                        <select @bind="ShippingInfo.City" class="form-select">
                                            <option value="">Sélectionnez votre ville</option>
                                            <option value="Conakry">Conakry</option>
                                            <option value="Kankan">Kankan</option>
                                            <option value="Labé">Labé</option>
                                            <option value="Kindia">Kindia</option>
                                            <option value="Mamou">Mamou</option>
                                            <option value="Boké">Boké</option>
                                            <option value="Faranah">Faranah</option>
                                            <option value="Siguiri">Siguiri</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Téléphone *</label>
                                        <input @bind="ShippingInfo.Phone" class="form-control" placeholder="+224 XXX XXX XXX" />
                                    </div>
                                </div>
                            </div>

                            <!-- Delivery Options -->
                            <div class="delivery-options mt-4">
                                <h6>Options de livraison</h6>
                                <div class="delivery-option">
                                    <input type="radio" id="standard" name="delivery" @onchange="@(() => SelectDeliveryOption("standard"))" checked="@(SelectedDeliveryOption == "standard")" />
                                    <label for="standard" class="delivery-label">
                                        <div class="delivery-info">
                                            <div class="delivery-title">Livraison standard</div>
                                            <div class="delivery-desc">3-5 jours ouvrables</div>
                                        </div>
                                        <div class="delivery-price">25,000 GNF</div>
                                    </label>
                                </div>
                                <div class="delivery-option">
                                    <input type="radio" id="express" name="delivery" @onchange="@(() => SelectDeliveryOption("express"))" checked="@(SelectedDeliveryOption == "express")" />
                                    <label for="express" class="delivery-label">
                                        <div class="delivery-info">
                                            <div class="delivery-title">Livraison express</div>
                                            <div class="delivery-desc">1-2 jours ouvrables</div>
                                        </div>
                                        <div class="delivery-price">50,000 GNF</div>
                                    </label>
                                </div>
                                <div class="delivery-option">
                                    <input type="radio" id="pickup" name="delivery" @onchange="@(() => SelectDeliveryOption("pickup"))" checked="@(SelectedDeliveryOption == "pickup")" />
                                    <label for="pickup" class="delivery-label">
                                        <div class="delivery-info">
                                            <div class="delivery-title">Retrait en magasin</div>
                                            <div class="delivery-desc">Disponible aujourd'hui</div>
                                        </div>
                                        <div class="delivery-price">Gratuit</div>
                                    </label>
                                </div>
                            </div>

                            <div class="d-flex justify-content-end mt-4">
                                <button class="btn btn-primary btn-lg" @onclick="() => NextStep()" disabled="@(!IsShippingValid())">
                                    Continuer vers le paiement
                                    <i class="fas fa-arrow-right ms-2"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Payment Information -->
                <div class="checkout-step @(CurrentStep == 2 ? "active" : "")">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-credit-card me-2"></i>
                                Méthode de paiement
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- Payment Methods -->
                            <div class="payment-methods">
                                <div class="payment-method">
                                    <input type="radio" id="card" name="payment" @onchange="@(() => SelectPaymentMethod("card"))" checked="@(SelectedPaymentMethod == "card")" />
                                    <label for="card" class="payment-label">
                                        <i class="fas fa-credit-card text-primary"></i>
                                        <span>Carte bancaire</span>
                                        <small class="text-muted">Visa, Mastercard</small>
                                    </label>
                                </div>
                                <div class="payment-method">
                                    <input type="radio" id="orange" name="payment" @onchange="@(() => SelectPaymentMethod("orange"))" checked="@(SelectedPaymentMethod == "orange")" />
                                    <label for="orange" class="payment-label">
                                        <i class="fas fa-mobile-alt text-warning"></i>
                                        <span>Orange Money</span>
                                        <small class="text-muted">Paiement mobile</small>
                                    </label>
                                </div>
                                <div class="payment-method">
                                    <input type="radio" id="mtn" name="payment" @onchange="@(() => SelectPaymentMethod("mtn"))" checked="@(SelectedPaymentMethod == "mtn")" />
                                    <label for="mtn" class="payment-label">
                                        <i class="fas fa-mobile-alt text-warning"></i>
                                        <span>MTN Mobile Money</span>
                                        <small class="text-muted">Paiement mobile</small>
                                    </label>
                                </div>
                                <div class="payment-method">
                                    <input type="radio" id="cod" name="payment" @onchange="@(() => SelectPaymentMethod("cod"))" checked="@(SelectedPaymentMethod == "cod")" />
                                    <label for="cod" class="payment-label">
                                        <i class="fas fa-money-bill text-success"></i>
                                        <span>Paiement à la livraison</span>
                                        <small class="text-muted">Espèces uniquement</small>
                                    </label>
                                </div>
                            </div>

                            <!-- Payment Details -->
                            @if (SelectedPaymentMethod == "card")
                            {
                                <div class="payment-details mt-4">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="form-group mb-3">
                                                <label class="form-label">Numéro de carte</label>
                                                <input @bind="PaymentInfo.CardNumber" class="form-control" placeholder="1234 5678 9012 3456" maxlength="19" />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label class="form-label">Date d'expiration</label>
                                                <input @bind="PaymentInfo.ExpiryDate" class="form-control" placeholder="MM/AA" maxlength="5" />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label class="form-label">Code CVV</label>
                                                <input @bind="PaymentInfo.CVV" class="form-control" placeholder="123" maxlength="4" type="password" />
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="form-group mb-3">
                                                <label class="form-label">Nom sur la carte</label>
                                                <input @bind="PaymentInfo.CardholderName" class="form-control" placeholder="John Doe" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }

                            @if (SelectedPaymentMethod == "orange" || SelectedPaymentMethod == "mtn")
                            {
                                <div class="payment-details mt-4">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Numéro de téléphone</label>
                                        <input @bind="PaymentInfo.MobileNumber" class="form-control" placeholder="+224 XXX XXX XXX" />
                                    </div>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Vous recevrez un SMS avec les instructions de paiement.
                                    </div>
                                </div>
                            }

                            <div class="d-flex justify-content-between mt-4">
                                <button class="btn btn-outline-secondary btn-lg" @onclick="() => PreviousStep()">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    Retour
                                </button>
                                <button class="btn btn-primary btn-lg" @onclick="() => NextStep()" disabled="@(!IsPaymentValid())">
                                    Réviser la commande
                                    <i class="fas fa-arrow-right ms-2"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Order Review -->
                <div class="checkout-step @(CurrentStep == 3 ? "active" : "")">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-check-circle me-2"></i>
                                Confirmation de commande
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="order-review">
                                <!-- Shipping Summary -->
                                <div class="review-section">
                                    <h6 class="section-title">Livraison</h6>
                                    <div class="review-content">
                                        <p class="mb-1">@ShippingInfo.FirstName @ShippingInfo.LastName</p>
                                        <p class="mb-1">@ShippingInfo.Address</p>
                                        <p class="mb-1">@ShippingInfo.City</p>
                                        <p class="mb-0">@ShippingInfo.Phone</p>
                                    </div>
                                    <button class="btn btn-link btn-sm" @onclick="() => GoToStep(1)">Modifier</button>
                                </div>

                                <!-- Payment Summary -->
                                <div class="review-section">
                                    <h6 class="section-title">Paiement</h6>
                                    <div class="review-content">
                                        <p class="mb-0">@GetPaymentMethodDisplay()</p>
                                    </div>
                                    <button class="btn btn-link btn-sm" @onclick="() => GoToStep(2)">Modifier</button>
                                </div>
                            </div>

                            <div class="terms-agreement mt-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" @bind="AcceptTerms" id="terms" />
                                    <label class="form-check-label" for="terms">
                                        J'accepte les <a href="/terms" target="_blank">conditions générales</a> et la <a href="/privacy" target="_blank">politique de confidentialité</a>
                                    </label>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between mt-4">
                                <button class="btn btn-outline-secondary btn-lg" @onclick="() => PreviousStep()">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    Retour
                                </button>
                                <button class="btn btn-success btn-lg" @onclick="PlaceOrder" disabled="@(!AcceptTerms || IsProcessing)">
                                    @if (IsProcessing)
                                    {
                                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                        <span>Traitement...</span>
                                    }
                                    else
                                    {
                                        <i class="fas fa-check me-2"></i>
                                        <span>Confirmer la commande</span>
                                    }
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column - Order Summary -->
            <div class="col-lg-4">
                <div class="order-summary sticky-top">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Résumé de commande</h6>
                        </div>
                        <div class="card-body">
                            <!-- Cart Items -->
                            <div class="cart-items">
                                @if (CartItems != null)
                                {
                                    @foreach (var item in CartItems)
                                    {
                                        <div class="cart-item">
                                            <div class="item-image">
                                                <img src="@item.ProductImage" alt="@item.ProductName" />
                                            </div>
                                            <div class="item-details">
                                                <div class="item-name">@item.ProductName</div>
                                                <div class="item-price">@item.UnitPrice.ToString("N0") GNF × @item.Quantity</div>
                                            </div>
                                            <div class="item-total">
                                                @((item.UnitPrice * item.Quantity).ToString("N0")) GNF
                                            </div>
                                        </div>
                                    }
                                }
                            </div>

                            <!-- Pricing Breakdown -->
                            <div class="pricing-breakdown">
                                <div class="pricing-row">
                                    <span>Sous-total</span>
                                    <span>@SubTotal.ToString("N0") GNF</span>
                                </div>
                                <div class="pricing-row">
                                    <span>Livraison</span>
                                    <span>@DeliveryFee.ToString("N0") GNF</span>
                                </div>
                                @if (DiscountAmount > 0)
                                {
                                    <div class="pricing-row discount">
                                        <span>Remise</span>
                                        <span>-@DiscountAmount.ToString("N0") GNF</span>
                                    </div>
                                }
                                <div class="pricing-row">
                                    <span>TVA (18%)</span>
                                    <span>@TaxAmount.ToString("N0") GNF</span>
                                </div>
                                <hr />
                                <div class="pricing-row total">
                                    <strong>
                                        <span>Total</span>
                                        <span>@Total.ToString("N0") GNF</span>
                                    </strong>
                                </div>
                            </div>

                            <!-- Promo Code -->
                            <div class="promo-code mt-3">
                                <div class="input-group">
                                    <input @bind="PromoCode" class="form-control" placeholder="Code promo" />
                                    <button class="btn btn-outline-primary" @onclick="ApplyPromoCode" disabled="@string.IsNullOrEmpty(PromoCode)">
                                        Appliquer
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Security Badges -->
                    <div class="security-badges mt-3">
                        <div class="text-center">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt me-1"></i>
                                Paiement sécurisé SSL
                            </small>
                        </div>
                        <div class="text-center mt-2">
                            <img src="/images/security-badges.png" alt="Security Badges" class="img-fluid" style="max-height: 30px;" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .one-page-checkout {
        padding: 2rem 0;
        background-color: #f8f9fa;
        min-height: 100vh;
    }

    .checkout-progress {
        background: white;
        border-radius: 10px;
        padding: 2rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .progress-steps {
        display: flex;
        justify-content: space-between;
        position: relative;
    }

    .progress-steps::before {
        content: '';
        position: absolute;
        top: 20px;
        left: 40px;
        right: 40px;
        height: 2px;
        background: #dee2e6;
        z-index: 1;
    }

    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 2;
    }

    .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #dee2e6;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
    }

    .step.active .step-number {
        background: #E73C30;
        color: white;
    }

    .step.completed .step-number {
        background: #28a745;
        color: white;
    }

    .checkout-step {
        display: none;
    }

    .checkout-step.active {
        display: block;
    }

    .delivery-options {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
    }

    .delivery-option {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .delivery-option:last-child {
        margin-bottom: 0;
    }

    .delivery-label {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        margin-left: 1rem;
        cursor: pointer;
        padding: 0.75rem;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        transition: all 0.3s ease;
    }

    .delivery-label:hover {
        border-color: #E73C30;
        background-color: #fff5f5;
    }

    .delivery-option input[type="radio"]:checked + .delivery-label {
        border-color: #E73C30;
        background-color: #fff5f5;
    }

    .payment-methods {
        display: grid;
        gap: 1rem;
    }

    .payment-method {
        display: flex;
        align-items: center;
    }

    .payment-label {
        display: flex;
        align-items: center;
        width: 100%;
        margin-left: 1rem;
        cursor: pointer;
        padding: 1rem;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .payment-label:hover {
        border-color: #E73C30;
        background-color: #fff5f5;
    }

    .payment-method input[type="radio"]:checked + .payment-label {
        border-color: #E73C30;
        background-color: #fff5f5;
    }

    .payment-label i {
        font-size: 1.5rem;
        margin-right: 1rem;
    }

    .payment-label span {
        font-weight: 600;
        margin-right: auto;
    }

    .payment-label small {
        margin-left: 1rem;
    }

    .order-summary {
        top: 2rem;
    }

    .cart-item {
        display: flex;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #eee;
    }

    .cart-item:last-child {
        border-bottom: none;
    }

    .item-image img {
        width: 50px;
        height: 50px;
        object-fit: cover;
        border-radius: 6px;
    }

    .item-details {
        flex: 1;
        margin-left: 1rem;
    }

    .item-name {
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .item-price {
        color: #6c757d;
        font-size: 0.875rem;
    }

    .item-total {
        font-weight: 600;
        color: #E73C30;
    }

    .pricing-breakdown {
        margin-top: 1rem;
    }

    .pricing-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
    }

    .pricing-row.total {
        font-size: 1.125rem;
        color: #E73C30;
    }

    .pricing-row.discount {
        color: #28a745;
    }

    .review-section {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 1rem 0;
        border-bottom: 1px solid #eee;
    }

    .review-section:last-child {
        border-bottom: none;
    }

    .section-title {
        font-weight: 600;
        margin-bottom: 0.5rem;
        min-width: 100px;
    }

    .review-content {
        flex: 1;
        margin: 0 1rem;
    }

    @@media (max-width: 768px) {
        .progress-steps {
            flex-direction: column;
        }

        .progress-steps::before {
            display: none;
        }

        .step {
            flex-direction: row;
            justify-content: flex-start;
            margin-bottom: 1rem;
        }

        .step-number {
            margin-right: 1rem;
            margin-bottom: 0;
        }

        .order-summary {
            margin-top: 2rem;
        }
    }
</style>

@code {
    [Parameter] public List<CartItemDto> CartItems { get; set; } = new();
    [Parameter] public EventCallback<OrderDto> OnOrderPlaced { get; set; }

    private int CurrentStep = 1;
    private bool IsProcessing = false;
    private bool AcceptTerms = false;

    // Shipping Information
    private ShippingInfoDto ShippingInfo = new();
    private string SelectedDeliveryOption = "standard";

    // Payment Information
    private PaymentInfoDto PaymentInfo = new();
    private string SelectedPaymentMethod = "card";

    // Pricing
    private decimal SubTotal => CartItems?.Sum(x => x.UnitPrice * x.Quantity) ?? 0;
    private decimal DeliveryFee => SelectedDeliveryOption switch
    {
        "express" => 50000,
        "pickup" => 0,
        _ => 25000
    };
    private decimal DiscountAmount = 0;
    private decimal TaxAmount => (SubTotal + DeliveryFee - DiscountAmount) * 0.18m;
    private decimal Total => SubTotal + DeliveryFee + TaxAmount - DiscountAmount;

    private string PromoCode = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadCartItems();
    }

    private async Task LoadCartItems()
    {
        try
        {
            var response = await HttpClient.GetAsync("/api/cart");
            if (response.IsSuccessStatusCode)
            {
                var cartData = await response.Content.ReadFromJsonAsync<CartDto>();
                CartItems = cartData?.Items ?? new List<CartItemDto>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading cart: {ex.Message}");
        }
    }

    private void NextStep()
    {
        if (CurrentStep < 3)
        {
            CurrentStep++;
            StateHasChanged();
        }
    }

    private void PreviousStep()
    {
        if (CurrentStep > 1)
        {
            CurrentStep--;
            StateHasChanged();
        }
    }

    private void GoToStep(int step)
    {
        CurrentStep = step;
        StateHasChanged();
    }

    private void SelectDeliveryOption(string option)
    {
        SelectedDeliveryOption = option;
        StateHasChanged();
    }

    private void SelectPaymentMethod(string method)
    {
        SelectedPaymentMethod = method;
        StateHasChanged();
    }

    private bool IsShippingValid()
    {
        return !string.IsNullOrEmpty(ShippingInfo.FirstName) &&
               !string.IsNullOrEmpty(ShippingInfo.LastName) &&
               !string.IsNullOrEmpty(ShippingInfo.Address) &&
               !string.IsNullOrEmpty(ShippingInfo.City) &&
               !string.IsNullOrEmpty(ShippingInfo.Phone);
    }

    private bool IsPaymentValid()
    {
        return SelectedPaymentMethod switch
        {
            "card" => !string.IsNullOrEmpty(PaymentInfo.CardNumber) &&
                     !string.IsNullOrEmpty(PaymentInfo.ExpiryDate) &&
                     !string.IsNullOrEmpty(PaymentInfo.CVV) &&
                     !string.IsNullOrEmpty(PaymentInfo.CardholderName),
            "orange" or "mtn" => !string.IsNullOrEmpty(PaymentInfo.MobileNumber),
            "cod" => true,
            _ => false
        };
    }

    private string GetPaymentMethodDisplay()
    {
        return SelectedPaymentMethod switch
        {
            "card" => $"Carte bancaire ****{PaymentInfo.CardNumber?.Substring(Math.Max(0, PaymentInfo.CardNumber.Length - 4))}",
            "orange" => $"Orange Money - {PaymentInfo.MobileNumber}",
            "mtn" => $"MTN Mobile Money - {PaymentInfo.MobileNumber}",
            "cod" => "Paiement à la livraison",
            _ => ""
        };
    }

    private async Task ApplyPromoCode()
    {
        if (string.IsNullOrEmpty(PromoCode)) return;

        try
        {
            var response = await HttpClient.PostAsJsonAsync("/api/coupons/apply", new { Code = PromoCode, Total = SubTotal });
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<CouponApplicationResult>();
                if (result?.IsValid == true)
                {
                    DiscountAmount = result.DiscountAmount;
                    await JSRuntime.InvokeVoidAsync("showToast", "Code promo appliqué avec succès!", "success");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("showToast", "Code promo invalide ou expiré", "error");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error applying promo code: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("showToast", "Erreur lors de l'application du code promo", "error");
        }

        StateHasChanged();
    }

    private async Task PlaceOrder()
    {
        if (!AcceptTerms || IsProcessing) return;

        IsProcessing = true;
        StateHasChanged();

        try
        {
            var order = new OrderDto
            {
                ShippingInfo = $"{ShippingInfo.FullName}, {ShippingInfo.Address}, {ShippingInfo.City}",
                PaymentMethod = SelectedPaymentMethod,
                PaymentInfo = $"{PaymentInfo.CardNumber} - {PaymentInfo.CardholderName}",
                DeliveryOption = SelectedDeliveryOption,
                Items = CartItems.Select(c => new OrderItemDto
                {
                    ProductId = c.ProductId,
                    ProductName = c.ProductName,
                    Quantity = c.Quantity,
                    UnitPrice = c.UnitPrice,
                    ImageUrl = c.ImageUrl
                }).ToList(),
                SubTotal = SubTotal,
                DeliveryFee = DeliveryFee,
                TaxAmount = TaxAmount,
                DiscountAmount = DiscountAmount,
                Total = Total,
                PromoCode = PromoCode
            };

            var response = await HttpClient.PostAsJsonAsync("/api/orders", order);

            if (response.IsSuccessStatusCode)
            {
                var createdOrder = await response.Content.ReadFromJsonAsync<OrderDto>();
                await OnOrderPlaced.InvokeAsync(createdOrder);
                await JSRuntime.InvokeVoidAsync("showToast", "Commande créée avec succès!", "success");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("showToast", "Erreur lors de la création de la commande", "error");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error placing order: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("showToast", "Erreur lors de la création de la commande", "error");
        }
        finally
        {
            IsProcessing = false;
            StateHasChanged();
        }
    }

    public class ShippingInfoDto
    {
        public string FirstName { get; set; } = "";
        public string LastName { get; set; } = "";
        public string Address { get; set; } = "";
        public string City { get; set; } = "";
        public string Phone { get; set; } = "";
        public string FullName => $"{FirstName} {LastName}".Trim();
    }

    public class PaymentInfoDto
    {
        public string CardNumber { get; set; } = "";
        public string ExpiryDate { get; set; } = "";
        public string CVV { get; set; } = "";
        public string CardholderName { get; set; } = "";
        public string MobileNumber { get; set; } = "";
    }

    public class CouponApplicationResult
    {
        public bool IsValid { get; set; }
        public decimal DiscountAmount { get; set; }
        public string Message { get; set; } = "";
    }
}