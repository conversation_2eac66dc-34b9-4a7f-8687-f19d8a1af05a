using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace NafaPlace.Localization.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class InitialLocalizationCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Table des langues supportées
            migrationBuilder.CreateTable(
                name: "Languages",
                columns: table => new
                {
                    Code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    NativeName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Region = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    IsRightToLeft = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    IsDefault = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    Priority = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    FlagIcon = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CurrencyCode = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    CurrencySymbol = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    CurrencyName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    DecimalPlaces = table.Column<int>(type: "integer", nullable: false, defaultValue: 2),
                    DecimalSeparator = table.Column<string>(type: "character varying(5)", maxLength: 5, nullable: false, defaultValue: ","),
                    ThousandsSeparator = table.Column<string>(type: "character varying(5)", maxLength: 5, nullable: false, defaultValue: " "),
                    SymbolBefore = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    ExchangeRate = table.Column<double>(type: "double precision", nullable: false, defaultValue: 1.0),
                    ShortDateFormat = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "dd/MM/yyyy"),
                    LongDateFormat = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false, defaultValue: "dddd, dd MMMM yyyy"),
                    TimeFormat = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "HH:mm"),
                    DateTimeFormat = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false, defaultValue: "dd/MM/yyyy HH:mm"),
                    TimeZone = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "UTC"),
                    WeekStartDay = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    Metadata = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Languages", x => x.Code);
                });

            // Table des traductions
            migrationBuilder.CreateTable(
                name: "Translations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Key = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    LanguageCode = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    Value = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    Context = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    Source = table.Column<int>(type: "integer", nullable: false),
                    Namespace = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Tags = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    Version = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    IsPlural = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    PluralForms = table.Column<string>(type: "jsonb", nullable: true),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    UpdatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Metadata = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Translations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Translations_Languages_LanguageCode",
                        column: x => x.LanguageCode,
                        principalTable: "Languages",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Cascade);
                });

            // Table des traductions automatiques
            migrationBuilder.CreateTable(
                name: "AutoTranslations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    SourceText = table.Column<string>(type: "text", nullable: false),
                    SourceLanguage = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    TargetLanguage = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    TranslatedText = table.Column<string>(type: "text", nullable: false),
                    Provider = table.Column<int>(type: "integer", nullable: false),
                    ConfidenceScore = table.Column<double>(type: "double precision", nullable: false),
                    IsReviewed = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    IsApproved = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    ReviewedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ReviewedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Metadata = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AutoTranslations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AutoTranslations_Languages_SourceLanguage",
                        column: x => x.SourceLanguage,
                        principalTable: "Languages",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AutoTranslations_Languages_TargetLanguage",
                        column: x => x.TargetLanguage,
                        principalTable: "Languages",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Cascade);
                });

            // Table de la mémoire de traduction
            migrationBuilder.CreateTable(
                name: "TranslationMemory",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    SourceText = table.Column<string>(type: "text", nullable: false),
                    TargetText = table.Column<string>(type: "text", nullable: false),
                    SourceLanguage = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    TargetLanguage = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    SimilarityScore = table.Column<double>(type: "double precision", nullable: false, defaultValue: 1.0),
                    UsageCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    LastUsed = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    Context = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    Tags = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    Metadata = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TranslationMemory", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TranslationMemory_Languages_SourceLanguage",
                        column: x => x.SourceLanguage,
                        principalTable: "Languages",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TranslationMemory_Languages_TargetLanguage",
                        column: x => x.TargetLanguage,
                        principalTable: "Languages",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Cascade);
                });

            // Table des lots de traduction
            migrationBuilder.CreateTable(
                name: "TranslationBatches",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    SourceLanguage = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    TargetLanguages = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    TotalKeys = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    CompletedKeys = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    PendingKeys = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    FailedKeys = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    Progress = table.Column<double>(type: "double precision", nullable: false, defaultValue: 0),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CompletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Metadata = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TranslationBatches", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TranslationBatches_Languages_SourceLanguage",
                        column: x => x.SourceLanguage,
                        principalTable: "Languages",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Cascade);
                });

            // Table des projets de traduction
            migrationBuilder.CreateTable(
                name: "TranslationProjects",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    SourceLanguage = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    TargetLanguages = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    StartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Deadline = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ProjectManager = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Translators = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    Reviewers = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    TotalKeys = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    ProgressByLanguage = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    Settings = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    Metadata = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TranslationProjects", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TranslationProjects_Languages_SourceLanguage",
                        column: x => x.SourceLanguage,
                        principalTable: "Languages",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Cascade);
                });

            // Table des workflows de localisation
            migrationBuilder.CreateTable(
                name: "LocalizationWorkflows",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    Steps = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    Languages = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    IsDefault = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    Settings = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}"),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LocalizationWorkflows", x => x.Id);
                });

            // Table de qualité des traductions
            migrationBuilder.CreateTable(
                name: "TranslationQuality",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    TranslationId = table.Column<int>(type: "integer", nullable: false),
                    OverallScore = table.Column<int>(type: "integer", nullable: false),
                    AccuracyScore = table.Column<int>(type: "integer", nullable: false),
                    FluencyScore = table.Column<int>(type: "integer", nullable: false),
                    ConsistencyScore = table.Column<int>(type: "integer", nullable: false),
                    Issues = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    Suggestions = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]"),
                    AssessedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    AssessedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    Metadata = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "{}")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TranslationQuality", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TranslationQuality_Translations_TranslationId",
                        column: x => x.TranslationId,
                        principalTable: "Translations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            // Table de l'historique des traductions
            migrationBuilder.CreateTable(
                name: "TranslationHistory",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    TranslationId = table.Column<int>(type: "integer", nullable: false),
                    OldValue = table.Column<string>(type: "text", nullable: false),
                    NewValue = table.Column<string>(type: "text", nullable: false),
                    ChangeType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ChangedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Reason = table.Column<string>(type: "text", nullable: true),
                    Version = table.Column<int>(type: "integer", nullable: false),
                    ChangedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TranslationHistory", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TranslationHistory_Translations_TranslationId",
                        column: x => x.TranslationId,
                        principalTable: "Translations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            // Index pour les performances
            migrationBuilder.CreateIndex(
                name: "IX_Languages_IsActive_IsDefault",
                table: "Languages",
                columns: new[] { "IsActive", "IsDefault" });

            migrationBuilder.CreateIndex(
                name: "IX_Languages_Priority",
                table: "Languages",
                column: "Priority");

            migrationBuilder.CreateIndex(
                name: "IX_Translations_Key_LanguageCode",
                table: "Translations",
                columns: new[] { "Key", "LanguageCode" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Translations_LanguageCode",
                table: "Translations",
                column: "LanguageCode");

            migrationBuilder.CreateIndex(
                name: "IX_Translations_Status",
                table: "Translations",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Translations_Namespace",
                table: "Translations",
                column: "Namespace");

            migrationBuilder.CreateIndex(
                name: "IX_Translations_Source",
                table: "Translations",
                column: "Source");

            migrationBuilder.CreateIndex(
                name: "IX_AutoTranslations_SourceLanguage_TargetLanguage",
                table: "AutoTranslations",
                columns: new[] { "SourceLanguage", "TargetLanguage" });

            migrationBuilder.CreateIndex(
                name: "IX_AutoTranslations_Provider",
                table: "AutoTranslations",
                column: "Provider");

            migrationBuilder.CreateIndex(
                name: "IX_AutoTranslations_IsReviewed_IsApproved",
                table: "AutoTranslations",
                columns: new[] { "IsReviewed", "IsApproved" });

            migrationBuilder.CreateIndex(
                name: "IX_TranslationMemory_SourceLanguage_TargetLanguage",
                table: "TranslationMemory",
                columns: new[] { "SourceLanguage", "TargetLanguage" });

            migrationBuilder.CreateIndex(
                name: "IX_TranslationMemory_SimilarityScore",
                table: "TranslationMemory",
                column: "SimilarityScore");

            migrationBuilder.CreateIndex(
                name: "IX_TranslationMemory_UsageCount",
                table: "TranslationMemory",
                column: "UsageCount");

            migrationBuilder.CreateIndex(
                name: "IX_TranslationBatches_Status",
                table: "TranslationBatches",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_TranslationBatches_SourceLanguage",
                table: "TranslationBatches",
                column: "SourceLanguage");

            migrationBuilder.CreateIndex(
                name: "IX_TranslationProjects_Status",
                table: "TranslationProjects",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_TranslationProjects_SourceLanguage",
                table: "TranslationProjects",
                column: "SourceLanguage");

            migrationBuilder.CreateIndex(
                name: "IX_LocalizationWorkflows_Status_IsDefault",
                table: "LocalizationWorkflows",
                columns: new[] { "Status", "IsDefault" });

            migrationBuilder.CreateIndex(
                name: "IX_TranslationQuality_TranslationId",
                table: "TranslationQuality",
                column: "TranslationId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TranslationQuality_OverallScore",
                table: "TranslationQuality",
                column: "OverallScore");

            migrationBuilder.CreateIndex(
                name: "IX_TranslationHistory_TranslationId_Version",
                table: "TranslationHistory",
                columns: new[] { "TranslationId", "Version" });

            migrationBuilder.CreateIndex(
                name: "IX_TranslationHistory_ChangedAt",
                table: "TranslationHistory",
                column: "ChangedAt");

            // Insérer les langues par défaut
            migrationBuilder.InsertData(
                table: "Languages",
                columns: new[] { "Code", "Name", "NativeName", "Region", "IsRightToLeft", "IsActive", "IsDefault", "Priority", "CurrencyCode", "CurrencySymbol", "CurrencyName", "DecimalPlaces" },
                values: new object[,]
                {
                    { "fr", "Français", "Français", "FR", false, true, true, 1, "GNF", "GNF", "Franc Guinéen", 0 },
                    { "en", "English", "English", "US", false, true, false, 2, "GNF", "GNF", "Franc Guinéen", 0 },
                    { "ar", "العربية", "العربية", "SA", true, true, false, 3, "GNF", "GNF", "Franc Guinéen", 0 }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(name: "AutoTranslations");
            migrationBuilder.DropTable(name: "TranslationMemory");
            migrationBuilder.DropTable(name: "TranslationBatches");
            migrationBuilder.DropTable(name: "TranslationProjects");
            migrationBuilder.DropTable(name: "LocalizationWorkflows");
            migrationBuilder.DropTable(name: "TranslationQuality");
            migrationBuilder.DropTable(name: "TranslationHistory");
            migrationBuilder.DropTable(name: "Translations");
            migrationBuilder.DropTable(name: "Languages");
        }
    }
}
