@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@inject IChatService ChatService
@inject ILogger<ContactSellerModal> Logger
@inject NavigationManager Navigation

<style>
    .contact-seller-modal .product-info-card,
    .contact-seller-modal .seller-info-card {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 12px;
        border-left: 3px solid #E73C30;
    }

    .contact-seller-modal .seller-info-card {
        border-left-color: #28a745;
    }

    .contact-seller-modal.modal.show {
        animation: fadeIn 0.3s ease;
    }

    @@keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    .contact-seller-modal .modal-content {
        animation: slideUp 0.3s ease;
    }

    @@keyframes slideUp {
        from {
            transform: translateY(50px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
</style>

<!-- Modal -->
<div class="contact-seller-modal modal fade @(IsVisible ? "show d-block" : "")" tabindex="-1" style="@(IsVisible ? "background: rgba(0,0,0,0.5);" : "")">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-comments me-2"></i>
                    Contacter le vendeur
                </h5>
                <button type="button" class="btn-close" @onclick="Close"></button>
            </div>
            <div class="modal-body">
                @if (!string.IsNullOrEmpty(errorMessage))
                {
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        @errorMessage
                        <button type="button" class="btn-close" @onclick="() => errorMessage = null"></button>
                    </div>
                }

                @if (successMessage != null)
                {
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        @successMessage
                    </div>
                }
                else
                {
                    <!-- Informations produit -->
                    @if (ProductId.HasValue && !string.IsNullOrEmpty(ProductName))
                    {
                        <div class="product-info-card mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-box text-primary me-3" style="font-size: 2rem;"></i>
                                <div>
                                    <h6 class="mb-0">@ProductName</h6>
                                    <small class="text-muted">Produit #@ProductId</small>
                                </div>
                            </div>
                        </div>
                    }

                    <!-- Informations vendeur -->
                    @if (!string.IsNullOrEmpty(SellerName))
                    {
                        <div class="seller-info-card mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-store text-success me-3" style="font-size: 2rem;"></i>
                                <div>
                                    <h6 class="mb-0">@SellerName</h6>
                                    <small class="text-muted">Vendeur</small>
                                </div>
                            </div>
                        </div>
                    }

                    <!-- Formulaire -->
                    <div class="mb-3">
                        <label class="form-label">Sujet <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" @bind="subject" 
                               placeholder="Ex: Question sur les caractéristiques" 
                               disabled="@isSubmitting" />
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Type de demande</label>
                        <select class="form-select" @bind="conversationType" disabled="@isSubmitting">
                            <option value="ProductInquiry">Question sur le produit</option>
                            <option value="PreSale">Avant-vente</option>
                            <option value="General">Général</option>
                            <option value="TechnicalSupport">Support technique</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Message <span class="text-danger">*</span></label>
                        <textarea class="form-control" rows="5" @bind="message" 
                                  placeholder="Décrivez votre question ou demande..."
                                  disabled="@isSubmitting"></textarea>
                        <small class="text-muted">Minimum 10 caractères</small>
                    </div>
                }
            </div>
            <div class="modal-footer">
                @if (successMessage == null)
                {
                    <button type="button" class="btn btn-secondary" @onclick="Close" disabled="@isSubmitting">
                        Annuler
                    </button>
                    <button type="button" class="btn btn-primary" @onclick="SendMessage" disabled="@isSubmitting">
                        @if (isSubmitting)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                            <span>Envoi en cours...</span>
                        }
                        else
                        {
                            <i class="fas fa-paper-plane me-2"></i>
                            <span>Envoyer</span>
                        }
                    </button>
                }
                else
                {
                    <button type="button" class="btn btn-primary" @onclick="Close">
                        Fermer
                    </button>
                }
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter]
    public bool IsVisible { get; set; }

    [Parameter]
    public EventCallback<bool> IsVisibleChanged { get; set; }

    [Parameter]
    public int? ProductId { get; set; }

    [Parameter]
    public string? ProductName { get; set; }

    [Parameter]
    public int? SellerId { get; set; }

    [Parameter]
    public string? SellerName { get; set; }

    [CascadingParameter]
    private Task<AuthenticationState>? AuthenticationStateTask { get; set; }

    private string subject = string.Empty;
    private string message = string.Empty;
    private string conversationType = "ProductInquiry";
    private bool isSubmitting = false;
    private string? errorMessage;
    private string? successMessage;

    protected override void OnParametersSet()
    {
        if (IsVisible && ProductId.HasValue && !string.IsNullOrEmpty(ProductName))
        {
            subject = $"Question sur {ProductName}";
        }
    }

    private async Task SendMessage()
    {
        errorMessage = null;
        successMessage = null;

        // Validation
        if (string.IsNullOrWhiteSpace(subject))
        {
            errorMessage = "Le sujet est obligatoire";
            return;
        }

        if (string.IsNullOrWhiteSpace(message) || message.Length < 10)
        {
            errorMessage = "Le message doit contenir au moins 10 caractères";
            return;
        }

        // Vérifier l'authentification
        if (AuthenticationStateTask == null)
        {
            errorMessage = "Erreur d'authentification";
            return;
        }

        var authState = await AuthenticationStateTask;
        if (!authState.User.Identity?.IsAuthenticated ?? true)
        {
            // Rediriger vers la page de connexion
            Navigation.NavigateTo($"/auth/login?returnUrl={Uri.EscapeDataString(Navigation.Uri)}");
            return;
        }

        var userId = authState.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        var userName = authState.User.FindFirst(ClaimTypes.Name)?.Value 
                       ?? authState.User.FindFirst(ClaimTypes.Email)?.Value 
                       ?? "Utilisateur";
        var userEmail = authState.User.FindFirst(ClaimTypes.Email)?.Value;

        if (string.IsNullOrEmpty(userId))
        {
            errorMessage = "Impossible de récupérer vos informations utilisateur";
            return;
        }

        isSubmitting = true;
        StateHasChanged();

        try
        {
            // Créer la conversation via l'API
            var conversationId = await CreateConversationViaApi(userId, userName, userEmail);

            if (conversationId > 0)
            {
                successMessage = "Votre message a été envoyé avec succès ! Le vendeur vous répondra bientôt.";
                Logger.LogInformation("Conversation créée avec succès: {ConversationId}", conversationId);
                
                // Réinitialiser le formulaire après 2 secondes
                await Task.Delay(2000);
                await Close();
            }
            else
            {
                errorMessage = "Une erreur est survenue lors de l'envoi du message. Veuillez réessayer.";
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'envoi du message au vendeur");
            errorMessage = "Une erreur est survenue. Veuillez réessayer plus tard.";
        }
        finally
        {
            isSubmitting = false;
            StateHasChanged();
        }
    }

    private async Task<int> CreateConversationViaApi(string customerId, string customerName, string? customerEmail)
    {
        try
        {
            var token = await ChatService.GetAuthTokenAsync();
            if (string.IsNullOrEmpty(token))
            {
                Logger.LogWarning("Token d'authentification non trouvé");
                return 0;
            }

            // Appeler l'API Chat E-commerce pour créer la conversation
            var httpClient = new HttpClient();
            httpClient.BaseAddress = new Uri("http://localhost:5000/");
            httpClient.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

            var createDto = new
            {
                CustomerId = customerId,
                CustomerName = customerName,
                CustomerEmail = customerEmail,
                SellerId = SellerId?.ToString(),
                ProductId = ProductId,
                Subject = subject,
                Type = conversationType,
                Priority = "Normal",
                InitialMessage = message
            };

            var response = await httpClient.PostAsJsonAsync("api/chat-ecommerce/conversations", createDto);
            
            if (response.IsSuccessStatusCode)
            {
                var conversationId = await response.Content.ReadFromJsonAsync<int>();
                return conversationId;
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            Logger.LogWarning("Échec de la création de la conversation: {StatusCode} - {Error}", 
                response.StatusCode, errorContent);
            return 0;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'appel API pour créer la conversation");
            return 0;
        }
    }

    private async Task Close()
    {
        // Réinitialiser le formulaire
        subject = string.Empty;
        message = string.Empty;
        conversationType = "ProductInquiry";
        errorMessage = null;
        successMessage = null;

        IsVisible = false;
        await IsVisibleChanged.InvokeAsync(false);
    }
}

