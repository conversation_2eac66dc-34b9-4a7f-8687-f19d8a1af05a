@using Microsoft.AspNetCore.Components.Authorization
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthStateProvider

@code {
    [Parameter] public string? ReturnUrl { get; set; }

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        
        if (!authState.User.Identity?.IsAuthenticated ?? true)
        {
            var returnUrl = ReturnUrl ?? Navigation.Uri;
            Navigation.NavigateTo($"/auth/login?returnUrl={Uri.EscapeDataString(returnUrl)}", true);
        }
    }
}
