using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using NafaPlace.Delivery.Application.Services;
using NafaPlace.Delivery.Domain.DTOs;
using System.Security.Claims;

namespace NafaPlace.Delivery.Infrastructure.Hubs;

/// <summary>
/// Hub SignalR pour le suivi en temps réel des livraisons
/// Gère les connexions des clients, livreurs et administrateurs
/// </summary>
[Authorize]
public class DeliveryTrackingHub : Hub
{
    private readonly ITrackingService _trackingService;
    private readonly ILogger<DeliveryTrackingHub> _logger;

    public DeliveryTrackingHub(ITrackingService trackingService, ILogger<DeliveryTrackingHub> logger)
    {
        _trackingService = trackingService;
        _logger = logger;
    }

    /// <summary>
    /// Connexion d'un utilisateur au hub
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        var userId = Context.UserIdentifier;
        var userRole = Context.User?.FindFirst(ClaimTypes.Role)?.Value;

        _logger.LogInformation("Utilisateur {UserId} connecté au hub de tracking avec le rôle {Role}", userId, userRole);

        // Joindre le groupe selon le rôle
        if (userRole == "DeliveryPerson")
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, "DeliveryPersons");
            _logger.LogInformation("Livreur {UserId} ajouté au groupe DeliveryPersons", userId);
        }
        else if (userRole == "Admin" || userRole == "Manager")
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, "Administrators");
            _logger.LogInformation("Administrateur {UserId} ajouté au groupe Administrators", userId);
        }
        else
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, "Customers");
            _logger.LogInformation("Client {UserId} ajouté au groupe Customers", userId);
        }

        await base.OnConnectedAsync();
    }

    /// <summary>
    /// Déconnexion d'un utilisateur du hub
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var userId = Context.UserIdentifier;
        _logger.LogInformation("Utilisateur {UserId} déconnecté du hub de tracking", userId);

        if (exception != null)
        {
            _logger.LogError(exception, "Erreur lors de la déconnexion de {UserId}", userId);
        }

        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// Un livreur s'abonne aux mises à jour de ses livraisons
    /// </summary>
    [Authorize(Roles = "DeliveryPerson")]
    public async Task SubscribeToMyDeliveries()
    {
        var deliveryPersonId = Context.UserIdentifier;
        if (string.IsNullOrEmpty(deliveryPersonId))
            return;

        var groupName = $"DeliveryPerson_{deliveryPersonId}";
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);

        _logger.LogInformation("Livreur {DeliveryPersonId} s'est abonné à ses livraisons", deliveryPersonId);

        // Envoyer les livraisons actives
        var activeDeliveries = await _trackingService.GetActiveDeliveriesForPersonAsync(deliveryPersonId);
        await Clients.Caller.SendAsync("ActiveDeliveriesUpdate", activeDeliveries);
    }

    /// <summary>
    /// Un client s'abonne au suivi d'une livraison spécifique
    /// </summary>
    public async Task SubscribeToDeliveryTracking(int deliveryId)
    {
        var userId = Context.UserIdentifier;
        if (string.IsNullOrEmpty(userId))
            return;

        // Vérifier les droits d'accès
        var canAccess = await _trackingService.CanUserAccessDeliveryAsync(userId, deliveryId);
        if (!canAccess)
        {
            await Clients.Caller.SendAsync("AccessDenied", "Vous n'avez pas accès à cette livraison");
            return;
        }

        var groupName = $"Delivery_{deliveryId}";
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);

        _logger.LogInformation("Utilisateur {UserId} s'est abonné au suivi de la livraison {DeliveryId}", userId, deliveryId);

        // Envoyer la position actuelle
        var currentPosition = await _trackingService.GetDeliveryCurrentPositionAsync(deliveryId);
        if (currentPosition != null)
        {
            await Clients.Caller.SendAsync("DeliveryPositionUpdate", currentPosition);
        }
    }

    /// <summary>
    /// Se désabonner du suivi d'une livraison
    /// </summary>
    public async Task UnsubscribeFromDeliveryTracking(int deliveryId)
    {
        var groupName = $"Delivery_{deliveryId}";
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);

        _logger.LogInformation("Utilisateur {UserId} s'est désabonné du suivi de la livraison {DeliveryId}", Context.UserIdentifier, deliveryId);
    }

    /// <summary>
    /// Mise à jour de position GPS d'un livreur
    /// </summary>
    [Authorize(Roles = "DeliveryPerson")]
    public async Task UpdatePosition(UpdatePositionDto positionUpdate)
    {
        var deliveryPersonId = Context.UserIdentifier;
        if (string.IsNullOrEmpty(deliveryPersonId))
            return;

        positionUpdate.DeliveryPersonId = deliveryPersonId;

        try
        {
            var result = await _trackingService.UpdateDeliveryPersonPositionAsync(positionUpdate);

            if (result.Success)
            {
                // Notifier tous les clients suivant les livraisons de ce livreur
                var activeDeliveries = await _trackingService.GetActiveDeliveriesForPersonAsync(deliveryPersonId);

                foreach (var delivery in activeDeliveries)
                {
                    var groupName = $"Delivery_{delivery.Id}";
                    var positionData = new
                    {
                        DeliveryId = delivery.Id,
                        DeliveryPersonId = deliveryPersonId,
                        Latitude = positionUpdate.Latitude,
                        Longitude = positionUpdate.Longitude,
                        Speed = positionUpdate.Speed,
                        Heading = positionUpdate.Heading,
                        Timestamp = positionUpdate.Timestamp,
                        Status = positionUpdate.Status
                    };

                    await Clients.Group(groupName).SendAsync("PositionUpdate", positionData);
                }

                // Vérifier les notifications de proximité
                var position = new PositionDto
                {
                    Latitude = positionUpdate.Latitude,
                    Longitude = positionUpdate.Longitude,
                    Timestamp = positionUpdate.Timestamp,
                    Speed = positionUpdate.Speed,
                    Heading = positionUpdate.Heading
                };

                await _trackingService.CheckProximityNotificationsAsync(deliveryPersonId, position);

                await Clients.Caller.SendAsync("PositionUpdateConfirmed", result);
            }
            else
            {
                await Clients.Caller.SendAsync("PositionUpdateError", result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour de position pour {DeliveryPersonId}", deliveryPersonId);
            await Clients.Caller.SendAsync("PositionUpdateError", "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Mise à jour du statut d'une livraison
    /// </summary>
    [Authorize(Roles = "DeliveryPerson")]
    public async Task UpdateDeliveryStatus(UpdateDeliveryStatusDto statusUpdate)
    {
        var deliveryPersonId = Context.UserIdentifier;
        if (string.IsNullOrEmpty(deliveryPersonId))
            return;

        statusUpdate.DeliveryPersonId = deliveryPersonId;

        try
        {
            var result = await _trackingService.UpdateDeliveryStatusWithLocationAsync(statusUpdate);

            if (result.Success)
            {
                // Notifier tous les abonnés à cette livraison
                var groupName = $"Delivery_{statusUpdate.DeliveryId}";
                await Clients.Group(groupName).SendAsync("StatusUpdate", statusUpdate);

                // Notifier les administrateurs
                await Clients.Group("Administrators").SendAsync("DeliveryStatusChanged", statusUpdate);

                await Clients.Caller.SendAsync("StatusUpdateConfirmed", result);
            }
            else
            {
                await Clients.Caller.SendAsync("StatusUpdateError", result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour de statut pour la livraison {DeliveryId}", statusUpdate.DeliveryId);
            await Clients.Caller.SendAsync("StatusUpdateError", "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Démarrer le suivi d'une livraison
    /// </summary>
    [Authorize(Roles = "DeliveryPerson")]
    public async Task StartDeliveryTracking(StartTrackingDto startTracking)
    {
        var deliveryPersonId = Context.UserIdentifier;
        if (string.IsNullOrEmpty(deliveryPersonId))
            return;

        startTracking.DeliveryPersonId = deliveryPersonId;

        try
        {
            var result = await _trackingService.StartDeliveryTrackingAsync(startTracking);

            if (result.Success)
            {
                // Notifier les abonnés à cette livraison
                var groupName = $"Delivery_{startTracking.DeliveryId}";
                await Clients.Group(groupName).SendAsync("TrackingStarted", startTracking);

                // Notifier les administrateurs
                await Clients.Group("Administrators").SendAsync("DeliveryTrackingStarted", startTracking);

                await Clients.Caller.SendAsync("TrackingStartConfirmed", result);
            }
            else
            {
                await Clients.Caller.SendAsync("TrackingStartError", result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du démarrage du suivi pour la livraison {DeliveryId}", startTracking.DeliveryId);
            await Clients.Caller.SendAsync("TrackingStartError", "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Terminer le suivi d'une livraison
    /// </summary>
    [Authorize(Roles = "DeliveryPerson")]
    public async Task CompleteDeliveryTracking(CompleteTrackingDto completeTracking)
    {
        var deliveryPersonId = Context.UserIdentifier;
        if (string.IsNullOrEmpty(deliveryPersonId))
            return;

        completeTracking.DeliveryPersonId = deliveryPersonId;

        try
        {
            var result = await _trackingService.CompleteDeliveryTrackingAsync(completeTracking);

            if (result.Success)
            {
                // Notifier les abonnés à cette livraison
                var groupName = $"Delivery_{completeTracking.DeliveryId}";
                await Clients.Group(groupName).SendAsync("TrackingCompleted", completeTracking);

                // Notifier les administrateurs
                await Clients.Group("Administrators").SendAsync("DeliveryCompleted", completeTracking);

                await Clients.Caller.SendAsync("TrackingCompleteConfirmed", result);
            }
            else
            {
                await Clients.Caller.SendAsync("TrackingCompleteError", result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la finalisation du suivi pour la livraison {DeliveryId}", completeTracking.DeliveryId);
            await Clients.Caller.SendAsync("TrackingCompleteError", "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Demander l'historique de suivi d'une livraison
    /// </summary>
    public async Task RequestTrackingHistory(int deliveryId)
    {
        var userId = Context.UserIdentifier;
        if (string.IsNullOrEmpty(userId))
            return;

        try
        {
            // Vérifier les droits d'accès
            var canAccess = await _trackingService.CanUserAccessDeliveryAsync(userId, deliveryId);
            if (!canAccess)
            {
                await Clients.Caller.SendAsync("AccessDenied", "Vous n'avez pas accès à cette livraison");
                return;
            }

            var history = await _trackingService.GetDeliveryTrackingHistoryAsync(deliveryId);
            await Clients.Caller.SendAsync("TrackingHistoryResponse", history);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de l'historique pour la livraison {DeliveryId}", deliveryId);
            await Clients.Caller.SendAsync("TrackingHistoryError", "Erreur lors de la récupération de l'historique");
        }
    }

    /// <summary>
    /// Calculer le temps estimé d'arrivée
    /// </summary>
    public async Task RequestEstimatedArrival(int deliveryId)
    {
        var userId = Context.UserIdentifier;
        if (string.IsNullOrEmpty(userId))
            return;

        try
        {
            // Vérifier les droits d'accès
            var canAccess = await _trackingService.CanUserAccessDeliveryAsync(userId, deliveryId);
            if (!canAccess)
            {
                await Clients.Caller.SendAsync("AccessDenied", "Vous n'avez pas accès à cette livraison");
                return;
            }

            var eta = await _trackingService.CalculateEstimatedArrivalTimeAsync(deliveryId);
            await Clients.Caller.SendAsync("EstimatedArrivalResponse", eta);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du calcul de l'ETA pour la livraison {DeliveryId}", deliveryId);
            await Clients.Caller.SendAsync("EstimatedArrivalError", "Erreur lors du calcul de l'ETA");
        }
    }

    /// <summary>
    /// S'abonner aux statistiques de livraison en temps réel (administrateurs)
    /// </summary>
    [Authorize(Roles = "Admin,Manager")]
    public async Task SubscribeToRealtimeStats()
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, "RealtimeStats");
        _logger.LogInformation("Administrateur {UserId} s'est abonné aux statistiques temps réel", Context.UserIdentifier);
    }

    /// <summary>
    /// Se désabonner des statistiques temps réel
    /// </summary>
    [Authorize(Roles = "Admin,Manager")]
    public async Task UnsubscribeFromRealtimeStats()
    {
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, "RealtimeStats");
        _logger.LogInformation("Administrateur {UserId} s'est désabonné des statistiques temps réel", Context.UserIdentifier);
    }

    /// <summary>
    /// Rejoindre un groupe régional pour le suivi
    /// </summary>
    [Authorize(Roles = "Admin,Manager")]
    public async Task JoinRegionalGroup(string region)
    {
        var groupName = $"Region_{region}";
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
        _logger.LogInformation("Utilisateur {UserId} a rejoint le groupe régional {Region}", Context.UserIdentifier, region);
    }

    /// <summary>
    /// Quitter un groupe régional
    /// </summary>
    [Authorize(Roles = "Admin,Manager")]
    public async Task LeaveRegionalGroup(string region)
    {
        var groupName = $"Region_{region}";
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
        _logger.LogInformation("Utilisateur {UserId} a quitté le groupe régional {Region}", Context.UserIdentifier, region);
    }
}