@using NafaPlace.Web.Services
@using Microsoft.AspNetCore.SignalR.Client
@inject IChatEcommerceService ChatService
@inject AuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@implements IAsyncDisposable

<!-- Bouton flottant -->
@if (!isOpen && isAuthenticated)
{
    <div class="floating-chat-button" @onclick="ToggleChat">
        <i class="fas fa-comments"></i>
        @if (unreadCount > 0)
        {
            <span class="unread-badge">@unreadCount</span>
        }
    </div>
}

<!-- Fenêtre de chat -->
@if (isOpen && isAuthenticated)
{
    <div class="floating-chat-window">
        <!-- En-tête -->
        <div class="chat-header">
            <div class="chat-header-content">
                @if (selectedConversation == null)
                {
                    <div class="header-with-status">
                        <h3><i class="fas fa-comments"></i> Mes Conversations</h3>
                        @if (hubConnection?.State == HubConnectionState.Connected)
                        {
                            <span class="connection-status connected" title="Connecté en temps réel">
                                <i class="fas fa-circle"></i>
                            </span>
                        }
                        else
                        {
                            <span class="connection-status disconnected" title="Déconnecté">
                                <i class="fas fa-circle"></i>
                            </span>
                        }
                    </div>
                }
                else
                {
                    <div class="chat-header-info">
                        <button class="btn-back" @onclick="BackToConversations">
                            <i class="fas fa-arrow-left"></i>
                        </button>
                        <div>
                            <h3>@selectedConversation.SellerName</h3>
                            <span class="status-badge <EMAIL>()">
                                @selectedConversation.Status
                            </span>
                        </div>
                    </div>
                }
            </div>
            <button class="btn-close" @onclick="ToggleChat">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <!-- Corps -->
        <div class="chat-body">
            @if (isLoading)
            {
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Chargement...</p>
                </div>
            }
            else if (selectedConversation == null)
            {
                <!-- Liste des conversations -->
                <div class="conversations-list">
                    @if (conversations.Any())
                    {
                        @foreach (var conv in conversations)
                        {
                            <div class="conversation-item @(conv.UnreadCount > 0 ? "unread" : "")"
                                 @onclick="() => SelectConversation(conv)">
                                <div class="conversation-avatar">
                                    <i class="fas fa-store"></i>
                                    @if (IsSellerOnline(conv.SellerId))
                                    {
                                        <span class="online-indicator" title="En ligne"></span>
                                    }
                                </div>
                                <div class="conversation-info">
                                    <div class="conversation-header">
                                        <div class="seller-name-with-status">
                                            <h4>@conv.SellerName</h4>
                                            @if (IsSellerOnline(conv.SellerId))
                                            {
                                                <span class="status-badge online">En ligne</span>
                                            }
                                            else
                                            {
                                                <span class="status-badge offline">Hors ligne</span>
                                            }
                                        </div>
                                        <span class="conversation-time">@FormatDate(conv.LastMessageAt ?? conv.CreatedAt)</span>
                                    </div>
                                    <p class="conversation-preview">@conv.Subject</p>
                                    @if (conv.UnreadCount > 0)
                                    {
                                        <span class="unread-count">@conv.UnreadCount</span>
                                    }
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="empty-state">
                            <i class="fas fa-inbox"></i>
                            <p>Aucune conversation</p>
                            <small>Contactez un vendeur pour démarrer une conversation</small>
                        </div>
                    }
                </div>
            }
            else
            {
                <!-- Messages de la conversation -->
                <div class="messages-container" @ref="messagesContainer">
                    @if (messages.Any())
                    {
                        @foreach (var msg in messages)
                        {
                            <div class="message @(msg.SenderType == "Customer" ? "message-customer" : "message-seller")">
                                <div class="message-content">
                                    <p>@msg.Content</p>
                                    <span class="message-time">@msg.Timestamp.ToString("HH:mm")</span>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="empty-messages">
                            <i class="fas fa-comment-dots"></i>
                            <p>Aucun message</p>
                        </div>
                    }
                </div>

                <!-- Zone de saisie -->
                <div class="chat-input-area">
                    <input type="text" 
                           class="chat-input" 
                           placeholder="Écrivez votre message..." 
                           @bind="newMessage"
                           @onkeypress="HandleKeyPress" />
                    <button class="btn-send" @onclick="SendMessage" disabled="@(string.IsNullOrWhiteSpace(newMessage))">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            }
        </div>
    </div>
}

@code {
    private bool isOpen = false;
    private bool isAuthenticated = false;
    private bool isLoading = false;
    private List<ConversationDto> conversations = new();
    private ConversationDto? selectedConversation = null;
    private List<MessageDto> messages = new();
    private string newMessage = string.Empty;
    private int unreadCount = 0;
    private ElementReference messagesContainer;
    private System.Threading.Timer? refreshTimer;
    private HubConnection? hubConnection;
    private string? currentUserId;
    private Dictionary<string, bool> onlineUsers = new();

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        isAuthenticated = authState.User.Identity?.IsAuthenticated ?? false;

        if (isAuthenticated)
        {
            currentUserId = authState.User.FindFirst("sub")?.Value;
            await LoadConversations();
            await InitializeSignalR();
        }
    }

    private async Task InitializeSignalR()
    {
        try
        {
            hubConnection = new HubConnectionBuilder()
                .WithUrl("http://localhost:5000/hubs/chat")
                .WithAutomaticReconnect()
                .Build();

            // Écouter les nouveaux messages
            hubConnection.On<int, string, string, string>("ReceiveMessage", async (conversationId, senderId, senderName, content) =>
            {
                await InvokeAsync(async () =>
                {
                    // Si c'est la conversation active, ajouter le message
                    if (selectedConversation != null && selectedConversation.Id == conversationId)
                    {
                        await LoadMessages(conversationId);
                    }
                    else
                    {
                        // Sinon, incrémenter le compteur de non lus
                        var conv = conversations.FirstOrDefault(c => c.Id == conversationId);
                        if (conv != null)
                        {
                            conv.UnreadCount++;
                            unreadCount = conversations.Sum(c => c.UnreadCount);
                        }
                    }

                    // Jouer un son de notification
                    await PlayNotificationSound();

                    StateHasChanged();
                });
            });

            // Écouter les changements de statut de conversation
            hubConnection.On<int, string>("ConversationStatusChanged", async (conversationId, newStatus) =>
            {
                await InvokeAsync(async () =>
                {
                    var conv = conversations.FirstOrDefault(c => c.Id == conversationId);
                    if (conv != null)
                    {
                        conv.Status = newStatus;
                    }

                    if (selectedConversation != null && selectedConversation.Id == conversationId)
                    {
                        selectedConversation.Status = newStatus;
                    }

                    StateHasChanged();
                });
            });

            // Écouter les utilisateurs qui se connectent
            hubConnection.On<string>("UserOnline", async (userId) =>
            {
                await InvokeAsync(() =>
                {
                    onlineUsers[userId] = true;
                    Console.WriteLine($"✅ Utilisateur {userId} est maintenant en ligne");
                    StateHasChanged();
                });
            });

            // Écouter les utilisateurs qui se déconnectent
            hubConnection.On<string>("UserOffline", async (userId) =>
            {
                await InvokeAsync(() =>
                {
                    onlineUsers[userId] = false;
                    Console.WriteLine($"❌ Utilisateur {userId} est maintenant hors ligne");
                    StateHasChanged();
                });
            });

            // Écouter les vendeurs qui se connectent (par SellerId)
            hubConnection.On<string>("SellerOnline", async (sellerId) =>
            {
                await InvokeAsync(() =>
                {
                    onlineUsers[sellerId] = true;
                    Console.WriteLine($"✅ Vendeur {sellerId} est maintenant en ligne");
                    StateHasChanged();
                });
            });

            // Écouter les vendeurs qui se déconnectent (par SellerId)
            hubConnection.On<string>("SellerOffline", async (sellerId) =>
            {
                await InvokeAsync(() =>
                {
                    onlineUsers[sellerId] = false;
                    Console.WriteLine($"❌ Vendeur {sellerId} est maintenant hors ligne");
                    StateHasChanged();
                });
            });

            await hubConnection.StartAsync();
            Console.WriteLine("✅ SignalR connecté au hub de chat");

            // Charger le statut en ligne initial de tous les vendeurs
            await LoadOnlineStatus();

            // Rejoindre le groupe de l'utilisateur
            if (!string.IsNullOrEmpty(currentUserId))
            {
                await hubConnection.InvokeAsync("JoinUserGroup", currentUserId);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Erreur lors de la connexion SignalR: {ex.Message}");
        }
    }

    private async Task PlayNotificationSound()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("playNotificationSound");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la lecture du son: {ex.Message}");
        }
    }

    private void ToggleChat()
    {
        isOpen = !isOpen;
        if (isOpen && conversations.Count == 0)
        {
            _ = LoadConversations();
        }
    }

    private async Task LoadConversations()
    {
        try
        {
            isLoading = true;
            conversations = await ChatService.GetCustomerConversationsAsync();
            unreadCount = conversations.Sum(c => c.UnreadCount);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des conversations: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task SelectConversation(ConversationDto conversation)
    {
        selectedConversation = conversation;
        await LoadMessages(conversation.Id);

        // Marquer comme lu
        if (conversation.UnreadCount > 0)
        {
            await ChatService.MarkMessagesAsReadAsync(conversation.Id);
            conversation.UnreadCount = 0;
            unreadCount = conversations.Sum(c => c.UnreadCount);
        }
    }

    private async Task LoadMessages(int conversationId)
    {
        try
        {
            isLoading = true;
            messages = await ChatService.GetMessagesAsync(conversationId);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des messages: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private void BackToConversations()
    {
        selectedConversation = null;
        messages.Clear();
        _ = LoadConversations();
    }

    private async Task SendMessage()
    {
        if (string.IsNullOrWhiteSpace(newMessage) || selectedConversation == null)
            return;

        try
        {
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            var userName = authState.User.Identity?.Name ?? "Client";

            var messageDto = new SendMessageDto
            {
                ConversationId = selectedConversation.Id,
                SenderId = authState.User.FindFirst("sub")?.Value ?? "",
                SenderName = userName,
                SenderType = "Customer",
                Content = newMessage,
                MessageType = "Text"
            };

            var success = await ChatService.SendMessageAsync(messageDto);

            if (success)
            {
                newMessage = string.Empty;

                // Envoyer via SignalR si connecté
                if (hubConnection?.State == HubConnectionState.Connected)
                {
                    await hubConnection.InvokeAsync("SendMessageToConversation",
                        selectedConversation.Id,
                        messageDto.SenderId,
                        messageDto.SenderName,
                        messageDto.Content);
                }

                await LoadMessages(selectedConversation.Id);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'envoi du message: {ex.Message}");
        }
    }

    private async Task HandleKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter" && !string.IsNullOrWhiteSpace(newMessage))
        {
            await SendMessage();
        }
    }

    private bool IsSellerOnline(string? sellerId)
    {
        if (string.IsNullOrEmpty(sellerId))
            return false;

        return onlineUsers.GetValueOrDefault(sellerId, false);
    }

    private async Task LoadOnlineStatus()
    {
        try
        {
            // Charger le statut en ligne de tous les vendeurs dans les conversations
            foreach (var conv in conversations)
            {
                if (!string.IsNullOrEmpty(conv.SellerId))
                {
                    Console.WriteLine($"🔍 Vérification du statut en ligne pour SellerId: {conv.SellerId}");

                    // Vérifier via SignalR Hub si le vendeur est en ligne
                    if (hubConnection?.State == HubConnectionState.Connected)
                    {
                        // Utiliser IsSellerOnline au lieu de IsUserOnline
                        var isOnline = await hubConnection.InvokeAsync<bool>("IsSellerOnline", conv.SellerId);
                        onlineUsers[conv.SellerId] = isOnline;
                        Console.WriteLine($"📊 SellerId {conv.SellerId} est {(isOnline ? "EN LIGNE ✅" : "HORS LIGNE ❌")}");
                    }
                    else
                    {
                        Console.WriteLine($"❌ Hub SignalR non connecté");
                    }
                }
            }
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement du statut en ligne: {ex.Message}");
        }
    }

    private string FormatDate(DateTime date)
    {
        var now = DateTime.Now;
        var diff = now - date;

        if (diff.TotalMinutes < 1)
            return "À l'instant";
        if (diff.TotalMinutes < 60)
            return $"Il y a {(int)diff.TotalMinutes} min";
        if (diff.TotalHours < 24)
            return $"Il y a {(int)diff.TotalHours}h";
        if (diff.TotalDays < 7)
            return $"Il y a {(int)diff.TotalDays}j";
        
        return date.ToString("dd/MM/yyyy");
    }

    public async ValueTask DisposeAsync()
    {
        refreshTimer?.Dispose();

        if (hubConnection != null)
        {
            await hubConnection.DisposeAsync();
        }
    }
}

<style>
    .floating-chat-button {
        position: fixed !important;
        bottom: 20px !important;
        right: 20px !important;
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #003366 0%, #0055aa 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(0, 51, 102, 0.3);
        transition: all 0.3s ease;
        z-index: 9999 !important;
        /* Annuler toute transformation héritée */
        transform: none !important;
        -webkit-transform: none !important;
    }

    .floating-chat-button:hover {
        transform: scale(1.1) !important;
        -webkit-transform: scale(1.1) !important;
        box-shadow: 0 6px 20px rgba(0, 51, 102, 0.4);
    }

    .floating-chat-button i {
        color: white;
        font-size: 24px;
    }

    .unread-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background: #E73C30;
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
    }

    .floating-chat-window {
        position: fixed !important;
        bottom: 90px !important;
        right: 20px !important;
        width: 380px;
        max-height: calc(100vh - 120px);
        height: 550px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        display: flex;
        flex-direction: column;
        z-index: 9999 !important;
        overflow: hidden;
        /* Annuler toute transformation héritée */
        transform: none !important;
        -webkit-transform: none !important;
    }

    .chat-header {
        background: linear-gradient(135deg, #003366 0%, #0055aa 100%);
        color: white;
        padding: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .chat-header-content {
        flex: 1;
    }

    .header-with-status {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .chat-header h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
    }

    .connection-status {
        display: flex;
        align-items: center;
        font-size: 10px;
        animation: pulse 2s infinite;
    }

    .connection-status.connected {
        color: #4CAF50;
    }

    .connection-status.disconnected {
        color: #f44336;
    }

    @@keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.5;
        }
    }

    .chat-header-info {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .btn-back {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn-close {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn-close:hover, .btn-back:hover {
        background: rgba(255, 255, 255, 0.3);
    }

    .status-badge {
        font-size: 11px;
        padding: 2px 8px;
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.2);
    }

    .chat-body {
        flex: 1;
        overflow-y: auto;
        background: #f8f9fa;
    }

    .loading-spinner {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #666;
    }

    .loading-spinner i {
        font-size: 32px;
        margin-bottom: 12px;
    }

    .conversations-list {
        padding: 8px;
    }

    .conversation-item {
        display: flex;
        gap: 12px;
        padding: 12px;
        background: white;
        border-radius: 8px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.2s;
    }

    .conversation-item:hover {
        background: #f0f7ff;
        transform: translateX(4px);
    }

    .conversation-item.unread {
        background: #e3f2fd;
        border-left: 3px solid #003366;
    }

    .conversation-avatar {
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, #003366 0%, #0055aa 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 20px;
        flex-shrink: 0;
        position: relative;
    }

    .online-indicator {
        position: absolute;
        bottom: 2px;
        right: 2px;
        width: 12px;
        height: 12px;
        background: #4CAF50;
        border: 2px solid white;
        border-radius: 50%;
        animation: pulse-online 2s infinite;
    }

    @@keyframes pulse-online {
        0%, 100% {
            box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
        }
        50% {
            box-shadow: 0 0 0 4px rgba(76, 175, 80, 0);
        }
    }

    .conversation-info {
        flex: 1;
        min-width: 0;
    }

    .conversation-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
    }

    .seller-name-with-status {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .conversation-header h4 {
        margin: 0;
        font-size: 14px;
        font-weight: 600;
        color: #333;
    }

    .status-badge {
        font-size: 10px;
        padding: 2px 8px;
        border-radius: 12px;
        font-weight: 500;
    }

    .status-badge.online {
        background: #E8F5E9;
        color: #2E7D32;
    }

    .status-badge.offline {
        background: #FAFAFA;
        color: #9E9E9E;
    }

    .conversation-time {
        font-size: 11px;
        color: #999;
    }

    .conversation-preview {
        margin: 0;
        font-size: 13px;
        color: #666;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .unread-count {
        display: inline-block;
        background: #E73C30;
        color: white;
        font-size: 11px;
        padding: 2px 8px;
        border-radius: 12px;
        margin-top: 4px;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #999;
    }

    .empty-state i {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.5;
    }

    .messages-container {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .message {
        display: flex;
        max-width: 75%;
    }

    .message-customer {
        align-self: flex-end;
    }

    .message-seller {
        align-self: flex-start;
    }

    .message-content {
        padding: 10px 14px;
        border-radius: 12px;
        word-wrap: break-word;
    }

    .message-customer .message-content {
        background: #003366;
        color: white;
        border-bottom-right-radius: 4px;
    }

    .message-seller .message-content {
        background: white;
        color: #333;
        border-bottom-left-radius: 4px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .message-content p {
        margin: 0 0 4px 0;
        font-size: 14px;
        line-height: 1.4;
    }

    .message-time {
        font-size: 11px;
        opacity: 0.7;
    }

    .empty-messages {
        text-align: center;
        padding: 60px 20px;
        color: #999;
    }

    .empty-messages i {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.5;
    }

    .chat-input-area {
        display: flex;
        gap: 8px;
        padding: 12px;
        background: white;
        border-top: 1px solid #e0e0e0;
    }

    .chat-input {
        flex: 1;
        padding: 10px 14px;
        border: 1px solid #ddd;
        border-radius: 20px;
        font-size: 14px;
        outline: none;
    }

    .chat-input:focus {
        border-color: #003366;
    }

    .btn-send {
        width: 40px;
        height: 40px;
        background: #003366;
        color: white;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s;
    }

    .btn-send:hover:not(:disabled) {
        background: #0055aa;
        transform: scale(1.05);
    }

    .btn-send:disabled {
        background: #ccc;
        cursor: not-allowed;
    }

    @@media (max-width: 1024px) {
        .floating-chat-window {
            width: 350px;
            max-height: calc(100vh - 100px);
            height: 500px;
        }
    }

    @@media (max-width: 768px) {
        .floating-chat-window {
            width: calc(100% - 20px);
            max-height: calc(100vh - 100px);
            height: calc(100vh - 100px);
            bottom: 80px;
            right: 10px;
            left: 10px;
        }

        .floating-chat-button {
            bottom: 16px;
            right: 16px;
        }
    }

    @@media (max-width: 480px) {
        .floating-chat-window {
            width: 100%;
            height: 100%;
            max-height: 100vh;
            bottom: 0;
            right: 0;
            left: 0;
            border-radius: 0;
        }
    }
</style>

