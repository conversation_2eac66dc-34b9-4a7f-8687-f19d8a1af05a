/* Hero Section Immersive - Design Révolutionnaire */
.hero-immersive {
    position: relative;
    min-height: 100vh;
    overflow: hidden;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #003366 0%, #004488 50%, #0066AA 100%);
}

/* Video Background */
.hero-video-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.3;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(0, 51, 102, 0.9) 0%,
        rgba(0, 68, 136, 0.8) 50%,
        rgba(0, 102, 170, 0.7) 100%
    );
    z-index: 2;
}

/* Hero Content */
.hero-content {
    position: relative;
    z-index: 3;
    color: white;
}

.min-vh-75 {
    min-height: 75vh;
}

/* Hero Badge */
.hero-badge {
    display: inline-flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    padding: 8px 20px;
    font-size: 0.9rem;
    font-weight: 600;
    animation: fadeInUp 0.8s ease-out;
}

/* Hero Title */
.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 2rem;
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.hero-title-main {
    display: block;
    color: white;
}

.hero-title-accent {
    display: block;
    background: linear-gradient(135deg, #F96302, #FFD700);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.hero-title-accent::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(135deg, #F96302, #FFD700);
    border-radius: 2px;
    animation: expandWidth 1s ease-out 1s both;
}

/* Hero Description */
.hero-description {
    font-size: 1.3rem;
    line-height: 1.6;
    opacity: 0.95;
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

/* Hero Stats */
.hero-stats {
    display: flex;
    gap: 2rem;
    animation: fadeInUp 0.8s ease-out 0.6s both;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #FFD700;
    line-height: 1;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 5px;
}

/* Hero Actions */
.hero-actions {
    animation: fadeInUp 0.8s ease-out 0.8s both;
}

.btn-hero-primary {
    background: linear-gradient(135deg, #F96302, #E73C30);
    border: none;
    color: white;
    padding: 15px 30px;
    font-weight: 700;
    border-radius: 50px;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(249, 99, 2, 0.4);
}

.btn-hero-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(249, 99, 2, 0.6);
    background: linear-gradient(135deg, #E73C30, #F96302);
}

.btn-hero-secondary {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 13px 28px;
    font-weight: 600;
    border-radius: 50px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.btn-hero-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-3px);
    color: white;
}

/* Trust Indicators */
.hero-trust-indicators {
    display: flex;
    gap: 2rem;
    animation: fadeInUp 0.8s ease-out 1s both;
}

.trust-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    opacity: 0.9;
}

.trust-item i {
    font-size: 1.2rem;
}

/* Visual Section */
.hero-visual-section {
    position: relative;
    animation: fadeInRight 1s ease-out 0.5s both;
}

.hero-product-showcase {
    position: relative;
    height: 500px;
}

/* Floating Product Cards */
.floating-product-card {
    position: absolute;
    background: white;
    border-radius: 15px;
    padding: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    animation: float 6s ease-in-out infinite;
}

.floating-product-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
}

.card-1 {
    top: 50px;
    left: 50px;
    width: 180px;
    animation-delay: 0s;
}

.card-2 {
    top: 200px;
    right: 80px;
    width: 160px;
    animation-delay: 2s;
}

.card-3 {
    bottom: 80px;
    left: 20px;
    width: 170px;
    animation-delay: 4s;
}

.floating-product-card img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 10px;
}

.product-info h6 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.product-info .price {
    color: #F96302;
    font-weight: 700;
    font-size: 1rem;
}

/* Africa Map Animation */
.hero-main-visual {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: -1;
}

.africa-map-container {
    width: 300px;
    height: 300px;
    opacity: 0.1;
}

.africa-map {
    width: 100%;
    height: 100%;
}

.country-path {
    fill: none;
    stroke: #FFD700;
    stroke-width: 2;
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    animation: drawPath 3s ease-out 2s forwards;
}

.city-point {
    fill: #F96302;
    animation: pulse 2s ease-in-out infinite;
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: white;
    opacity: 0.8;
    animation: bounce 2s ease-in-out infinite;
}

.scroll-arrow {
    font-size: 1.5rem;
    margin-bottom: 5px;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes expandWidth {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes drawPath {
    to {
        stroke-dashoffset: 0;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.2);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Responsive Design */
@media (max-width: 992px) {
    .hero-title {
        font-size: 2.8rem;
    }
    
    .hero-stats {
        gap: 1.5rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .hero-product-showcase {
        height: 400px;
    }
    
    .floating-product-card {
        width: 140px !important;
    }
    
    .floating-product-card img {
        height: 100px;
    }
}

@media (max-width: 768px) {
    .hero-immersive {
        min-height: 90vh;
    }
    
    .hero-title {
        font-size: 2.2rem;
    }
    
    .hero-description {
        font-size: 1.1rem;
    }
    
    .hero-stats {
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .hero-trust-indicators {
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .hero-actions {
        text-align: center;
    }
    
    .btn-hero-primary,
    .btn-hero-secondary {
        width: 100%;
        margin-bottom: 1rem;
    }
    
    .hero-visual-section {
        margin-top: 2rem;
    }
    
    .hero-product-showcase {
        height: 300px;
    }
    
    .floating-product-card {
        width: 120px !important;
    }
}
