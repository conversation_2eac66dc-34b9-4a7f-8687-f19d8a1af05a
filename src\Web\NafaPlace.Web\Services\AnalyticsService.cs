using NafaPlace.Web.Models.Analytics;
using System.Text.Json;

namespace NafaPlace.Web.Services;

public interface IAnalyticsService
{
    Task<ComprehensiveDashboardDto?> GetComprehensiveDashboardAsync(DateTime fromDate, DateTime toDate);
    Task<List<SalesPredictionDto>> GetSalesPredictionsAsync(int days);
    Task<List<string>> GetCustomerInsightsAsync();
    Task<CustomerChurnAnalysisDto> GetCustomerChurnAnalysisAsync();
}

public class AnalyticsService : IAnalyticsService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<AnalyticsService> _logger;
    private readonly string _apiBaseUrl;

    public AnalyticsService(HttpClient httpClient, ILogger<AnalyticsService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        _apiBaseUrl = httpClient.BaseAddress?.ToString().TrimEnd('/') ?? "http://localhost:5011";
    }

    public async Task<ComprehensiveDashboardDto?> GetComprehensiveDashboardAsync(DateTime fromDate, DateTime toDate)
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_apiBaseUrl}/api/analytics/dashboard?fromDate={fromDate:yyyy-MM-dd}&toDate={toDate:yyyy-MM-dd}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<ComprehensiveDashboardDto>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
            else
            {
                _logger.LogWarning("Échec de récupération des données du dashboard: {StatusCode}", response.StatusCode);
                return GetFallbackDashboardData();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des données du dashboard");
            return GetFallbackDashboardData();
        }
    }

    public async Task<List<SalesPredictionDto>> GetSalesPredictionsAsync(int days)
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_apiBaseUrl}/api/analytics/predictions?days={days}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<SalesPredictionDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }) ?? new List<SalesPredictionDto>();
            }
            else
            {
                _logger.LogWarning("Échec de récupération des prédictions: {StatusCode}", response.StatusCode);
                return GetFallbackPredictions(days);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des prédictions");
            return GetFallbackPredictions(days);
        }
    }

    public async Task<List<string>> GetCustomerInsightsAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_apiBaseUrl}/api/analytics/customer-insights");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<string>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }) ?? new List<string>();
            }
            else
            {
                _logger.LogWarning("Échec de récupération des insights clients: {StatusCode}", response.StatusCode);
                return new List<string>();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des insights clients");
            return new List<string>();
        }
    }

    private ComprehensiveDashboardDto GetFallbackDashboardData()
    {
        return new ComprehensiveDashboardDto
        {
            TotalRevenue = 0,
            TotalOrders = 0,
            ConversionRate = 0,
            AverageOrderValue = 0,
            CustomerRetentionRate = 0
        };
    }

    private List<SalesPredictionDto> GetFallbackPredictions(int days)
    {
        var predictions = new List<SalesPredictionDto>();
        var startDate = DateTime.Today;

        for (int i = 0; i < days; i++)
        {
            predictions.Add(new SalesPredictionDto
            {
                PredictedRevenue = 0,
                Confidence = "0"
            });
        }

        return predictions;
    }

    public async Task<CustomerChurnAnalysisDto> GetCustomerChurnAnalysisAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_apiBaseUrl}/api/analytics/customer-churn");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<CustomerChurnAnalysisDto>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }) ?? new CustomerChurnAnalysisDto();
            }
            else
            {
                _logger.LogWarning("Échec de récupération de l'analyse de churn: {StatusCode}", response.StatusCode);
                return new CustomerChurnAnalysisDto();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de l'analyse de churn");
            return new CustomerChurnAnalysisDto();
        }
    }
}

// Les DTOs sont définis dans NafaPlace.Web.Models.Analytics
