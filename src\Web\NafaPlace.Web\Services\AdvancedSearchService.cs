using NafaPlace.Web.Models.Catalog;
using System.Net.Http.Json;
using System.Text.Json;

namespace NafaPlace.Web.Services;

public class AdvancedSearchService : IAdvancedSearchService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<AdvancedSearchService> _logger;

    public AdvancedSearchService(HttpClient httpClient, ILogger<AdvancedSearchService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<ProductSearchResult> SearchProductsAdvancedAsync(ProductSearchRequest request)
    {
        try
        {
            var searchDto = MapToSearchDto(request);
            var response = await _httpClient.PostAsJsonAsync("/api/v1/search/advanced", searchDto);
            
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ProductSearchResult>();
                return result ?? new ProductSearchResult();
            }
            
            _logger.LogWarning("Échec de la recherche avancée: {StatusCode}", response.StatusCode);
            return new ProductSearchResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche avancée");
            return new ProductSearchResult();
        }
    }

    public async Task<List<SearchSuggestion>> GetSearchSuggestionsAsync(string query, int maxSuggestions = 10)
    {
        try
        {
            if (string.IsNullOrEmpty(query) || query.Length < 2)
                return new List<SearchSuggestion>();

            // Utiliser l'endpoint de recherche de produits pour obtenir des suggestions réelles
            var searchRequest = new
            {
                Query = query,
                Page = 1,
                PageSize = maxSuggestions,
                Status = "approved"
            };

            var response = await _httpClient.PostAsJsonAsync("/api/catalog/products/search", searchRequest);

            if (response.IsSuccessStatusCode)
            {
                var searchResponse = await response.Content.ReadFromJsonAsync<ProductSearchResponse>();
                if (searchResponse?.Products != null)
                {
                    var suggestions = new List<SearchSuggestion>();

                    // Créer des suggestions à partir des produits trouvés
                    foreach (var product in searchResponse.Products.Take(maxSuggestions))
                    {
                        suggestions.Add(new SearchSuggestion
                        {
                            Text = product.Name,
                            Type = "product",
                            Metadata = new Dictionary<string, string>
                            {
                                { "productId", product.Id.ToString() },
                                { "price", product.Price.ToString("N0") + " GNF" }
                            }
                        });
                    }

                    return suggestions;
                }
            }

            return new List<SearchSuggestion>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des suggestions");
            return new List<SearchSuggestion>();
        }
    }

    public async Task<List<string>> GetAutocompleteSuggestionsAsync(string query, int maxSuggestions = 5)
    {
        try
        {
            if (string.IsNullOrEmpty(query) || query.Length < 2)
                return new List<string>();

            var response = await _httpClient.GetFromJsonAsync<List<string>>(
                $"/api/v1/search/autocomplete?query={Uri.EscapeDataString(query)}&maxSuggestions={maxSuggestions}");
            
            return response ?? new List<string>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'autocomplétion");
            return new List<string>();
        }
    }

    public async Task<List<ProductDto>> FindSimilarProductsAsync(int productId, int maxResults = 10)
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<List<ProductDto>>(
                $"/api/v1/search/similar/{productId}?maxResults={maxResults}");
            
            return response ?? new List<ProductDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche de produits similaires");
            return new List<ProductDto>();
        }
    }

    public async Task<List<ProductDto>> SearchByImageAsync(string imageUrl, int maxResults = 10)
    {
        try
        {
            var request = new ImageSearchRequest
            {
                ImageUrl = imageUrl,
                MaxResults = maxResults
            };

            var response = await _httpClient.PostAsJsonAsync("/api/v1/search/by-image", request);
            
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<List<ProductDto>>();
                return result ?? new List<ProductDto>();
            }
            
            return new List<ProductDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche par image");
            return new List<ProductDto>();
        }
    }

    public async Task<List<SearchFacet>> GetSearchFacetsAsync(ProductSearchRequest request)
    {
        try
        {
            var queryParams = BuildQueryParams(request);
            var response = await _httpClient.GetFromJsonAsync<List<SearchFacet>>(
                $"/api/v1/search/facets?{queryParams}");
            
            return response ?? new List<SearchFacet>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des facettes");
            return new List<SearchFacet>();
        }
    }

    public async Task<Dictionary<string, List<string>>> GetAvailableFiltersAsync()
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<Dictionary<string, List<string>>>(
                "/api/v1/search/filters");
            
            return response ?? new Dictionary<string, List<string>>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des filtres");
            return new Dictionary<string, List<string>>();
        }
    }

    public async Task<List<string>> GetPopularSearchesAsync(int count = 10)
    {
        try
        {
            // Récupérer les produits les plus récents pour générer des recherches populaires
            var searchRequest = new
            {
                Page = 1,
                PageSize = count * 2,
                Status = "approved",
                SortBy = "createdat",
                SortDescending = true
            };

            var response = await _httpClient.PostAsJsonAsync("/api/catalog/products/search", searchRequest);

            if (response.IsSuccessStatusCode)
            {
                var searchResponse = await response.Content.ReadFromJsonAsync<ProductSearchResponse>();
                if (searchResponse?.Products != null && searchResponse.Products.Any())
                {
                    // Extraire des mots-clés des noms de produits
                    var keywords = new HashSet<string>();
                    foreach (var product in searchResponse.Products)
                    {
                        var words = product.Name.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                        foreach (var word in words.Take(2)) // Prendre les 2 premiers mots
                        {
                            if (word.Length > 3) // Ignorer les mots trop courts
                            {
                                keywords.Add(word);
                            }
                        }

                        if (keywords.Count >= count)
                            break;
                    }

                    return keywords.Take(count).ToList();
                }
            }

            // Fallback sur des données statiques
            return new List<string> { "smartphone", "ordinateur", "vêtements", "chaussures", "livre" }.Take(count).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des recherches populaires");
            return new List<string> { "smartphone", "ordinateur", "vêtements", "chaussures", "livre" }.Take(count).ToList();
        }
    }

    public async Task<List<string>> GetUserSearchHistoryAsync(int count = 10)
    {
        // TODO: Implémenter l'endpoint backend pour l'historique de recherche
        // Pour l'instant, retourner une liste vide pour éviter les erreurs HTTP
        await Task.CompletedTask;
        return new List<string>();
    }

    public async Task<List<string>> GetSearchTrendsAsync(int days = 30)
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<List<string>>(
                $"/api/v1/search/trends?days={days}");
            
            return response ?? new List<string>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des tendances");
            return new List<string>();
        }
    }

    private object MapToSearchDto(ProductSearchRequest request)
    {
        return new
        {
            searchTerm = request.SearchTerm,
            categoryId = request.CategoryId,
            categoryIds = request.CategoryIds,
            minPrice = request.MinPrice,
            maxPrice = request.MaxPrice,
            brand = request.Brand,
            brands = request.Brands,
            inStockOnly = request.InStockOnly,
            sellerId = request.SellerId,
            sellerIds = request.SellerIds,
            sortBy = request.SortBy,
            sortDescending = request.SortDescending,
            page = request.Page,
            pageSize = request.PageSize,
            minRating = request.MinRating,
            maxRating = request.MaxRating,
            createdAfter = request.CreatedAfter,
            createdBefore = request.CreatedBefore,
            tags = request.Tags,
            attributes = request.Attributes,
            color = request.Color,
            size = request.Size,
            material = request.Material,
            hasDiscount = request.HasDiscount,
            minDiscountPercentage = request.MinDiscountPercentage,
            isNewArrival = request.IsNewArrival,
            hasFreeShipping = request.HasFreeShipping,
            useFullTextSearch = request.UseFullTextSearch,
            includeSimilarProducts = request.IncludeSimilarProducts,
            searchMode = request.SearchMode,
            maxSuggestions = request.MaxSuggestions
        };
    }

    private string BuildQueryParams(ProductSearchRequest request)
    {
        var queryParams = new List<string>();

        if (!string.IsNullOrEmpty(request.SearchTerm))
            queryParams.Add($"searchTerm={Uri.EscapeDataString(request.SearchTerm)}");
        
        if (request.CategoryId.HasValue)
            queryParams.Add($"categoryId={request.CategoryId}");
        
        if (request.CategoryIds?.Any() == true)
        {
            foreach (var id in request.CategoryIds)
                queryParams.Add($"categoryIds={id}");
        }

        if (request.MinPrice.HasValue)
            queryParams.Add($"minPrice={request.MinPrice}");
        
        if (request.MaxPrice.HasValue)
            queryParams.Add($"maxPrice={request.MaxPrice}");

        if (!string.IsNullOrEmpty(request.Brand))
            queryParams.Add($"brand={Uri.EscapeDataString(request.Brand)}");

        if (request.InStockOnly)
            queryParams.Add("inStockOnly=true");

        queryParams.Add($"page={request.Page}");
        queryParams.Add($"pageSize={request.PageSize}");

        return string.Join("&", queryParams);
    }
}
