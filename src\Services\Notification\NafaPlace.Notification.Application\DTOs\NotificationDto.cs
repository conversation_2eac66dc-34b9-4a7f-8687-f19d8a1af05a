using NafaPlace.Notification.Domain.Enums;

namespace NafaPlace.Notification.Application.DTOs;

public class NotificationDto
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public NotificationType Type { get; set; }
    public NotificationPriority Priority { get; set; }
    public string RecipientId { get; set; } = string.Empty;
    public string RecipientType { get; set; } = string.Empty; // User, Se<PERSON>, Admin
    public List<NotificationChannel> Channels { get; set; } = new();
    public Dictionary<string, object> Data { get; set; } = new();
    public string? ActionUrl { get; set; }
    public string? ImageUrl { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ScheduledAt { get; set; }
    public DateTime? SentAt { get; set; }
    public DateTime? ReadAt { get; set; }
    public bool IsRead { get; set; }
    public bool IsSent { get; set; }
    public NotificationStatus Status { get; set; }
    public string? ErrorMessage { get; set; }
    public int RetryCount { get; set; }
    public DateTime? ExpiresAt { get; set; }
}

public class CreateNotificationDto
{
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public NotificationType Type { get; set; }
    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;
    public string RecipientId { get; set; } = string.Empty;
    public string RecipientType { get; set; } = string.Empty;
    public List<NotificationChannel> Channels { get; set; } = new();
    public Dictionary<string, object> Data { get; set; } = new();
    public string? ActionUrl { get; set; }
    public string? ImageUrl { get; set; }
    public DateTime? ScheduledAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public string? TemplateId { get; set; }
    public Dictionary<string, object> TemplateData { get; set; } = new();
}

public class BulkNotificationDto
{
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public NotificationType Type { get; set; }
    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;
    public List<string> RecipientIds { get; set; } = new();
    public string RecipientType { get; set; } = string.Empty;
    public List<NotificationChannel> Channels { get; set; } = new();
    public Dictionary<string, object> Data { get; set; } = new();
    public string? ActionUrl { get; set; }
    public string? ImageUrl { get; set; }
    public DateTime? ScheduledAt { get; set; }
    public string? TemplateId { get; set; }
    public Dictionary<string, object> TemplateData { get; set; } = new();
}

public class NotificationTemplateDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public NotificationType Type { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string HtmlContent { get; set; } = string.Empty;
    public string TextContent { get; set; } = string.Empty;
    public string PushTitle { get; set; } = string.Empty;
    public string PushBody { get; set; } = string.Empty;
    public List<string> Variables { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public string Language { get; set; } = "fr";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class NotificationPreferenceDto
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public NotificationType Type { get; set; }
    public List<NotificationChannel> EnabledChannels { get; set; } = new();
    public bool IsEnabled { get; set; } = true;
    public Dictionary<string, object> Settings { get; set; } = new();
    public DateTime UpdatedAt { get; set; }
}

public class NotificationStatsDto
{
    public int TotalSent { get; set; }
    public int TotalDelivered { get; set; }
    public int TotalRead { get; set; }
    public int TotalFailed { get; set; }
    public double DeliveryRate { get; set; }
    public double ReadRate { get; set; }
    public Dictionary<NotificationChannel, int> ByChannel { get; set; } = new();
    public Dictionary<NotificationType, int> ByType { get; set; } = new();
    public Dictionary<string, int> ByDay { get; set; } = new();
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
}

public class RealTimeNotificationDto
{
    public string Id { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public Dictionary<string, object> Data { get; set; } = new();
    public string? ActionUrl { get; set; }
    public string? ImageUrl { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public bool RequiresAction { get; set; }
    public int? AutoHideAfterMs { get; set; }
}

public class PushSubscriptionDto
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string Endpoint { get; set; } = string.Empty;
    public string P256dh { get; set; } = string.Empty;
    public string Auth { get; set; } = string.Empty;
    public string UserAgent { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime? LastUsed { get; set; }
}

public class EmailNotificationDto
{
    public string To { get; set; } = string.Empty;
    public List<string>? Cc { get; set; }
    public List<string>? Bcc { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string HtmlBody { get; set; } = string.Empty;
    public string? TextBody { get; set; }
    public List<EmailAttachmentDto>? Attachments { get; set; }
    public Dictionary<string, string> Headers { get; set; } = new();
    public string? TemplateId { get; set; }
    public Dictionary<string, object> TemplateData { get; set; } = new();
    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;
    public DateTime? ScheduledAt { get; set; }
}

public class EmailAttachmentDto
{
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public byte[] Content { get; set; } = Array.Empty<byte>();
    public string? ContentId { get; set; }
}

public class SmsNotificationDto
{
    public string To { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? From { get; set; }
    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;
    public DateTime? ScheduledAt { get; set; }
}

public class NotificationFilterDto
{
    public string? RecipientId { get; set; }
    public string? RecipientType { get; set; }
    public NotificationType? Type { get; set; }
    public NotificationStatus? Status { get; set; }
    public NotificationChannel? Channel { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public bool? IsRead { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public string? SortBy { get; set; } = "CreatedAt";
    public bool SortDescending { get; set; } = true;
}

public class NotificationDeliveryDto
{
    public int NotificationId { get; set; }
    public NotificationChannel Channel { get; set; }
    public NotificationStatus Status { get; set; }
    public DateTime AttemptedAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ExternalId { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public enum NotificationType
{
    OrderCreated,
    OrderUpdated,
    OrderCancelled,
    OrderDelivered,
    PaymentReceived,
    PaymentFailed,
    ProductLowStock,
    ProductOutOfStock,
    ProductApproved,
    ProductRejected,
    NewReview,
    NewMessage,
    SystemAlert,
    Marketing,
    Welcome,
    PasswordReset,
    EmailVerification,
    AccountSuspended,
    PromotionAlert,
    NewsletterUpdate
}

public enum NotificationPriority
{
    Low,
    Normal,
    High,
    Critical
}

public enum NotificationChannel
{
    InApp,
    Email,
    SMS,
    Push,
    WebSocket
}

public enum NotificationStatus
{
    Pending,
    Sent,
    Delivered,
    Read,
    Failed,
    Expired,
    Cancelled
}
