using NafaPlace.Notification.Application.DTOs;

namespace NafaPlace.Notification.Application.Services;

public interface IPushNotificationService
{
    // Envoi de notifications push
    Task<bool> SendToUserAsync(string userId, string title, string body, Dictionary<string, object>? data = null);
    Task<bool> SendToDeviceAsync(string deviceToken, string title, string body, Dictionary<string, object>? data = null);
    Task<int> SendToMultipleUsersAsync(List<string> userIds, string title, string body, Dictionary<string, object>? data = null);
    Task<int> SendToMultipleDevicesAsync(List<string> deviceTokens, string title, string body, Dictionary<string, object>? data = null);
    Task<bool> SendToTopicAsync(string topic, string title, string body, Dictionary<string, object>? data = null);
    
    // Gestion des abonnements
    Task<bool> SubscribeUserAsync(string userId, PushSubscriptionDto subscription);
    Task<bool> UnsubscribeUserAsync(string userId, string endpoint);
    Task<bool> UpdateSubscriptionAsync(string userId, PushSubscriptionDto subscription);
    Task<List<PushSubscriptionDto>> GetUserSubscriptionsAsync(string userId);
    Task<bool> ValidateSubscriptionAsync(PushSubscriptionDto subscription);
    
    // Gestion des topics
    Task<bool> SubscribeToTopicAsync(string userId, string topic);
    Task<bool> UnsubscribeFromTopicAsync(string userId, string topic);
    Task<List<string>> GetUserTopicsAsync(string userId);
    Task<int> GetTopicSubscriberCountAsync(string topic);
    
    // Notifications Web Push
    Task<bool> SendWebPushAsync(PushSubscriptionDto subscription, string title, string body, Dictionary<string, object>? data = null);
    Task<int> SendWebPushToMultipleAsync(List<PushSubscriptionDto> subscriptions, string title, string body, Dictionary<string, object>? data = null);
    Task<bool> TestWebPushAsync(PushSubscriptionDto subscription);
    
    // Notifications FCM (Firebase Cloud Messaging)
    Task<bool> SendFcmAsync(string deviceToken, string title, string body, Dictionary<string, object>? data = null);
    Task<int> SendFcmToMultipleAsync(List<string> deviceTokens, string title, string body, Dictionary<string, object>? data = null);
    Task<bool> SendFcmToTopicAsync(string topic, string title, string body, Dictionary<string, object>? data = null);
    
    // Gestion des templates
    Task<bool> SendTemplatedPushAsync(string userId, string templateId, Dictionary<string, object> templateData);
    Task<bool> SendTemplatedPushToMultipleAsync(List<string> userIds, string templateId, Dictionary<string, object> templateData);
    
    // Analytics et statistiques
    Task<Dictionary<string, object>> GetPushStatsAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<List<PushDeliveryDto>> GetDeliveryHistoryAsync(string userId, int limit = 50);
    Task<double> GetDeliveryRateAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<Dictionary<string, int>> GetPushStatsByPlatformAsync();
    
    // Gestion des erreurs et retry
    Task<bool> RetryFailedPushAsync(int pushId);
    Task<int> RetryFailedPushesAsync(DateTime? olderThan = null);
    Task<int> CleanupExpiredSubscriptionsAsync(int daysInactive = 30);
    Task<int> CleanupFailedPushesAsync(int daysToKeep = 7);
    
    // Configuration et test
    Task<bool> TestPushServiceAsync();
    Task<Dictionary<string, bool>> GetServiceHealthAsync();
    Task<bool> ValidateConfigurationAsync();
    Task<Dictionary<string, object>> GetConfigurationAsync();
    
    // Notifications spécialisées
    Task SendOrderPushAsync(string userId, int orderId, string status, decimal? amount = null);
    Task SendPaymentPushAsync(string userId, int orderId, string paymentStatus, decimal amount);
    Task SendPromotionPushAsync(List<string> userIds, string title, string message, string? actionUrl = null);
    Task SendStockAlertPushAsync(string sellerId, string productName, int currentStock);
    Task SendReviewPushAsync(string sellerId, string productName, int rating, string? reviewText = null);
    Task SendWelcomePushAsync(string userId, string userName);
    Task SendSystemAlertPushAsync(string message, NotificationPriority priority = NotificationPriority.Normal);
    
    // Planification et automatisation
    Task<bool> SchedulePushAsync(string userId, string title, string body, DateTime scheduledAt, Dictionary<string, object>? data = null);
    Task<bool> ScheduleBulkPushAsync(List<string> userIds, string title, string body, DateTime scheduledAt, Dictionary<string, object>? data = null);
    Task<bool> CancelScheduledPushAsync(int scheduledPushId);
    Task ProcessScheduledPushesAsync();
    
    // Segmentation et ciblage
    Task<int> SendToSegmentAsync(string segmentName, string title, string body, Dictionary<string, object>? data = null);
    Task<int> SendToUsersByLocationAsync(string country, string? city, string title, string body, Dictionary<string, object>? data = null);
    Task<int> SendToUsersByInterestsAsync(List<string> interests, string title, string body, Dictionary<string, object>? data = null);
    Task<int> SendToInactiveUsersAsync(int daysSinceLastActivity, string title, string body, Dictionary<string, object>? data = null);
}

public class PushDeliveryDto
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty; // web, android, ios
    public string Status { get; set; } = string.Empty; // sent, delivered, failed, clicked
    public DateTime SentAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public DateTime? ClickedAt { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
}

public class PushTemplateDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public string? Icon { get; set; }
    public string? Image { get; set; }
    public string? ActionUrl { get; set; }
    public List<PushActionDto> Actions { get; set; } = new();
    public Dictionary<string, object> DefaultData { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class PushActionDto
{
    public string Action { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string? Icon { get; set; }
    public string? Url { get; set; }
}

public class ScheduledPushDto
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public List<string> UserIds { get; set; } = new();
    public Dictionary<string, object> Data { get; set; } = new();
    public DateTime ScheduledAt { get; set; }
    public bool IsProcessed { get; set; }
    public DateTime? ProcessedAt { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class PushSegmentDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Dictionary<string, object> Criteria { get; set; } = new();
    public int EstimatedUserCount { get; set; }
    public DateTime LastUpdated { get; set; }
    public bool IsActive { get; set; } = true;
}
