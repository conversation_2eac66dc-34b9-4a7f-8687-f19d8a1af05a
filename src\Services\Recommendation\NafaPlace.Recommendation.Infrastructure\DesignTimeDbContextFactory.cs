using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using NafaPlace.Recommendation.Infrastructure.Data;

namespace NafaPlace.Recommendation.Infrastructure
{
    public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<RecommendationDbContext>
    {
        public RecommendationDbContext CreateDbContext(string[] args)
        {
            var connectionString = "Host=localhost;Port=5432;Database=NafaPlace.Recommendation;Username=postgres;Password=*****************";
            var optionsBuilder = new DbContextOptionsBuilder<RecommendationDbContext>();
            optionsBuilder.UseNpgsql(connectionString);

            return new RecommendationDbContext(optionsBuilder.Options);
        }
    }
}
