@using Microsoft.JSInterop
@using NafaPlace.Web.Models.Common
@inject IJSRuntime JSRuntime
@inject HttpClient HttpClient

<div class="mobile-payment-component">
    <!-- Sélection du fournisseur de paiement mobile -->
    <div class="payment-providers">
        <h6 class="mb-3">Choisissez votre méthode de paiement mobile</h6>

        <div class="provider-options">
            <!-- Orange Money -->
            <div class="provider-option @(SelectedProvider == "orange" ? "selected" : "")"
                 @onclick="@(() => SelectProvider("orange"))">
                <div class="provider-logo">
                    <img src="/images/payment/orange-money.png" alt="Orange Money" />
                </div>
                <div class="provider-info">
                    <h6 class="provider-name">Orange Money</h6>
                    <p class="provider-description">Paiement rapide et sécurisé avec Orange Money</p>
                    <div class="provider-fees">
                        Frais: <span class="fee-amount">@OrangeMoneyFees GNF</span>
                    </div>
                </div>
                <div class="provider-radio">
                    <i class="fas fa-check-circle @(SelectedProvider == "orange" ? "active" : "")"></i>
                </div>
            </div>

            <!-- MTN Mobile Money -->
            <div class="provider-option @(SelectedProvider == "mtn" ? "selected" : "")"
                 @onclick="@(() => SelectProvider("mtn"))">
                <div class="provider-logo">
                    <img src="/images/payment/mtn-money.png" alt="MTN Mobile Money" />
                </div>
                <div class="provider-info">
                    <h6 class="provider-name">MTN Mobile Money</h6>
                    <p class="provider-description">Payez facilement avec votre compte MTN Money</p>
                    <div class="provider-fees">
                        Frais: <span class="fee-amount">@MtnMoneyFees GNF</span>
                    </div>
                </div>
                <div class="provider-radio">
                    <i class="fas fa-check-circle @(SelectedProvider == "mtn" ? "active" : "")"></i>
                </div>
            </div>

            <!-- Moov Money -->
            <div class="provider-option @(SelectedProvider == "moov" ? "selected" : "")"
                 @onclick="@(() => SelectProvider("moov"))">
                <div class="provider-logo">
                    <img src="/images/payment/moov-money.png" alt="Moov Money" />
                </div>
                <div class="provider-info">
                    <h6 class="provider-name">Moov Money</h6>
                    <p class="provider-description">Service de paiement mobile Moov Money</p>
                    <div class="provider-fees">
                        Frais: <span class="fee-amount">@MoovMoneyFees GNF</span>
                    </div>
                </div>
                <div class="provider-radio">
                    <i class="fas fa-check-circle @(SelectedProvider == "moov" ? "active" : "")"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulaire de paiement -->
    @if (!string.IsNullOrEmpty(SelectedProvider))
    {
        <div class="payment-form mt-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-mobile-alt me-2"></i>
                        Détails du paiement @GetProviderDisplayName(SelectedProvider)
                    </h6>
                </div>
                <div class="card-body">
                    <!-- Méthodes sauvegardées -->
                    @if (SavedMethods.Any(m => GetProviderKey(m.Provider) == SelectedProvider))
                    {
                        <div class="saved-methods mb-3">
                            <label class="form-label">Numéros sauvegardés</label>
                            @foreach (var method in SavedMethods.Where(m => GetProviderKey(m.Provider) == SelectedProvider))
                            {
                                <div class="form-check">
                                    <input class="form-check-input" type="radio"
                                           name="savedMethod" id="<EMAIL>"
                                           @onchange="() => SelectSavedMethod(method)" />
                                    <label class="form-check-label" for="<EMAIL>">
                                        <strong>@method.Alias</strong> - @FormatPhoneNumber(method.PhoneNumber)
                                        @if (method.IsDefault)
                                        {
                                            <span class="badge bg-primary ms-2">Par défaut</span>
                                        }
                                    </label>
                                </div>
                            }
                            <hr />
                            <small class="text-muted">Ou utilisez un nouveau numéro ci-dessous</small>
                        </div>
                    }

                    <!-- Saisie du numéro de téléphone -->
                    <div class="form-group mb-3">
                        <label class="form-label" for="phoneNumber">
                            Numéro de téléphone @GetProviderDisplayName(SelectedProvider) *
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">+224</span>
                            <input @bind="PhoneNumber"
                                   @bind:event="oninput"
                                   @onkeyup="ValidatePhoneNumber"
                                   id="phoneNumber"
                                   class="form-control @(IsPhoneNumberValid ? "is-valid" : (string.IsNullOrEmpty(PhoneNumber) ? "" : "is-invalid"))"
                                   placeholder="6XX XXX XXX"
                                   maxlength="11" />
                        </div>
                        <div class="form-text">
                            Format: 6XX XXX XXX ou 7XX XXX XXX
                        </div>
                        @if (!string.IsNullOrEmpty(PhoneValidationMessage))
                        {
                            <div class="@(IsPhoneNumberValid ? "valid-feedback" : "invalid-feedback")">
                                @PhoneValidationMessage
                            </div>
                        }
                    </div>

                    <!-- Résumé du paiement -->
                    <div class="payment-summary mb-3">
                        <div class="summary-row">
                            <span>Montant à payer:</span>
                            <strong>@Amount.ToString("N0") GNF</strong>
                        </div>
                        <div class="summary-row">
                            <span>Frais de transaction:</span>
                            <span>@GetCurrentProviderFees().ToString("N0") GNF</span>
                        </div>
                        <hr />
                        <div class="summary-row total">
                            <span><strong>Total:</strong></span>
                            <strong class="text-primary">@(Amount + GetCurrentProviderFees()).ToString("N0") GNF</strong>
                        </div>
                    </div>

                    <!-- Option de sauvegarde -->
                    @if (IsUserLoggedIn)
                    {
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox"
                                   @bind="SavePaymentMethod" id="saveMethod" />
                            <label class="form-check-label" for="saveMethod">
                                Sauvegarder cette méthode de paiement pour les prochaines fois
                            </label>
                        </div>

                        @if (SavePaymentMethod)
                        {
                            <div class="form-group mb-3">
                                <label class="form-label" for="methodAlias">
                                    Nom pour cette méthode (optionnel)
                                </label>
                                <input @bind="MethodAlias"
                                       id="methodAlias"
                                       class="form-control"
                                       placeholder="Ex: Mon compte principal" />
                            </div>
                        }
                    }

                    <!-- Instructions de paiement -->
                    <div class="payment-instructions">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Comment procéder:</strong>
                            <ol class="mb-0 mt-2">
                                <li>Cliquez sur "Payer maintenant" ci-dessous</li>
                                <li>Vous recevrez un SMS avec les instructions</li>
                                <li>Suivez les étapes sur votre téléphone pour confirmer le paiement</li>
                                <li>Votre commande sera confirmée automatiquement</li>
                            </ol>
                        </div>
                    </div>

                    <!-- Bouton de paiement -->
                    <div class="payment-actions">
                        <button class="btn btn-primary btn-lg w-100"
                                @onclick="ProcessPayment"
                                disabled="@(!IsPhoneNumberValid || IsProcessing)">
                            @if (IsProcessing)
                            {
                                <span class="spinner-border spinner-border-sm me-2"></span>
                                <span>Traitement en cours...</span>
                            }
                            else
                            {
                                <i class="fas fa-mobile-alt me-2"></i>
                                <span>Payer @(Amount + GetCurrentProviderFees()).ToString("N0") GNF</span>
                            }
                        </button>
                    </div>

                    <!-- Aide et support -->
                    <div class="payment-help mt-3">
                        <small class="text-muted">
                            <i class="fas fa-question-circle me-1"></i>
                            Besoin d'aide?
                            <a href="#" @onclick="ShowHelpModal">Consultez notre guide</a> ou
                            <a href="tel:+************">contactez-nous</a>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<!-- Modal de statut de paiement -->
@if (ShowPaymentStatus)
{
    <div class="modal fade show d-block" tabindex="-1" style="background: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header border-0">
                    <h5 class="modal-title">Statut du paiement</h5>
                    @if (PaymentResult?.Status != "Processing")
                    {
                        <button type="button" class="btn-close" @onclick="ClosePaymentStatus"></button>
                    }
                </div>
                <div class="modal-body text-center">
                    @if (PaymentResult?.Status == "Processing")
                    {
                        <div class="payment-processing">
                            <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;"></div>
                            <h6>Paiement en cours...</h6>
                            <p class="text-muted">
                                Vérifiez votre téléphone et suivez les instructions reçues par SMS.
                            </p>
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     style="width: @ProgressPercentage%"></div>
                            </div>
                            <small class="text-muted">
                                Transaction ID: @PaymentResult?.TransactionId
                            </small>
                        </div>
                    }
                    else if (PaymentResult?.Status == "Completed")
                    {
                        <div class="payment-success">
                            <i class="fas fa-check-circle text-success mb-3" style="font-size: 3rem;"></i>
                            <h6 class="text-success">Paiement réussi!</h6>
                            <p class="text-muted">
                                Votre paiement de @PaymentResult?.Amount.ToString("N0") GNF a été effectué avec succès.
                            </p>
                            <div class="transaction-details">
                                <small class="text-muted">
                                    Transaction ID: @PaymentResult?.TransactionId<br />
                                    Date: @DateTime.Now.ToString("dd/MM/yyyy HH:mm")
                                </small>
                            </div>
                        </div>
                    }
                    else if (PaymentResult?.Status == "Failed")
                    {
                        <div class="payment-failed">
                            <i class="fas fa-times-circle text-danger mb-3" style="font-size: 3rem;"></i>
                            <h6 class="text-danger">Paiement échoué</h6>
                            <p class="text-muted">
                                @(PaymentResult?.ErrorMessage ?? "Une erreur s'est produite lors du paiement.")
                            </p>
                            <button class="btn btn-primary" @onclick="RetryPayment">
                                <i class="fas fa-redo me-2"></i>
                                Réessayer
                            </button>
                        </div>
                    }
                </div>
                @if (PaymentResult?.Status != "Processing")
                {
                    <div class="modal-footer border-0">
                        <button type="button" class="btn btn-secondary" @onclick="ClosePaymentStatus">
                            Fermer
                        </button>
                        @if (PaymentResult?.Status == "Completed")
                        {
                            <button type="button" class="btn btn-primary" @onclick="ContinueShopping">
                                Continuer mes achats
                            </button>
                        }
                    </div>
                }
            </div>
        </div>
    </div>
}

<!-- Modal d'aide -->
@if (ShowHelp)
{
    <div class="modal fade show d-block" tabindex="-1" style="background: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-question-circle me-2"></i>
                        Guide de paiement mobile
                    </h5>
                    <button type="button" class="btn-close" @onclick="CloseHelpModal"></button>
                </div>
                <div class="modal-body">
                    <div class="help-content">
                        <h6>Comment payer avec votre mobile?</h6>

                        <div class="help-section">
                            <h6><i class="fas fa-mobile-alt text-warning me-2"></i>Orange Money</h6>
                            <ol>
                                <li>Composez <code>#144#</code> sur votre téléphone Orange</li>
                                <li>Sélectionnez "Paiement marchand"</li>
                                <li>Entrez le code marchand reçu par SMS</li>
                                <li>Confirmez le montant et votre code PIN</li>
                            </ol>
                        </div>

                        <div class="help-section">
                            <h6><i class="fas fa-mobile-alt text-primary me-2"></i>MTN Mobile Money</h6>
                            <ol>
                                <li>Composez <code>*223#</code> sur votre téléphone MTN</li>
                                <li>Sélectionnez "Paiement"</li>
                                <li>Entrez le numéro marchand reçu par SMS</li>
                                <li>Confirmez avec votre code PIN MTN</li>
                            </ol>
                        </div>

                        <div class="help-section">
                            <h6><i class="fas fa-mobile-alt text-success me-2"></i>Moov Money</h6>
                            <ol>
                                <li>Composez <code>#155#</code> sur votre téléphone Moov</li>
                                <li>Sélectionnez "Paiement"</li>
                                <li>Entrez les informations reçues par SMS</li>
                                <li>Validez avec votre code secret Moov Money</li>
                            </ol>
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Important:</strong> Assurez-vous d'avoir suffisamment de crédit sur votre compte mobile money avant de procéder au paiement.
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="CloseHelpModal">
                        Fermer
                    </button>
                </div>
            </div>
        </div>
    </div>
}

<style>
    .mobile-payment-component {
        max-width: 600px;
        margin: 0 auto;
    }

    .payment-providers {
        margin-bottom: 2rem;
    }

    .provider-options {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .provider-option {
        display: flex;
        align-items: center;
        padding: 1.5rem;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
    }

    .provider-option:hover {
        border-color: #E73C30;
        box-shadow: 0 4px 15px rgba(231, 60, 48, 0.1);
    }

    .provider-option.selected {
        border-color: #E73C30;
        background: #fff5f5;
        box-shadow: 0 4px 15px rgba(231, 60, 48, 0.15);
    }

    .provider-logo {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8f9fa;
        border-radius: 12px;
        margin-right: 1rem;
    }

    .provider-logo img {
        width: 40px;
        height: 40px;
        object-fit: contain;
    }

    .provider-info {
        flex: 1;
    }

    .provider-name {
        margin: 0 0 0.5rem 0;
        color: #333;
        font-weight: 600;
    }

    .provider-description {
        margin: 0 0 0.5rem 0;
        color: #6c757d;
        font-size: 0.9rem;
    }

    .provider-fees {
        font-size: 0.85rem;
        color: #495057;
    }

    .fee-amount {
        font-weight: 600;
        color: #E73C30;
    }

    .provider-radio {
        margin-left: 1rem;
    }

    .provider-radio i {
        font-size: 1.5rem;
        color: #dee2e6;
        transition: color 0.3s ease;
    }

    .provider-radio i.active {
        color: #E73C30;
    }

    .payment-form {
        margin-top: 2rem;
    }

    .card {
        border: none;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        border-radius: 12px;
    }

    .card-header {
        background: linear-gradient(135deg, #E73C30, #F96302);
        color: white;
        border-radius: 12px 12px 0 0;
        padding: 1rem 1.5rem;
    }

    .saved-methods {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
    }

    .form-check {
        margin-bottom: 0.75rem;
    }

    .form-check-label {
        cursor: pointer;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .payment-summary {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
    }

    .summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .summary-row:last-child {
        margin-bottom: 0;
    }

    .summary-row.total {
        font-size: 1.1rem;
        padding-top: 0.5rem;
    }

    .payment-instructions {
        margin: 1.5rem 0;
    }

    .payment-instructions ol {
        padding-left: 1.25rem;
    }

    .payment-instructions li {
        margin-bottom: 0.25rem;
    }

    .payment-actions {
        margin-top: 1.5rem;
    }

    .payment-help {
        text-align: center;
        border-top: 1px solid #e9ecef;
        padding-top: 1rem;
    }

    .payment-help a {
        color: #E73C30;
        text-decoration: none;
    }

    .payment-help a:hover {
        text-decoration: underline;
    }

    /* Modal styles */
    .payment-processing {
        padding: 2rem 0;
    }

    .payment-success,
    .payment-failed {
        padding: 1rem 0;
    }

    .transaction-details {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 1rem;
        margin-top: 1rem;
    }

    .help-content {
        max-height: 500px;
        overflow-y: auto;
    }

    .help-section {
        margin-bottom: 2rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .help-section h6 {
        margin-bottom: 1rem;
        color: #333;
    }

    .help-section ol {
        margin-bottom: 0;
        padding-left: 1.25rem;
    }

    .help-section li {
        margin-bottom: 0.5rem;
    }

    .help-section code {
        background: #e9ecef;
        color: #495057;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-weight: 600;
    }

    .progress {
        height: 8px;
        background-color: #f0f0f0;
    }

    .progress-bar {
        background: linear-gradient(90deg, #E73C30, #F96302);
    }

    /* Responsive */
    @@media (max-width: 768px) {
        .provider-option {
            flex-direction: column;
            text-align: center;
            padding: 1rem;
        }

        .provider-logo {
            margin-right: 0;
            margin-bottom: 1rem;
        }

        .provider-radio {
            margin-left: 0;
            margin-top: 1rem;
        }

        .summary-row {
            font-size: 0.9rem;
        }

        .help-section {
            padding: 0.75rem;
        }
    }

    /* Animation pour le spinner */
    @@keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .spinner-border {
        animation: spin 1s linear infinite;
    }
</style>

@code {
    [Parameter] public decimal Amount { get; set; }
    [Parameter] public string OrderId { get; set; } = "";
    [Parameter] public string? UserId { get; set; }
    [Parameter] public EventCallback<PaymentResultDto> OnPaymentCompleted { get; set; }
    [Parameter] public EventCallback OnPaymentCancelled { get; set; }

    // État du composant
    private string SelectedProvider = "";
    private string PhoneNumber = "";
    private bool IsPhoneNumberValid = false;
    private string PhoneValidationMessage = "";
    private bool SavePaymentMethod = false;
    private string MethodAlias = "";
    private bool IsProcessing = false;
    private bool ShowPaymentStatus = false;
    private bool ShowHelp = false;
    private int ProgressPercentage = 0;

    // Données
    private List<SavedPaymentMethodDto> SavedMethods = new();
    private PaymentResultDto? PaymentResult = null;
    private decimal OrangeMoneyFees = 0;
    private decimal MtnMoneyFees = 0;
    private decimal MoovMoneyFees = 0;

    // Propriétés calculées
    private bool IsUserLoggedIn => !string.IsNullOrEmpty(UserId);

    protected override async Task OnInitializedAsync()
    {
        // Charger les méthodes de paiement sauvegardées
        if (IsUserLoggedIn)
        {
            await LoadSavedPaymentMethods();
        }

        // Calculer les frais pour chaque fournisseur
        await CalculateAllFees();
    }

    /// <summary>
    /// Charge les méthodes de paiement sauvegardées de l'utilisateur
    /// </summary>
    private async Task LoadSavedPaymentMethods()
    {
        try
        {
            var response = await HttpClient.GetAsync("/api/v1/mobilepayment/saved-methods");
            if (response.IsSuccessStatusCode)
            {
                SavedMethods = await response.Content.ReadFromJsonAsync<List<SavedPaymentMethodDto>>() ?? new();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des méthodes sauvegardées: {ex.Message}");
        }
    }

    /// <summary>
    /// Calcule les frais pour tous les fournisseurs
    /// </summary>
    private async Task CalculateAllFees()
    {
        try
        {
            // Orange Money
            var orangeResponse = await HttpClient.GetAsync($"/api/v1/mobilepayment/fees?provider=OrangeMoney&amount={Amount}");
            if (orangeResponse.IsSuccessStatusCode)
            {
                var orangeFees = await orangeResponse.Content.ReadFromJsonAsync<FeeCalculationDto>();
                OrangeMoneyFees = orangeFees?.Fees ?? 0;
            }

            // MTN Money
            var mtnResponse = await HttpClient.GetAsync($"/api/v1/mobilepayment/fees?provider=MtnMoney&amount={Amount}");
            if (mtnResponse.IsSuccessStatusCode)
            {
                var mtnFees = await mtnResponse.Content.ReadFromJsonAsync<FeeCalculationDto>();
                MtnMoneyFees = mtnFees?.Fees ?? 0;
            }

            // Moov Money
            var moovResponse = await HttpClient.GetAsync($"/api/v1/mobilepayment/fees?provider=MoovMoney&amount={Amount}");
            if (moovResponse.IsSuccessStatusCode)
            {
                var moovFees = await moovResponse.Content.ReadFromJsonAsync<FeeCalculationDto>();
                MoovMoneyFees = moovFees?.Fees ?? 0;
            }

            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du calcul des frais: {ex.Message}");
        }
    }

    /// <summary>
    /// Sélectionne un fournisseur de paiement
    /// </summary>
    private void SelectProvider(string provider)
    {
        SelectedProvider = provider;
        PhoneNumber = "";
        IsPhoneNumberValid = false;
        PhoneValidationMessage = "";
        StateHasChanged();
    }

    /// <summary>
    /// Sélectionne une méthode de paiement sauvegardée
    /// </summary>
    private void SelectSavedMethod(SavedPaymentMethodDto method)
    {
        PhoneNumber = method.PhoneNumber.Replace("+224", "").Replace(" ", "");
        ValidatePhoneNumber();
        StateHasChanged();
    }

    /// <summary>
    /// Valide le numéro de téléphone saisi
    /// </summary>
    private void ValidatePhoneNumber()
    {
        if (string.IsNullOrEmpty(PhoneNumber))
        {
            IsPhoneNumberValid = false;
            PhoneValidationMessage = "";
            return;
        }

        // Nettoyer le numéro
        var cleanNumber = PhoneNumber.Replace(" ", "").Replace("-", "");

        // Vérifier le format guinéen (6XXXXXXXX ou 7XXXXXXXX)
        if (cleanNumber.Length == 9 && (cleanNumber.StartsWith("6") || cleanNumber.StartsWith("7")))
        {
            IsPhoneNumberValid = true;
            PhoneValidationMessage = "Numéro valide";
        }
        else
        {
            IsPhoneNumberValid = false;
            PhoneValidationMessage = "Format invalide. Utilisez 6XX XXX XXX ou 7XX XXX XXX";
        }

        StateHasChanged();
    }

    /// <summary>
    /// Traite le paiement mobile
    /// </summary>
    private async Task ProcessPayment()
    {
        if (!IsPhoneNumberValid || IsProcessing)
            return;

        try
        {
            IsProcessing = true;
            ShowPaymentStatus = true;
            ProgressPercentage = 10;

            // Préparer la requête selon le fournisseur
            var endpoint = GetPaymentEndpoint();
            var requestData = CreatePaymentRequest();

            ProgressPercentage = 30;
            StateHasChanged();

            // Envoyer la requête de paiement
            var response = await HttpClient.PostAsJsonAsync(endpoint, requestData);

            ProgressPercentage = 60;
            StateHasChanged();

            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<MobilePaymentResponseDto>();
                if (result?.Success == true)
                {
                    PaymentResult = new PaymentResultDto
                    {
                        Status = "Processing",
                        TransactionId = result.TransactionId,
                        Amount = Amount,
                        Message = result.Message
                    };

                    ProgressPercentage = 80;
                    StateHasChanged();

                    // Commencer la vérification du statut
                    await MonitorPaymentStatus(result.TransactionId);

                    // Sauvegarder la méthode si demandé
                    if (SavePaymentMethod && IsUserLoggedIn)
                    {
                        await SaveCurrentPaymentMethod();
                    }
                }
                else
                {
                    PaymentResult = new PaymentResultDto
                    {
                        Status = "Failed",
                        ErrorMessage = result?.Message ?? "Erreur lors de l'initiation du paiement"
                    };
                }
            }
            else
            {
                PaymentResult = new PaymentResultDto
                {
                    Status = "Failed",
                    ErrorMessage = "Erreur de communication avec le service de paiement"
                };
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du traitement du paiement: {ex.Message}");
            PaymentResult = new PaymentResultDto
            {
                Status = "Failed",
                ErrorMessage = "Une erreur inattendue s'est produite"
            };
        }
        finally
        {
            IsProcessing = false;
            ProgressPercentage = 100;
            StateHasChanged();
        }
    }

    /// <summary>
    /// Surveille le statut du paiement
    /// </summary>
    private async Task MonitorPaymentStatus(string transactionId)
    {
        var maxAttempts = 30; // 5 minutes maximum (30 x 10 secondes)
        var attempts = 0;

        while (attempts < maxAttempts && PaymentResult?.Status == "Processing")
        {
            await Task.Delay(10000); // Attendre 10 secondes
            attempts++;

            try
            {
                var response = await HttpClient.GetAsync($"/api/v1/mobilepayment/status/{transactionId}");
                if (response.IsSuccessStatusCode)
                {
                    var status = await response.Content.ReadFromJsonAsync<PaymentStatusDto>();
                    if (status != null)
                    {
                        if (status.Status == "Completed")
                        {
                            PaymentResult = new PaymentResultDto
                            {
                                Status = "Completed",
                                TransactionId = transactionId,
                                Amount = status.Amount,
                                Message = "Paiement effectué avec succès"
                            };

                            await OnPaymentCompleted.InvokeAsync(PaymentResult);
                            break;
                        }
                        else if (status.Status == "Failed" || status.Status == "Cancelled")
                        {
                            PaymentResult = new PaymentResultDto
                            {
                                Status = "Failed",
                                TransactionId = transactionId,
                                ErrorMessage = "Le paiement a été annulé ou a échoué"
                            };
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la vérification du statut: {ex.Message}");
            }

            StateHasChanged();
        }

        // Timeout - considérer comme échoué
        if (attempts >= maxAttempts && PaymentResult?.Status == "Processing")
        {
            PaymentResult = new PaymentResultDto
            {
                Status = "Failed",
                ErrorMessage = "Timeout - Le paiement n'a pas pu être confirmé"
            };
            StateHasChanged();
        }
    }

    /// <summary>
    /// Sauvegarde la méthode de paiement actuelle
    /// </summary>
    private async Task SaveCurrentPaymentMethod()
    {
        try
        {
            var saveRequest = new SaveMobilePaymentMethodRequestDto
            {
                Provider = GetCurrentProvider(),
                PhoneNumber = "+224" + PhoneNumber.Replace(" ", ""),
                Alias = string.IsNullOrEmpty(MethodAlias) ? GetProviderDisplayName(SelectedProvider) : MethodAlias,
                IsDefault = SavedMethods.Count == 0 // Premier = par défaut
            };

            await HttpClient.PostAsJsonAsync("/api/v1/mobilepayment/save-method", saveRequest);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la sauvegarde de la méthode: {ex.Message}");
        }
    }

    /// <summary>
    /// Obtient l'endpoint de paiement selon le fournisseur
    /// </summary>
    private string GetPaymentEndpoint()
    {
        return SelectedProvider switch
        {
            "orange" => "/api/v1/mobilepayment/orange-money/initiate",
            "mtn" => "/api/v1/mobilepayment/mtn-money/initiate",
            "moov" => "/api/v1/mobilepayment/moov-money/initiate",
            _ => throw new InvalidOperationException("Fournisseur non supporté")
        };
    }

    /// <summary>
    /// Crée la requête de paiement
    /// </summary>
    private object CreatePaymentRequest()
    {
        var phoneWithCountryCode = "+224" + PhoneNumber.Replace(" ", "");

        return new
        {
            PhoneNumber = phoneWithCountryCode,
            Amount = Amount,
            OrderId = OrderId,
            Description = $"Commande NafaPlace #{OrderId}",
            ReturnUrl = $"{JSRuntime.InvokeAsync<string>("window.location.origin")}/orders/{OrderId}"
        };
    }

    /// <summary>
    /// Obtient les frais du fournisseur actuel
    /// </summary>
    private decimal GetCurrentProviderFees()
    {
        return SelectedProvider switch
        {
            "orange" => OrangeMoneyFees,
            "mtn" => MtnMoneyFees,
            "moov" => MoovMoneyFees,
            _ => 0
        };
    }

    /// <summary>
    /// Obtient l'énumération du fournisseur actuel
    /// </summary>
    private MobilePaymentProvider GetCurrentProvider()
    {
        return SelectedProvider switch
        {
            "orange" => MobilePaymentProvider.OrangeMoney,
            "mtn" => MobilePaymentProvider.MtnMoney,
            "moov" => MobilePaymentProvider.MoovMoney,
            _ => throw new InvalidOperationException("Fournisseur non supporté")
        };
    }

    /// <summary>
    /// Obtient le nom d'affichage du fournisseur
    /// </summary>
    private string GetProviderDisplayName(string provider)
    {
        return provider switch
        {
            "orange" => "Orange Money",
            "mtn" => "MTN Mobile Money",
            "moov" => "Moov Money",
            _ => provider
        };
    }

    /// <summary>
    /// Obtient la clé du fournisseur depuis l'énumération
    /// </summary>
    private string GetProviderKey(MobilePaymentProvider provider)
    {
        return provider switch
        {
            MobilePaymentProvider.OrangeMoney => "orange",
            MobilePaymentProvider.MtnMoney => "mtn",
            MobilePaymentProvider.MoovMoney => "moov",
            _ => ""
        };
    }

    /// <summary>
    /// Formate un numéro de téléphone pour l'affichage
    /// </summary>
    private string FormatPhoneNumber(string phoneNumber)
    {
        var clean = phoneNumber.Replace("+224", "").Replace(" ", "");
        if (clean.Length == 9)
        {
            return $"+224 {clean.Substring(0, 3)} {clean.Substring(3, 3)} {clean.Substring(6, 3)}";
        }
        return phoneNumber;
    }

    private void RetryPayment()
    {
        PaymentResult = null;
        ShowPaymentStatus = false;
        ProgressPercentage = 0;
        StateHasChanged();
    }

    private void ClosePaymentStatus()
    {
        ShowPaymentStatus = false;
        PaymentResult = null;
        StateHasChanged();
    }

    private async Task ContinueShopping()
    {
        ClosePaymentStatus();
        await JSRuntime.InvokeVoidAsync("window.location.href", "/");
    }

    private void ShowHelpModal()
    {
        ShowHelp = true;
        StateHasChanged();
    }

    private void CloseHelpModal()
    {
        ShowHelp = false;
        StateHasChanged();
    }

    // DTOs
    public class SavedPaymentMethodDto
    {
        public int Id { get; set; }
        public MobilePaymentProvider Provider { get; set; }
        public string PhoneNumber { get; set; } = "";
        public string Alias { get; set; } = "";
        public bool IsDefault { get; set; }
    }

    public class FeeCalculationDto
    {
        public decimal Fees { get; set; }
        public decimal TotalAmount { get; set; }
    }

    public class MobilePaymentResponseDto
    {
        public bool Success { get; set; }
        public string TransactionId { get; set; } = "";
        public string? PaymentUrl { get; set; }
        public string Message { get; set; } = "";
        public DateTime ExpiresAt { get; set; }
    }

    public class PaymentResultDto
    {
        public string Status { get; set; } = "";
        public string? TransactionId { get; set; }
        public decimal Amount { get; set; }
        public string? Message { get; set; }
        public string? ErrorMessage { get; set; }
    }

    public class PaymentStatusDto
    {
        public string TransactionId { get; set; } = "";
        public string Status { get; set; } = "";
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "";
        public MobilePaymentProvider Provider { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class SaveMobilePaymentMethodRequestDto
    {
        public MobilePaymentProvider Provider { get; set; }
        public string PhoneNumber { get; set; } = "";
        public string Alias { get; set; } = "";
        public bool IsDefault { get; set; }
    }

    public enum MobilePaymentProvider
    {
        OrangeMoney,
        MtnMoney,
        MoovMoney
    }
}