using System.ComponentModel.DataAnnotations;

namespace NafaPlace.ChatEcommerce.Domain.Entities;

public class ChatSession
{
    public int Id { get; set; }
    
    [Required]
    public string SessionId { get; set; } = string.Empty;
    
    public string? UserId { get; set; }
    
    public string? UserName { get; set; }
    
    public bool IsOnline { get; set; } = false;
    
    public DateTime LastActivity { get; set; } = DateTime.UtcNow;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public string? UserAgent { get; set; }
    
    public string? IpAddress { get; set; }
    
    public string? Metadata { get; set; } // JSON metadata
}
