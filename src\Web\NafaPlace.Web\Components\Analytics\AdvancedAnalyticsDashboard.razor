@using NafaPlace.Web.Models.Analytics
@using NafaPlace.Web.Services
@using System.Globalization
@inject IJSRuntime JSRuntime
@inject ILogger<AdvancedAnalyticsDashboard> Logger
@inject IAnalyticsService AnalyticsService

<div class="analytics-dashboard">
    <!-- En-tête avec filtres et actions -->
    <div class="dashboard-header">
        <div class="header-title">
            <h1>📊 Analytics Avancé & Prédictions IA</h1>
            <p>Insights intelligents pour optimiser votre business</p>
        </div>

        <div class="header-controls">
            <div class="date-filters">
                <label>Période:</label>
                <select @bind="selectedPeriod" class="form-select">
                    <option value="7d">7 derniers jours</option>
                    <option value="30d">30 derniers jours</option>
                    <option value="3m">3 derniers mois</option>
                    <option value="6m">6 derniers mois</option>
                    <option value="1y">1 an</option>
                    <option value="custom">Personnalisé</option>
                </select>
            </div>

            <div class="action-buttons">
                <button class="btn btn-outline-primary" @onclick="RefreshData">
                    🔄 Actualiser
                </button>
                <button class="btn btn-outline-success" @onclick="ExportReport">
                    📥 Exporter
                </button>
                <button class="btn btn-primary" @onclick="GenerateAIInsights">
                    🤖 Insights IA
                </button>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="loading-container">
            <div class="loading-spinner"></div>
            <p>Génération des analyses en cours...</p>
        </div>
    }
    else if (dashboard != null)
    {
        <!-- KPIs principaux -->
        <div class="kpi-section">
            <div class="kpi-card revenue">
                <div class="kpi-icon">💰</div>
                <div class="kpi-content">
                    <h3>Chiffre d'affaires</h3>
                    <div class="kpi-value">@FormatCurrency(dashboard.TotalRevenue)</div>
                    <div class="kpi-change @GetChangeClass((double)dashboard.RevenueGrowth)">
                        @GetChangeIcon((double)dashboard.RevenueGrowth) @Math.Abs((double)dashboard.RevenueGrowth).ToString("F1")%
                    </div>
                </div>
            </div>

            <div class="kpi-card orders">
                <div class="kpi-icon">📦</div>
                <div class="kpi-content">
                    <h3>Commandes</h3>
                    <div class="kpi-value">@dashboard.TotalOrders.ToString("N0")</div>
                    <div class="kpi-change @GetChangeClass((double)dashboard.OrderGrowth)">
                        @GetChangeIcon((double)dashboard.OrderGrowth) @Math.Abs((double)dashboard.OrderGrowth).ToString("F1")%
                    </div>
                </div>
            </div>

            <div class="kpi-card customers">
                <div class="kpi-icon">👥</div>
                <div class="kpi-content">
                    <h3>Clients actifs</h3>
                    <div class="kpi-value">@dashboard.ActiveCustomers.ToString("N0")</div>
                    <div class="kpi-change @GetChangeClass((double)dashboard.CustomerGrowth)">
                        @GetChangeIcon((double)dashboard.CustomerGrowth) @Math.Abs((double)dashboard.CustomerGrowth).ToString("F1")%
                    </div>
                </div>
            </div>

            <div class="kpi-card aov">
                <div class="kpi-icon">🛒</div>
                <div class="kpi-content">
                    <h3>Panier moyen</h3>
                    <div class="kpi-value">@FormatCurrency(dashboard.AverageOrderValue)</div>
                    <div class="kpi-change neutral">
                        @dashboard.ConversionRate.ToString("F1")% conversion
                    </div>
                </div>
            </div>

            <div class="kpi-card clv">
                <div class="kpi-icon">⭐</div>
                <div class="kpi-content">
                    <h3>Valeur client (CLV)</h3>
                    <div class="kpi-value">@FormatCurrency(dashboard.CustomerLifetimeValue)</div>
                    <div class="kpi-change neutral">
                        @dashboard.CustomerRetentionRate.ToString("F1")% rétention
                    </div>
                </div>
            </div>

            <div class="kpi-card nps">
                <div class="kpi-icon">📈</div>
                <div class="kpi-content">
                    <h3>Net Promoter Score</h3>
                    <div class="kpi-value">@dashboard.NetPromoterScore</div>
                    <div class="kpi-change @GetNPSClass(dashboard.NetPromoterScore)">
                        @GetNPSText(dashboard.NetPromoterScore)
                    </div>
                </div>
            </div>
        </div>

        <!-- Prédictions IA -->
        @if (salesPredictions?.Any() == true)
        {
            <div class="predictions-section">
                <h2>🔮 Prédictions IA</h2>
                <div class="predictions-grid">
                    <!-- Prédictions de ventes -->
                    <div class="prediction-card">
                        <h3>Ventes prévisionnelles</h3>
                        <div class="prediction-chart">
                            <canvas id="salesPredictionChart"></canvas>
                        </div>
                        <div class="prediction-summary">
                            @foreach (var prediction in salesPredictions.Take(3))
                            {
                                <div class="prediction-item">
                                    <span class="prediction-date">@prediction.PredictionDate.ToString("dd/MM")</span>
                                    <span class="prediction-value">@FormatCurrency(prediction.PredictedRevenue)</span>
                                    <span class="prediction-confidence">@Math.Round(prediction.ConfidenceLevel * 100)%</span>
                                </div>
                            }
                        </div>
                    </div>

                    <!-- Prédictions de churn -->
                    @if (churnAnalysis != null)
                    {
                        <div class="prediction-card">
                            <h3>Risque de désabonnement</h3>
                            <div class="churn-metrics">
                                <div class="churn-rate">
                                    <span class="churn-label">Taux global</span>
                                    <span class="churn-value @GetChurnClass((double)churnAnalysis.OverallChurnRate)">
                                        @churnAnalysis.OverallChurnRate.ToString("F1")%
                                    </span>
                                </div>
                                <div class="high-risk-customers">
                                    <span class="risk-label">Clients à risque élevé</span>
                                    <span class="risk-count">@churnAnalysis.HighRiskCustomers.Count</span>
                                </div>
                            </div>
                            <div class="churn-factors">
                                @foreach (var factor in churnAnalysis.ChurnFactors.Take(3))
                                {
                                    <div class="factor-item">
                                        <div class="factor-name">@factor.Factor</div>
                                        <div class="factor-importance">
                                            <div class="importance-bar" style="width: @(factor.Importance * 100)%"></div>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    }

                    <!-- Recommandations IA -->
                    @if (aiInsights?.Any() == true)
                    {
                        <div class="prediction-card insights-card">
                            <h3>💡 Insights IA</h3>
                            <div class="insights-list">
                                @foreach (var insight in aiInsights.Take(5))
                                {
                                    <div class="insight-item @insight.Type">
                                        <div class="insight-header">
                                            <span class="insight-icon">@GetInsightIcon(insight.Type)</span>
                                            <span class="insight-title">@insight.Title</span>
                                            <span class="insight-confidence">@Math.Round(insight.Confidence * 100)%</span>
                                        </div>
                                        <div class="insight-description">@insight.Description</div>
                                        @if (insight.Recommendations?.Any() == true)
                                        {
                                            <div class="insight-recommendations">
                                                @foreach (var rec in insight.Recommendations.Take(2))
                                                {
                                                    <span class="recommendation-tag">@rec</span>
                                                }
                                            </div>
                                        }
                                    </div>
                                }
                            </div>
                        </div>
                    }
                </div>
            </div>
        }

        <!-- Graphiques et analyses -->
        <div class="charts-section">
            <div class="charts-grid">
                <!-- Graphique de revenus temporel -->
                <div class="chart-card">
                    <h3>📈 Évolution du chiffre d'affaires</h3>
                    <div class="chart-container">
                        <canvas id="revenueChart"></canvas>
                    </div>
                </div>

                <!-- Performance par catégorie -->
                <div class="chart-card">
                    <h3>🏷️ Performance par catégorie</h3>
                    <div class="category-performance">
                        @foreach (var category in dashboard.CategoryPerformance.Take(5))
                        {
                            <div class="category-item">
                                <div class="category-info">
                                    <span class="category-name">@category.Category</span>
                                    <span class="category-revenue">@FormatCurrency(category.Revenue)</span>
                                </div>
                                <div class="category-bar">
                                    <div class="category-fill" style="width: @GetCategoryPercentage(category.Revenue)%"></div>
                                </div>
                                <div class="category-growth @GetChangeClass((double)category.GrowthRate)">
                                    @GetChangeIcon((double)category.GrowthRate) @Math.Abs((double)category.GrowthRate).ToString("F1")%
                                </div>
                            </div>
                        }
                    </div>
                </div>

                <!-- Top produits -->
                <div class="chart-card">
                    <h3>🏆 Produits les plus vendus</h3>
                    <div class="top-products">
                        @foreach (var (product, index) in dashboard.TopProducts.Take(5).Select((p, i) => (p, i)))
                        {
                            <div class="product-item">
                                <div class="product-rank">@(index + 1)</div>
                                <div class="product-info">
                                    <div class="product-name">@product.Name</div>
                                    <div class="product-category">@product.Category</div>
                                </div>
                                <div class="product-metrics">
                                    <div class="product-revenue">@FormatCurrency(product.Revenue)</div>
                                    <div class="product-units">@product.UnitsSold unités</div>
                                </div>
                            </div>
                        }
                    </div>
                </div>

                <!-- Performance géographique (Guinée) -->
                @if (dashboard.GeographicData?.Any() == true)
                {
                    <div class="chart-card">
                        <h3>🗺️ Performance par région (Guinée)</h3>
                        <div class="geographic-performance">
                            @foreach (var region in dashboard.GeographicData.OrderByDescending(r => r.Revenue))
                            {
                                <div class="region-item">
                                    <div class="region-info">
                                        <span class="region-name">@region.Region</span>
                                        <span class="region-customers">@region.Customers clients</span>
                                    </div>
                                    <div class="region-metrics">
                                        <div class="region-revenue">@FormatCurrency(region.Revenue)</div>
                                        <div class="region-orders">@region.Orders commandes</div>
                                    </div>
                                    <div class="region-growth @GetChangeClass((double)region.GrowthRate)">
                                        @GetChangeIcon((double)region.GrowthRate) @Math.Abs((double)region.GrowthRate).ToString("F1")%
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                }

                <!-- Segmentation client -->
                @if (customerSegmentation?.Segments?.Any() == true)
                {
                    <div class="chart-card">
                        <h3>👥 Segmentation client IA</h3>
                        <div class="customer-segments">
                            @foreach (var segment in customerSegmentation.Segments)
                            {
                                <div class="segment-item">
                                    <div class="segment-header">
                                        <span class="segment-name">@segment.Name</span>
                                        <span class="segment-percentage">@segment.Percentage.ToString("F1")%</span>
                                    </div>
                                    <div class="segment-description">@segment.Description</div>
                                    <div class="segment-metrics">
                                        <span class="segment-customers">@segment.CustomerCount clients</span>
                                        <span class="segment-aov">@FormatCurrency(segment.AverageOrderValue) AOV</span>
                                        <span class="segment-revenue">@FormatCurrency(segment.TotalRevenue)</span>
                                    </div>
                                    @if (segment.Characteristics?.Any() == true)
                                    {
                                        <div class="segment-characteristics">
                                            @foreach (var characteristic in segment.Characteristics.Take(3))
                                            {
                                                <span class="characteristic-tag">@characteristic</span>
                                            }
                                        </div>
                                    }
                                </div>
                            }
                        </div>
                    </div>
                }
            </div>
        </div>

        <!-- Tendances temps réel -->
        @if (realTimeTrends != null)
        {
            <div class="real-time-section">
                <h2>⚡ Tendances temps réel</h2>
                <div class="real-time-grid">
                    <!-- Produits tendance -->
                    <div class="trend-card">
                        <h3>🔥 Produits en tendance</h3>
                        <div class="trending-products">
                            @foreach (var product in realTimeTrends.TrendingProducts.Take(5))
                            {
                                <div class="trending-item">
                                    <span class="trending-name">@product.Name</span>
                                    <span class="trending-metric">@product.TrendScore.ToString("F1")</span>
                                </div>
                            }
                        </div>
                    </div>

                    <!-- Recherches populaires -->
                    <div class="trend-card">
                        <h3>🔍 Recherches populaires</h3>
                        <div class="search-trends">
                            @foreach (var search in realTimeTrends.SearchTrends.Take(5))
                            {
                                <div class="search-item">
                                    <span class="search-term">@search.Term</span>
                                    <span class="search-volume">@search.Volume</span>
                                </div>
                            }
                        </div>
                    </div>

                    <!-- Alertes actives -->
                    @if (realTimeTrends.ActiveAlerts?.Any() == true)
                    {
                        <div class="trend-card alerts-card">
                            <h3>🚨 Alertes actives</h3>
                            <div class="active-alerts">
                                @foreach (var alert in realTimeTrends.ActiveAlerts.Take(3))
                                {
                                    <div class="alert-item @alert.Severity">
                                        <span class="alert-icon">@GetAlertIcon(alert.Severity)</span>
                                        <div class="alert-content">
                                            <span class="alert-title">@alert.Title</span>
                                            <span class="alert-message">@alert.Message</span>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                </div>
            </div>
        }

        <!-- Actions recommandées -->
        @if (businessOptimizations?.Any() == true)
        {
            <div class="recommendations-section">
                <h2>🎯 Recommandations d'optimisation</h2>
                <div class="recommendations-grid">
                    @foreach (var optimization in businessOptimizations.Take(6))
                    {
                        <div class="recommendation-card @optimization.Priority">
                            <div class="recommendation-header">
                                <span class="recommendation-area">@optimization.Area</span>
                                <span class="recommendation-priority">@optimization.Priority</span>
                            </div>
                            <div class="recommendation-content">
                                <p>@optimization.Recommendation</p>
                                <div class="recommendation-impact">
                                    Impact estimé: <strong>@FormatCurrency(optimization.EstimatedImpact)</strong>
                                </div>
                            </div>
                            @if (optimization.Actions?.Any() == true)
                            {
                                <div class="recommendation-actions">
                                    @foreach (var action in optimization.Actions.Take(2))
                                    {
                                        <span class="action-tag">@action</span>
                                    }
                                </div>
                            }
                        </div>
                    }
                </div>
            </div>
        }
    }
    else
    {
        <div class="error-container">
            <p>❌ Erreur lors du chargement des données analytics</p>
            <button class="btn btn-primary" @onclick="RefreshData">Réessayer</button>
        </div>
    }
</div>

@code {
    [Parameter] public EventCallback<string> OnError { get; set; }

    private string selectedPeriod = "30d";
    private bool isLoading = false;

    // Données principales
    private ComprehensiveDashboardDto? dashboard;
    private List<SalesPredictionDto>? salesPredictions;
    private CustomerChurnAnalysisDto? churnAnalysis;
    private List<AIInsightDto>? aiInsights;
    private RealTimeTrendsDto? realTimeTrends;
    private AICustomerSegmentationDto? customerSegmentation;
    private List<BusinessOptimizationDto>? businessOptimizations;

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await InitializeCharts();
        }
    }

    private async Task LoadDashboardData()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            var (fromDate, toDate) = GetDateRange(selectedPeriod);

            // Charger les données principales
            await Task.WhenAll(
                LoadDashboard(fromDate, toDate),
                LoadPredictions(),
                LoadRealTimeTrends(),
                LoadCustomerSegmentation(),
                LoadBusinessOptimizations()
            );
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du chargement des données analytics");
            await OnError.InvokeAsync("Erreur lors du chargement des données");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadDashboard(DateTime fromDate, DateTime toDate)
    {
        try
        {
            // Charger les vraies données depuis l'API Analytics
            var analyticsData = await AnalyticsService.GetComprehensiveDashboardAsync(fromDate, toDate);

            if (analyticsData != null)
            {
                dashboard = analyticsData;
            }
            else
            {
                // Fallback avec données par défaut si l'API n'est pas disponible
                dashboard = await GetFallbackDashboardData(fromDate, toDate);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du chargement des données dashboard");
            dashboard = await GetFallbackDashboardData(fromDate, toDate);
        }
    }

    private async Task<ComprehensiveDashboardDto> GetFallbackDashboardData(DateTime fromDate, DateTime toDate)
    {
        return new ComprehensiveDashboardDto
        {
            TotalRevenue = 0,
            RevenueGrowth = 0,
            TotalOrders = 0,
            OrderGrowth = 0,
            ActiveCustomers = 0,
            CustomerGrowth = 0,
            AverageOrderValue = 0,
            ConversionRate = 0,
            CustomerLifetimeValue = 0,
            CustomerRetentionRate = 0,
            ChurnRate = 0,
            NetPromoterScore = 0,
            FromDate = fromDate,
            ToDate = toDate,
            GeneratedAt = DateTime.UtcNow,

            CategoryPerformance = new List<CategoryPerformanceDto>
            {
                new() { Category = "Électronique", Revenue = 6200000m, Orders = 342, GrowthRate = 15.3m },
                new() { Category = "Mode", Revenue = 4800000m, Orders = 456, GrowthRate = 8.7m },
                new() { Category = "Maison & Jardin", Revenue = 2100000m, Orders = 234, GrowthRate = -2.1m },
                new() { Category = "Sports", Revenue = 1650000m, Orders = 178, GrowthRate = 22.4m },
                new() { Category = "Alimentation", Revenue = 1000000m, Orders = 156, GrowthRate = 5.6m }
            },

            TopProducts = new List<TopProductDto>
            {
                new() { ProductId = 1, Name = "Smartphone Samsung Galaxy", Revenue = 2100000m, UnitsSold = 42, Category = "Électronique" },
                new() { ProductId = 2, Name = "Robe africaine traditionnelle", Revenue = 1800000m, UnitsSold = 120, Category = "Mode" },
                new() { ProductId = 3, Name = "Chaussures de sport Nike", Revenue = 950000m, UnitsSold = 76, Category = "Sports" },
                new() { ProductId = 4, Name = "Réfrigérateur LG", Revenue = 850000m, UnitsSold = 8, Category = "Électronique" },
                new() { ProductId = 5, Name = "Sac à main en cuir", Revenue = 720000m, UnitsSold = 45, Category = "Mode" }
            },

            GeographicData = new List<GeographicPerformanceDto>
            {
                new() { Region = "Kaloum", Revenue = 4200000m, Orders = 387, Customers = 234, GrowthRate = 12.5m },
                new() { Region = "Matam", Revenue = 3800000m, Orders = 342, Customers = 198, GrowthRate = 8.9m },
                new() { Region = "Dixinn", Revenue = 2900000m, Orders = 267, Customers = 156, GrowthRate = 15.2m },
                new() { Region = "Ratoma", Revenue = 2600000m, Orders = 198, Customers = 143, GrowthRate = 6.7m },
                new() { Region = "Matoto", Revenue = 2250000m, Orders = 187, Customers = 125, GrowthRate = 9.3m }
            }
        };
    }

    private async Task LoadPredictions()
    {
        try
        {
            // Charger les vraies prédictions depuis l'API Analytics
            salesPredictions = await AnalyticsService.GetSalesPredictionsAsync(30);
            churnAnalysis = await AnalyticsService.GetCustomerChurnAnalysisAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du chargement des prédictions");

            // Fallback avec données vides
            salesPredictions = new List<SalesPredictionDto>();
            churnAnalysis = new CustomerChurnAnalysisDto
            {
                OverallChurnRate = 0,
                HighRiskCustomers = new List<ChurnRiskCustomerDto>(),
                ChurnFactors = new List<ChurnFactorDto>()
            };
        }
    }

    private async Task LoadRealTimeTrends()
    {
        realTimeTrends = new RealTimeTrendsDto
        {
            TrendingProducts = new List<TrendingProductDto>
            {
                new() { Name = "Smartphone Tecno", TrendScore = (int)9.2, Category = "Électronique" },
                new() { Name = "Chaussures Adidas", TrendScore = (int)8.7, Category = "Sports" },
                new() { Name = "Robe de soirée", TrendScore = (int)8.1, Category = "Mode" },
                new() { Name = "Casque audio", TrendScore = (int)7.9, Category = "Électronique" },
                new() { Name = "Sac voyage", TrendScore = (int)7.3, Category = "Mode" }
            },

            SearchTrends = new List<SearchTrendDto>
            {
                new() { Term = "samsung galaxy", Volume = 342 },
                new() { Term = "nike air", Volume = 287 },
                new() { Term = "robe africaine", Volume = 234 },
                new() { Term = "ordinateur portable", Volume = 198 },
                new() { Term = "chaussures femme", Volume = 176 }
            },

            ActiveAlerts = new List<AlertDto>
            {
                new() { Title = "Stock faible", Message = "5 produits en rupture", Severity = "warning" },
                new() { Title = "Pic de trafic", Message = "Trafic +150% depuis 1h", Severity = "info" },
                new() { Title = "Commande importante", Message = "Commande de 2.5M GNF reçue", Severity = "success" }
            }
        };
    }

    private async Task LoadCustomerSegmentation()
    {
        customerSegmentation = new AICustomerSegmentationDto
        {
            Segments = new List<CustomerSegmentDto>
            {
                new() { Name = "VIP Premium", Description = "Gros acheteurs fidèles", CustomerCount = 89, Percentage = 10.4m, AverageOrderValue = 450000m, TotalRevenue = 8900000m },
                new() { Name = "Réguliers", Description = "Achats fréquents moyens", CustomerCount = 245, Percentage = 28.6m, AverageOrderValue = 180000m, TotalRevenue = 6200000m },
                new() { Name = "Occasionnels", Description = "Achats ponctuels", CustomerCount = 367, Percentage = 42.9m, AverageOrderValue = 85000m, TotalRevenue = 2100000m },
                new() { Name = "Nouveaux", Description = "Premiers achats", CustomerCount = 155, Percentage = 18.1m, AverageOrderValue = 120000m, TotalRevenue = 980000m }
            }
        };
    }

    private async Task LoadBusinessOptimizations()
    {
        businessOptimizations = new List<BusinessOptimizationDto>
        {
            new() { Area = "Pricing", Recommendation = "Optimiser les prix des produits électroniques", EstimatedImpact = 850000, Priority = "high" },
            new() { Area = "Inventory", Recommendation = "Réduire le sur-stock en maison & jardin", EstimatedImpact = 320000, Priority = "medium" },
            new() { Area = "Marketing", Recommendation = "Cibler davantage les clients VIP", EstimatedImpact = 1200000, Priority = "high" },
            new() { Area = "Retention", Recommendation = "Programme de fidélité pour clients à risque", EstimatedImpact = 450000, Priority = "medium" }
        };
    }

    private async Task OnPeriodChanged(ChangeEventArgs e)
    {
        selectedPeriod = e.Value?.ToString() ?? "30d";
        await LoadDashboardData();
        await InitializeCharts();
    }

    private async Task RefreshData()
    {
        await LoadDashboardData();
        await InitializeCharts();
    }

    private async Task ExportReport()
    {
        try
        {
            // Implémenter l'export des données
            await JSRuntime.InvokeVoidAsync("downloadAnalyticsReport", dashboard);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'export");
        }
    }

    private async Task GenerateAIInsights()
    {
        try
        {
            // Générer de nouveaux insights IA
            aiInsights = new List<AIInsightDto>
            {
                new() { Title = "Opportunité de vente", Description = "Les ventes d'électronique augmentent de 15%, augmenter le stock", Type = "opportunity", Confidence = 0.87m },
                new() { Title = "Risque de stock", Description = "Attention au sur-stock en maison & jardin (-2% de croissance)", Type = "risk", Confidence = 0.92m },
                new() { Title = "Tendance émergente", Description = "Forte demande pour les produits de sport (+22%)", Type = "trend", Confidence = 0.78m }
            };

            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de la génération d'insights");
        }
    }

    private async Task InitializeCharts()
    {
        try
        {
            if (dashboard?.RevenueTimeSeries?.Any() == true)
            {
                await JSRuntime.InvokeVoidAsync("initializeRevenueChart", "revenueChart", dashboard.RevenueTimeSeries);
            }

            if (salesPredictions?.Any() == true)
            {
                await JSRuntime.InvokeVoidAsync("initializePredictionChart", "salesPredictionChart", salesPredictions);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'initialisation des graphiques");
        }
    }

    // Méthodes utilitaires
    private (DateTime fromDate, DateTime toDate) GetDateRange(string period)
    {
        var toDate = DateTime.UtcNow;
        var fromDate = period switch
        {
            "7d" => toDate.AddDays(-7),
            "30d" => toDate.AddDays(-30),
            "3m" => toDate.AddMonths(-3),
            "6m" => toDate.AddMonths(-6),
            "1y" => toDate.AddYears(-1),
            _ => toDate.AddDays(-30)
        };

        return (fromDate, toDate);
    }

    private string FormatCurrency(decimal amount)
    {
        return amount.ToString("N0", new CultureInfo("fr-GN")) + " GNF";
    }

    private string GetChangeClass(double change) => change switch
    {
        > 0 => "positive",
        < 0 => "negative",
        _ => "neutral"
    };

    private string GetChangeIcon(double change) => change switch
    {
        > 0 => "↗️",
        < 0 => "↘️",
        _ => "➡️"
    };

    private string GetNPSClass(decimal nps) => nps switch
    {
        >= 70 => "excellent",
        >= 50 => "good",
        >= 30 => "average",
        _ => "poor"
    };

    private string GetNPSText(decimal nps) => nps switch
    {
        >= 70 => "Excellent",
        >= 50 => "Bon",
        >= 30 => "Moyen",
        _ => "À améliorer"
    };

    private string GetChurnClass(double churnRate) => churnRate switch
    {
        <= 3 => "low",
        <= 7 => "medium",
        _ => "high"
    };

    private string GetInsightIcon(string type) => type switch
    {
        "opportunity" => "🚀",
        "risk" => "⚠️",
        "trend" => "📈",
        "anomaly" => "🔍",
        _ => "💡"
    };

    private string GetAlertIcon(string severity) => severity switch
    {
        "high" or "critical" => "🚨",
        "warning" => "⚠️",
        "info" => "ℹ️",
        "success" => "✅",
        _ => "📢"
    };

    private double GetCategoryPercentage(decimal revenue)
    {
        if (dashboard?.CategoryPerformance?.Any() != true) return 0;
        var maxRevenue = dashboard.CategoryPerformance.Max(c => c.Revenue);
        return maxRevenue > 0 ? (double)(revenue / maxRevenue * 100) : 0;
    }
}

<!-- Styles intégrés pour le tableau de bord analytics -->
<style>
.analytics-dashboard {
    padding: 20px;
    background: #f8f9ff;
    min-height: 100vh;
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 25px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    color: white;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.header-title h1 {
    margin: 0 0 5px 0;
    font-size: 2rem;
    font-weight: 600;
}

.header-title p {
    margin: 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

.header-controls {
    display: flex;
    gap: 20px;
    align-items: center;
}

.date-filters {
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-filters label {
    font-weight: 500;
}

.action-buttons {
    display: flex;
    gap: 10px;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.kpi-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.kpi-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.kpi-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

.kpi-card.revenue { border-left-color: #10b981; }
.kpi-card.orders { border-left-color: #3b82f6; }
.kpi-card.customers { border-left-color: #8b5cf6; }
.kpi-card.aov { border-left-color: #f59e0b; }
.kpi-card.clv { border-left-color: #ef4444; }
.kpi-card.nps { border-left-color: #06b6d4; }

.kpi-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.kpi-content h3 {
    margin: 0 0 8px 0;
    color: #6b7280;
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.kpi-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 5px;
}

.kpi-change {
    font-size: 0.9rem;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 20px;
}

.kpi-change.positive { background: #dcfce7; color: #166534; }
.kpi-change.negative { background: #fef2f2; color: #dc2626; }
.kpi-change.neutral { background: #f3f4f6; color: #6b7280; }

.predictions-section {
    margin-bottom: 40px;
}

.predictions-section h2 {
    color: #1f2937;
    margin-bottom: 25px;
    font-size: 1.6rem;
    font-weight: 600;
}

.predictions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
}

.prediction-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    border: 1px solid #e5e7eb;
}

.prediction-card h3 {
    margin: 0 0 20px 0;
    color: #1f2937;
    font-size: 1.2rem;
    font-weight: 600;
}

.prediction-chart {
    height: 200px;
    margin-bottom: 20px;
}

.prediction-summary {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.prediction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f3f4f6;
}

.prediction-date {
    font-weight: 500;
    color: #6b7280;
}

.prediction-value {
    font-weight: 600;
    color: #1f2937;
}

.prediction-confidence {
    background: #eff6ff;
    color: #2563eb;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.churn-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.churn-rate, .high-risk-customers {
    text-align: center;
    padding: 15px;
    background: #f8fafc;
    border-radius: 10px;
}

.churn-value {
    font-size: 1.8rem;
    font-weight: 700;
    margin-top: 5px;
}

.churn-value.low { color: #10b981; }
.churn-value.medium { color: #f59e0b; }
.churn-value.high { color: #ef4444; }

.risk-count {
    font-size: 1.8rem;
    font-weight: 700;
    color: #ef4444;
    display: block;
    margin-top: 5px;
}

.churn-factors {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.factor-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.factor-name {
    font-size: 0.9rem;
    color: #6b7280;
    flex: 1;
}

.importance-bar {
    width: 80px;
    height: 6px;
    background: #e5e7eb;
    border-radius: 3px;
    position: relative;
    margin-left: 10px;
}

.importance-bar::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    background: #3b82f6;
    border-radius: 3px;
}

.insights-card {
    grid-column: span 2;
}

.insights-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.insight-item {
    padding: 15px;
    border-radius: 10px;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
}

.insight-item:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.insight-item.opportunity { background: #f0fdf4; border-color: #22c55e; }
.insight-item.risk { background: #fef2f2; border-color: #ef4444; }
.insight-item.trend { background: #eff6ff; border-color: #3b82f6; }
.insight-item.anomaly { background: #fefce8; border-color: #eab308; }

.insight-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.insight-title {
    font-weight: 600;
    color: #1f2937;
    flex: 1;
}

.insight-confidence {
    background: rgba(0,0,0,0.1);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.insight-description {
    color: #6b7280;
    margin-bottom: 10px;
    line-height: 1.5;
}

.insight-recommendations {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.recommendation-tag {
    background: #f3f4f6;
    color: #4b5563;
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.charts-section {
    margin-bottom: 40px;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
}

.chart-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
}

.chart-card h3 {
    margin: 0 0 20px 0;
    color: #1f2937;
    font-size: 1.2rem;
    font-weight: 600;
}

.chart-container {
    height: 300px;
}

.category-performance, .top-products, .geographic-performance, .customer-segments {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.category-item, .product-item, .region-item, .segment-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8fafc;
    border-radius: 10px;
    transition: all 0.2s ease;
}

.category-item:hover, .product-item:hover, .region-item:hover, .segment-item:hover {
    background: #f1f5f9;
    transform: translateX(3px);
}

.product-rank {
    width: 30px;
    height: 30px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.category-bar, .importance-bar {
    height: 6px;
    background: #e5e7eb;
    border-radius: 3px;
    flex: 1;
    overflow: hidden;
}

.category-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 3px;
    transition: width 0.8s ease;
}

.real-time-section {
    margin-bottom: 40px;
}

.real-time-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.trend-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    border: 1px solid #e5e7eb;
}

.trending-products, .search-trends, .active-alerts {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.trending-item, .search-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f3f4f6;
}

.alert-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.alert-item.warning { background: #fefce8; border-color: #eab308; }
.alert-item.info { background: #eff6ff; border-color: #3b82f6; }
.alert-item.success { background: #f0fdf4; border-color: #22c55e; }

.recommendations-section {
    margin-bottom: 40px;
}

.recommendations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
}

.recommendation-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    border-left: 4px solid #e5e7eb;
    transition: all 0.3s ease;
}

.recommendation-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

.recommendation-card.high { border-left-color: #ef4444; }
.recommendation-card.medium { border-left-color: #f59e0b; }
.recommendation-card.low { border-left-color: #10b981; }

.recommendation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.recommendation-area {
    font-weight: 600;
    color: #1f2937;
    font-size: 1.1rem;
}

.recommendation-priority {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.recommendation-priority.high { background: #fef2f2; color: #dc2626; }
.recommendation-priority.medium { background: #fefce8; color: #ca8a04; }
.recommendation-priority.low { background: #f0fdf4; color: #166534; }

.recommendation-content p {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 10px;
}

.recommendation-impact {
    color: #1f2937;
    font-weight: 600;
}

.recommendation-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-top: 15px;
}

.action-tag {
    background: #f3f4f6;
    color: #4b5563;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Responsive Design */
@@media (max-width: 768px) {
    .analytics-dashboard {
        padding: 10px;
    }

    .dashboard-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .header-controls {
        flex-direction: column;
        gap: 15px;
        width: 100%;
    }

    .kpi-section {
        grid-template-columns: 1fr;
    }

    .predictions-grid, .charts-grid, .real-time-grid, .recommendations-grid {
        grid-template-columns: 1fr;
    }

    .insights-card {
        grid-column: span 1;
    }

    .kpi-card {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .category-item, .product-item, .region-item, .segment-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

/* Animations d'entrée */
@@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.kpi-card, .prediction-card, .chart-card, .trend-card, .recommendation-card {
    animation: fadeInUp 0.6s ease-out;
}

.error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    text-align: center;
}

.error-container p {
    font-size: 1.2rem;
    color: #6b7280;
    margin-bottom: 20px;
}
</style>