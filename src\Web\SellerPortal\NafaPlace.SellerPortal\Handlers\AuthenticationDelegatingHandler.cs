using System.Net.Http.Headers;
using Blazored.LocalStorage;

namespace NafaPlace.SellerPortal.Handlers;

public class AuthenticationDelegatingHandler : DelegatingHandler
{
    private readonly ILocalStorageService _localStorage;
    private readonly ILogger<AuthenticationDelegatingHandler> _logger;

    public AuthenticationDelegatingHandler(
        ILocalStorageService localStorage,
        ILogger<AuthenticationDelegatingHandler> logger)
    {
        _localStorage = localStorage;
        _logger = logger;
    }

    protected override async Task<HttpResponseMessage> SendAsync(
        HttpRequestMessage request,
        CancellationToken cancellationToken)
    {
        try
        {
            // Récupérer le token depuis le localStorage
            var token = await _localStorage.GetItemAsync<string>("authToken");

            if (!string.IsNullOrEmpty(token))
            {
                // Nettoyer le token des guillemets éventuels
                token = token.Trim('"');

                // Ajouter le token à l'en-tête Authorization
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);

                _logger.LogDebug("Token JWT ajouté à la requête: {Url}", request.RequestUri);
            }
            else
            {
                _logger.LogWarning("Aucun token JWT trouvé dans le localStorage pour la requête: {Url}", request.RequestUri);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ajout du token JWT à la requête: {Url}", request.RequestUri);
        }

        return await base.SendAsync(request, cancellationToken);
    }
}

