@page "/chat/support"
@using NafaPlace.SellerPortal.Services
@inject IChatEcommerceService ChatService
@inject ChatSignalRService SignalRService
@inject IJSRuntime JSRuntime
@inject ILogger<SupportClient> Logger
@inject NavigationManager Navigation
@implements IAsyncDisposable


<PageTitle>Support Client - NafaPlace Seller</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-headset me-2"></i>
                        Support Client
                    </h4>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary btn-sm" @onclick="RefreshData">
                            <i class="fas fa-sync-alt me-1"></i>
                            Actualiser
                        </button>
                        <div class="badge @(isHealthy ? "bg-success" : "bg-danger")">
                            @(isHealthy ? "Service en ligne" : "Service hors ligne")
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if (isLoading)
                    {
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                            <p class="mt-2">Chargement des données...</p>
                        </div>
                    }
                    else
                    {
                        <!-- Tabs Navigation -->
                        <ul class="nav nav-tabs mb-3" id="supportTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link @(activeTab == "conversations" ? "active" : "")"
                                        @onclick="@(() => SetActiveTab("conversations"))" type="button">
                                    <i class="fas fa-comments me-1"></i>
                                    Conversations (@conversations.Count)
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link @(activeTab == "faqs" ? "active" : "")"
                                        @onclick="@(() => SetActiveTab("faqs"))" type="button">
                                    <i class="fas fa-question-circle me-1"></i>
                                    FAQs (@faqs.Count)
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link @(activeTab == "quickreplies" ? "active" : "")"
                                        @onclick="@(() => SetActiveTab("quickreplies"))" type="button">
                                    <i class="fas fa-bolt me-1"></i>
                                    Réponses Rapides (@quickReplies.Count)
                                </button>
                            </li>
                        </ul>

                        <!-- Tab Content -->
                        <div class="tab-content">
                            <!-- Conversations Tab -->
                            @if (activeTab == "conversations")
                            {
                                <div class="tab-pane fade show active">
                                    <!-- Filtres et recherche -->
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <div class="row g-3">
                                                <div class="col-md-4">
                                                    <div class="input-group">
                                                        <span class="input-group-text">
                                                            <i class="fas fa-search"></i>
                                                        </span>
                                                        <input type="text" class="form-control"
                                                               placeholder="Rechercher par client ou sujet..."
                                                               @bind="conversationSearchTerm"
                                                               @bind:event="oninput"
                                                               @onkeyup="FilterConversations" />
                                                        @if (!string.IsNullOrWhiteSpace(conversationSearchTerm))
                                                        {
                                                            <button class="btn btn-outline-secondary" @onclick="ClearConversationSearch">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                                        }
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <select class="form-select" @bind="conversationStatusFilter" @bind:after="FilterConversations">
                                                        <option value="">Tous les statuts</option>
                                                        <option value="Open">Ouvert</option>
                                                        <option value="InProgress">En cours</option>
                                                        <option value="Waiting">En attente</option>
                                                        <option value="Resolved">Résolu</option>
                                                        <option value="Closed">Fermé</option>
                                                        <option value="Escalated">Escaladé</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-3">
                                                    <select class="form-select" @bind="conversationPriorityFilter" @bind:after="FilterConversations">
                                                        <option value="">Toutes les priorités</option>
                                                        <option value="Low">Basse</option>
                                                        <option value="Normal">Normale</option>
                                                        <option value="High">Haute</option>
                                                        <option value="Urgent">Urgente</option>
                                                        <option value="Critical">Critique</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-2">
                                                    <select class="form-select" @bind="conversationSortOrder" @bind:after="FilterConversations">
                                                        <option value="recent">Plus récent</option>
                                                        <option value="oldest">Plus ancien</option>
                                                        <option value="unread">Non lus</option>
                                                    </select>
                                                </div>
                                            </div>
                                            @if (filteredConversations.Count != conversations.Count)
                                            {
                                                <div class="mt-2">
                                                    <small class="text-muted">
                                                        <i class="fas fa-filter me-1"></i>
                                                        @filteredConversations.Count résultat(s) sur @conversations.Count
                                                    </small>
                                                </div>
                                            }
                                        </div>
                                    </div>

                                    @if (filteredConversations.Any())
                                    {
                                        <div class="row">
                                            @foreach (var conversation in filteredConversations)
                                            {
                                                <div class="col-md-6 col-lg-4 mb-3">
                                                    <div class="card h-100 @(conversation.UnreadCount > 0 ? "border-primary" : "")">
                                                        <div class="card-body">
                                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                                <h6 class="card-title mb-0">
                                                                    @conversation.CustomerName
                                                                    @if (conversation.UnreadCount > 0)
                                                                    {
                                                                        <span class="badge bg-primary ms-2">@conversation.UnreadCount</span>
                                                                    }
                                                                </h6>
                                                                <span class="badge bg-@GetPriorityColor(conversation.Priority)">
                                                                    @GetPriorityLabel(conversation.Priority)
                                                                </span>
                                                            </div>
                                                            <p class="card-text text-muted small mb-2">
                                                                <i class="fas fa-comment-dots me-1"></i>
                                                                @conversation.Subject
                                                            </p>
                                                            @if (!string.IsNullOrEmpty(conversation.ProductName))
                                                            {
                                                                <p class="card-text small mb-2">
                                                                    <i class="fas fa-box me-1 text-primary"></i>
                                                                    @conversation.ProductName
                                                                </p>
                                                            }
                                                            @if (conversation.OrderId.HasValue)
                                                            {
                                                                <p class="card-text small mb-2">
                                                                    <i class="fas fa-shopping-cart me-1 text-success"></i>
                                                                    Commande #@conversation.OrderId
                                                                </p>
                                                            }

                                                            <div class="d-flex justify-content-between align-items-center mt-2">
                                                                <small class="text-muted">
                                                                    <i class="fas fa-clock me-1"></i>
                                                                    @GetRelativeTime(conversation.LastMessageAt ?? conversation.CreatedAt)
                                                                </small>
                                                                <span class="badge bg-@GetStatusColor(conversation.Status)">
                                                                    @GetStatusLabel(conversation.Status)
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <div class="card-footer">
                                                            <button class="btn btn-primary btn-sm w-100"
                                                                    @onclick="@(() => OpenConversation(conversation.Id))">
                                                                <i class="fas fa-eye me-1"></i>
                                                                Voir la conversation
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="text-center py-4">
                                            <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">Aucune conversation</h5>
                                            <p class="text-muted">
                                                @if (conversations.Any())
                                                {
                                                    <span>Aucune conversation ne correspond aux filtres sélectionnés.</span>
                                                }
                                                else
                                                {
                                                    <span>Les conversations avec vos clients apparaîtront ici.</span>
                                                }
                                            </p>
                                        </div>
                                    }
                                </div>
                            }

                            <!-- FAQs Tab -->
                            @if (activeTab == "faqs")
                            {
                                <div class="tab-pane fade show active">
                                    @if (faqs.Any())
                                    {
                                        <!-- Category Filter -->
                                        <div class="mb-3">
                                            <select class="form-select" @onchange="OnFAQCategoryChanged">
                                                <option value="">Toutes les catégories</option>
                                                @foreach (var category in faqCategories)
                                                {
                                                    <option value="@category">@category</option>
                                                }
                                            </select>
                                        </div>

                                        <!-- FAQs List -->
                                        <div class="accordion" id="faqAccordion">
                                            @foreach (var faq in faqs.Where(f => string.IsNullOrEmpty(selectedFAQCategory) || f.Category == selectedFAQCategory))
                                            {
                                                <div class="accordion-item">
                                                    <h2 class="accordion-header" id="<EMAIL>">
                                                        <button class="accordion-button collapsed" type="button" 
                                                                data-bs-toggle="collapse" data-bs-target="#<EMAIL>">
                                                            <div class="d-flex justify-content-between w-100 me-3">
                                                                <span>@faq.Question</span>
                                                                <div class="d-flex gap-2">
                                                                    <span class="badge bg-secondary">@faq.Category</span>
                                                                    <span class="badge bg-info">@faq.ViewCount vues</span>
                                                                </div>
                                                            </div>
                                                        </button>
                                                    </h2>
                                                    <div id="<EMAIL>" class="accordion-collapse collapse" 
                                                         data-bs-parent="#faqAccordion">
                                                        <div class="accordion-body">
                                                            <p>@faq.Answer</p>
                                                            <div class="d-flex justify-content-between align-items-center mt-3">
                                                                <div class="d-flex gap-2">
                                                                    <span class="badge bg-success">@faq.HelpfulCount utile</span>
                                                                    <span class="badge bg-warning">@faq.NotHelpfulCount pas utile</span>
                                                                </div>
                                                                <small class="text-muted">
                                                                    @faq.ViewCount vues
                                                                </small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="text-center py-4">
                                            <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">Aucune FAQ disponible</h5>
                                            <p class="text-muted">Les questions fréquemment posées apparaîtront ici.</p>
                                        </div>
                                    }
                                </div>
                            }

                            <!-- Quick Replies Tab -->
                            @if (activeTab == "quickreplies")
                            {
                                <div class="tab-pane fade show active">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5>Mes Réponses Rapides</h5>
                                        <button class="btn btn-primary btn-sm" @onclick="@(() => ShowCreateQuickReplyModal())">
                                            <i class="fas fa-plus me-1"></i>
                                            Nouvelle réponse
                                        </button>
                                    </div>

                                    <!-- Barre de recherche et filtres -->
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <div class="row g-3">
                                                <div class="col-md-6">
                                                    <div class="input-group">
                                                        <span class="input-group-text">
                                                            <i class="fas fa-search"></i>
                                                        </span>
                                                        <input type="text" class="form-control"
                                                               placeholder="Rechercher par titre ou contenu..."
                                                               @bind="quickReplySearchTerm"
                                                               @bind:event="oninput"
                                                               @onkeyup="FilterQuickReplies" />
                                                        @if (!string.IsNullOrWhiteSpace(quickReplySearchTerm))
                                                        {
                                                            <button class="btn btn-outline-secondary" @onclick="ClearQuickReplySearch">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                                        }
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <select class="form-select" @bind="quickReplyCategoryFilter" @bind:after="FilterQuickReplies">
                                                        <option value="">Toutes les catégories</option>
                                                        <option value="Livraison">Livraison</option>
                                                        <option value="Paiement">Paiement</option>
                                                        <option value="Retours">Retours</option>
                                                        <option value="Produits">Produits</option>
                                                        <option value="Support">Support</option>
                                                        <option value="Autre">Autre</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-2">
                                                    <select class="form-select" @bind="quickReplySortOrder" @bind:after="FilterQuickReplies">
                                                        <option value="recent">Plus récent</option>
                                                        <option value="oldest">Plus ancien</option>
                                                        <option value="title">Titre A-Z</option>
                                                    </select>
                                                </div>
                                            </div>
                                            @if (filteredQuickReplies.Count != quickReplies.Count)
                                            {
                                                <div class="mt-2">
                                                    <small class="text-muted">
                                                        <i class="fas fa-filter me-1"></i>
                                                        @filteredQuickReplies.Count résultat(s) sur @quickReplies.Count
                                                    </small>
                                                </div>
                                            }
                                        </div>
                                    </div>

                                    @if (filteredQuickReplies.Any())
                                    {
                                        <div class="row">
                                            @foreach (var reply in filteredQuickReplies)
                                            {
                                                <div class="col-md-6 col-lg-4 mb-3">
                                                    <div class="card h-100">
                                                        <div class="card-body">
                                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                                <h6 class="card-title">@reply.Title</h6>
                                                                <span class="badge bg-secondary">@reply.Category</span>
                                                            </div>
                                                            <p class="card-text small">@reply.Content</p>
                                                            <small class="text-muted">
                                                                Catégorie: @reply.Category
                                                            </small>
                                                        </div>
                                                        <div class="card-footer d-flex justify-content-between">
                                                            <button class="btn btn-outline-primary btn-sm"
                                                                    @onclick="@(() => CopyToClipboard(reply.Content))">
                                                                <i class="fas fa-copy me-1"></i>
                                                                Copier
                                                            </button>
                                                            <div>
                                                                <button class="btn btn-outline-secondary btn-sm me-1"
                                                                        @onclick="@(() => ShowEditQuickReplyModal(reply))">
                                                                    <i class="fas fa-edit"></i>
                                                                </button>
                                                                <button class="btn btn-outline-danger btn-sm"
                                                                        @onclick="@(() => ShowDeleteQuickReplyConfirm(reply))">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="text-center py-4">
                                            <i class="fas fa-bolt fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">Aucune réponse rapide</h5>
                                            <p class="text-muted">Créez des réponses rapides pour gagner du temps dans vos conversations.</p>
                                            <button class="btn btn-primary" @onclick="@(() => ShowCreateQuickReplyModal())">
                                                <i class="fas fa-plus me-1"></i>
                                                Créer ma première réponse
                                            </button>
                                        </div>
                                    }
                                </div>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour créer une réponse rapide -->
@if (showQuickReplyModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-bolt me-2"></i>
                        @(isEditingQuickReply ? "Modifier la Réponse Rapide" : "Nouvelle Réponse Rapide")
                    </h5>
                    <button type="button" class="btn-close" @onclick="CloseQuickReplyModal"></button>
                </div>
                <div class="modal-body">
                    @if (!string.IsNullOrEmpty(quickReplyError))
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            @quickReplyError
                            <button type="button" class="btn-close" @onclick="@(() => quickReplyError = "")"></button>
                        </div>
                    }

                    <div class="mb-3">
                        <label for="quickReplyTitle" class="form-label">Titre <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="quickReplyTitle"
                               @bind="newQuickReply.Title"
                               placeholder="Ex: Délai de livraison standard" />
                    </div>

                    <div class="mb-3">
                        <label for="quickReplyContent" class="form-label">Contenu <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="quickReplyContent" rows="4"
                                  @bind="newQuickReply.Content"
                                  placeholder="Ex: Nos délais de livraison sont de 2-5 jours ouvrables pour Conakry..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="quickReplyCategory" class="form-label">Catégorie <span class="text-danger">*</span></label>
                        <select class="form-select" id="quickReplyCategory" @bind="newQuickReply.Category">
                            <option value="">-- Sélectionner une catégorie --</option>
                            <option value="Livraison">Livraison</option>
                            <option value="Paiement">Paiement</option>
                            <option value="Retours">Retours</option>
                            <option value="Produits">Produits</option>
                            <option value="Support">Support</option>
                            <option value="Autre">Autre</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="quickReplyTags" class="form-label">Tags (séparés par des virgules)</label>
                        <input type="text" class="form-control" id="quickReplyTags"
                               @bind="newQuickReplyTags"
                               placeholder="Ex: livraison, délai, conakry" />
                        <small class="text-muted">Les tags aident à organiser et rechercher vos réponses rapides</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="CloseQuickReplyModal" disabled="@isSavingQuickReply">
                        <i class="fas fa-times me-1"></i>
                        Annuler
                    </button>
                    <button type="button" class="btn btn-primary" @onclick="SaveQuickReply" disabled="@isSavingQuickReply">
                        @if (isSavingQuickReply)
                        {
                            <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                            <span>Enregistrement...</span>
                        }
                        else
                        {
                            <i class="fas fa-save me-1"></i>
                            <span>Enregistrer</span>
                        }
                    </button>
                </div>
            </div>
        </div>
    </div>
}

<!-- Modal de confirmation de suppression -->
@if (showDeleteConfirm)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Confirmer la suppression
                    </h5>
                    <button type="button" class="btn-close btn-close-white" @onclick="CloseDeleteConfirm"></button>
                </div>
                <div class="modal-body">
                    <p>Êtes-vous sûr de vouloir supprimer cette réponse rapide ?</p>
                    @if (quickReplyToDelete != null)
                    {
                        <div class="alert alert-warning">
                            <strong>@quickReplyToDelete.Title</strong>
                            <p class="mb-0 small">@quickReplyToDelete.Content</p>
                        </div>
                    }
                    <p class="text-danger mb-0"><strong>Cette action est irréversible.</strong></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="CloseDeleteConfirm" disabled="@isDeletingQuickReply">
                        <i class="fas fa-times me-1"></i>
                        Annuler
                    </button>
                    <button type="button" class="btn btn-danger" @onclick="DeleteQuickReply" disabled="@isDeletingQuickReply">
                        @if (isDeletingQuickReply)
                        {
                            <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                            <span>Suppression...</span>
                        }
                        else
                        {
                            <i class="fas fa-trash me-1"></i>
                            <span>Supprimer</span>
                        }
                    </button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private bool isLoading = true;
    private bool isHealthy = false;
    private string activeTab = "conversations";
    private string selectedFAQCategory = "";

    private List<ConversationDto> conversations = new();
    private List<ConversationDto> filteredConversations = new();
    private List<FAQDto> faqs = new();
    private List<QuickReplyDto> quickReplies = new();
    private List<QuickReplyDto> filteredQuickReplies = new();
    private List<string> faqCategories = new();

    // Filtres et recherche pour les conversations
    private string conversationSearchTerm = "";
    private string conversationStatusFilter = "";
    private string conversationPriorityFilter = "";
    private string conversationSortOrder = "recent";

    // Filtres et recherche pour les réponses rapides
    private string quickReplySearchTerm = "";
    private string quickReplyCategoryFilter = "";
    private string quickReplySortOrder = "recent";

    // Modal pour créer/modifier une réponse rapide
    private bool showQuickReplyModal = false;
    private bool isSavingQuickReply = false;
    private bool isEditingQuickReply = false;
    private string quickReplyError = "";
    private string newQuickReplyTags = "";
    private CreateQuickReplyDto newQuickReply = new();
    private int editingQuickReplyId = 0;

    // Modal de confirmation de suppression
    private bool showDeleteConfirm = false;
    private bool isDeletingQuickReply = false;
    private QuickReplyDto? quickReplyToDelete = null;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
        await InitializeSignalR();
    }

    private async Task InitializeSignalR()
    {
        try
        {
            // Initialiser la connexion SignalR
            await SignalRService.InitializeAsync();

            // S'abonner aux événements
            SignalRService.OnMessageReceived += HandleMessageReceived;
            SignalRService.OnUserOnline += HandleUserOnline;
            SignalRService.OnUserOffline += HandleUserOffline;
            SignalRService.OnNewConversation += HandleNewConversation;

            Logger.LogInformation("SignalR initialisé avec succès");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'initialisation de SignalR");
        }
    }

    private void HandleMessageReceived(MessageReceivedEventArgs message)
    {
        Logger.LogInformation("Nouveau message reçu dans la conversation {ConversationId}", message.ConversationId);

        // Recharger les conversations pour mettre à jour le compteur de messages non lus
        InvokeAsync(async () =>
        {
            await LoadConversations();
            StateHasChanged();
        });
    }

    private void HandleUserOnline(string userId)
    {
        Logger.LogInformation("Utilisateur {UserId} est en ligne", userId);
        // TODO: Mettre à jour l'UI pour afficher l'utilisateur en ligne
    }

    private void HandleUserOffline(string userId)
    {
        Logger.LogInformation("Utilisateur {UserId} est hors ligne", userId);
        // TODO: Mettre à jour l'UI pour afficher l'utilisateur hors ligne
    }

    private void HandleNewConversation(int conversationId)
    {
        Logger.LogInformation("Nouvelle conversation créée: {ConversationId}", conversationId);

        // Recharger les conversations
        InvokeAsync(async () =>
        {
            await LoadConversations();
            StateHasChanged();
        });
    }

    private async Task LoadData()
    {
        isLoading = true;

        try
        {
            // Check service health
            isHealthy = await ChatService.IsHealthyAsync();

            if (isHealthy)
            {
                // Load all data in parallel
                var tasks = new[]
                {
                    LoadConversations(),
                    LoadFAQs(),
                    LoadQuickReplies(),
                    LoadFAQCategories()
                };

                await Task.WhenAll(tasks);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du chargement des données");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task LoadConversations()
    {
        try
        {
            Console.WriteLine("[SupportClient] 🔍 Début du chargement des conversations...");
            var sellerId = await GetCurrentSellerIdAsync();
            Console.WriteLine($"[SupportClient] SellerId récupéré: {sellerId}");

            if (sellerId > 0)
            {
                Console.WriteLine($"[SupportClient] 📞 Appel de l'API pour récupérer les conversations du vendeur {sellerId}...");
                conversations = await ChatService.GetConversationsAsync(sellerId.ToString());
                Console.WriteLine($"[SupportClient] ✅ {conversations.Count} conversation(s) récupérée(s)");
                FilterConversations();
                Console.WriteLine($"[SupportClient] ✅ {filteredConversations.Count} conversation(s) après filtrage");
            }
            else
            {
                Console.WriteLine("[SupportClient] ❌ SellerId invalide (0)");
                Logger.LogWarning("Impossible de récupérer le SellerId");
                conversations = new List<ConversationDto>();
                filteredConversations = new List<ConversationDto>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[SupportClient] ❌ ERREUR: {ex.Message}");
            Logger.LogError(ex, "Erreur lors du chargement des conversations");
            conversations = new List<ConversationDto>();
            filteredConversations = new List<ConversationDto>();
        }
    }

    private async Task<int> GetCurrentSellerIdAsync()
    {
        try
        {
            Console.WriteLine("[SupportClient] 🔑 Récupération du token depuis localStorage...");
            var token = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "authToken");
            if (string.IsNullOrEmpty(token))
            {
                Console.WriteLine("[SupportClient] ❌ Token vide ou null");
                return 0;
            }

            Console.WriteLine($"[SupportClient] ✅ Token récupéré (longueur: {token.Length})");
            token = token.Trim('"');
            var handler = new System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler();
            var jsonToken = handler.ReadJwtToken(token);

            Console.WriteLine($"[SupportClient] 🔍 Recherche du claim 'SellerId' dans le token...");
            var sellerIdClaim = jsonToken.Claims.FirstOrDefault(x => x.Type == "SellerId" || x.Type == "sellerId");
            if (sellerIdClaim != null && int.TryParse(sellerIdClaim.Value, out int sellerId))
            {
                Console.WriteLine($"[SupportClient] ✅ SellerId trouvé: {sellerId}");
                return sellerId;
            }

            Console.WriteLine("[SupportClient] ❌ Claim 'SellerId' non trouvé dans le token");
            return 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[SupportClient] ❌ ERREUR lors de la récupération du SellerId: {ex.Message}");
            Logger.LogError(ex, "Erreur lors de la récupération du SellerId");
            return 0;
        }
    }

    private void FilterConversations()
    {
        var filtered = conversations.AsEnumerable();

        // Filtre par recherche (nom du client ou sujet)
        if (!string.IsNullOrWhiteSpace(conversationSearchTerm))
        {
            var searchLower = conversationSearchTerm.ToLower();
            filtered = filtered.Where(c =>
                c.CustomerName.ToLower().Contains(searchLower) ||
                c.Subject.ToLower().Contains(searchLower));
        }

        // Filtre par statut
        if (!string.IsNullOrWhiteSpace(conversationStatusFilter))
        {
            filtered = filtered.Where(c => c.Status == conversationStatusFilter);
        }

        // Filtre par priorité
        if (!string.IsNullOrWhiteSpace(conversationPriorityFilter))
        {
            filtered = filtered.Where(c => c.Priority == conversationPriorityFilter);
        }

        // Tri
        filtered = conversationSortOrder switch
        {
            "recent" => filtered.OrderByDescending(c => c.LastMessageAt ?? c.CreatedAt),
            "oldest" => filtered.OrderBy(c => c.LastMessageAt ?? c.CreatedAt),
            "unread" => filtered.OrderByDescending(c => c.UnreadCount).ThenByDescending(c => c.LastMessageAt ?? c.CreatedAt),
            _ => filtered.OrderByDescending(c => c.LastMessageAt ?? c.CreatedAt)
        };

        filteredConversations = filtered.ToList();
    }

    private void ClearConversationSearch()
    {
        conversationSearchTerm = "";
        FilterConversations();
    }

    private async Task LoadFAQs()
    {
        try
        {
            faqs = await ChatService.GetFAQsAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du chargement des FAQs");
            faqs = new List<FAQDto>();
        }
    }

    private async Task LoadQuickReplies()
    {
        try
        {
            quickReplies = await ChatService.GetQuickRepliesAsync();
            FilterQuickReplies();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du chargement des réponses rapides");
            quickReplies = new List<QuickReplyDto>();
            filteredQuickReplies = new List<QuickReplyDto>();
        }
    }

    private void FilterQuickReplies()
    {
        var filtered = quickReplies.AsEnumerable();

        // Filtre par recherche (titre ou contenu)
        if (!string.IsNullOrWhiteSpace(quickReplySearchTerm))
        {
            var searchLower = quickReplySearchTerm.ToLower();
            filtered = filtered.Where(qr =>
                qr.Title.ToLower().Contains(searchLower) ||
                qr.Content.ToLower().Contains(searchLower));
        }

        // Filtre par catégorie
        if (!string.IsNullOrWhiteSpace(quickReplyCategoryFilter))
        {
            filtered = filtered.Where(qr => qr.Category == quickReplyCategoryFilter);
        }

        // Tri
        filtered = quickReplySortOrder switch
        {
            "recent" => filtered.OrderByDescending(qr => qr.Id),
            "oldest" => filtered.OrderBy(qr => qr.Id),
            "title" => filtered.OrderBy(qr => qr.Title),
            _ => filtered.OrderByDescending(qr => qr.Id)
        };

        filteredQuickReplies = filtered.ToList();
    }

    private void ClearQuickReplySearch()
    {
        quickReplySearchTerm = "";
        FilterQuickReplies();
    }

    private async Task LoadFAQCategories()
    {
        try
        {
            faqCategories = await ChatService.GetFAQCategoriesAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du chargement des catégories FAQ");
            faqCategories = new List<string>();
        }
    }

    private async Task RefreshData()
    {
        await LoadData();
    }

    private void SetActiveTab(string tab)
    {
        activeTab = tab;
    }

    private async Task OnFAQCategoryChanged(ChangeEventArgs e)
    {
        selectedFAQCategory = e.Value?.ToString() ?? "";
    }

    private void OpenConversation(int conversationId)
    {
        Logger.LogInformation("Opening conversation {ConversationId}", conversationId);
        Navigation.NavigateTo($"/chat/conversation/{conversationId}");
    }

    private void ShowCreateQuickReplyModal()
    {
        isEditingQuickReply = false;
        editingQuickReplyId = 0;
        newQuickReply = new CreateQuickReplyDto();
        newQuickReplyTags = "";
        quickReplyError = "";
        showQuickReplyModal = true;
        Logger.LogInformation("Show create quick reply modal");
    }

    private void ShowEditQuickReplyModal(QuickReplyDto reply)
    {
        isEditingQuickReply = true;
        editingQuickReplyId = reply.Id;
        newQuickReply = new CreateQuickReplyDto
        {
            Title = reply.Title,
            Content = reply.Content,
            Category = reply.Category,
            Tags = reply.Tags
        };
        newQuickReplyTags = reply.Tags != null && reply.Tags.Any()
            ? string.Join(", ", reply.Tags)
            : "";
        quickReplyError = "";
        showQuickReplyModal = true;
        Logger.LogInformation("Show edit quick reply modal for ID: {Id}", reply.Id);
    }

    private void CloseQuickReplyModal()
    {
        showQuickReplyModal = false;
        isEditingQuickReply = false;
        editingQuickReplyId = 0;
        newQuickReply = new CreateQuickReplyDto();
        newQuickReplyTags = "";
        quickReplyError = "";
    }

    private void ShowDeleteQuickReplyConfirm(QuickReplyDto reply)
    {
        quickReplyToDelete = reply;
        showDeleteConfirm = true;
        Logger.LogInformation("Show delete confirmation for quick reply ID: {Id}", reply.Id);
    }

    private void CloseDeleteConfirm()
    {
        showDeleteConfirm = false;
        quickReplyToDelete = null;
    }

    private async Task SaveQuickReply()
    {
        try
        {
            // Validation
            if (string.IsNullOrWhiteSpace(newQuickReply.Title))
            {
                quickReplyError = "Le titre est requis";
                return;
            }

            if (string.IsNullOrWhiteSpace(newQuickReply.Content))
            {
                quickReplyError = "Le contenu est requis";
                return;
            }

            if (string.IsNullOrWhiteSpace(newQuickReply.Category))
            {
                quickReplyError = "La catégorie est requise";
                return;
            }

            isSavingQuickReply = true;
            quickReplyError = "";

            // Convertir les tags
            if (!string.IsNullOrWhiteSpace(newQuickReplyTags))
            {
                newQuickReply.Tags = newQuickReplyTags
                    .Split(',')
                    .Select(t => t.Trim())
                    .Where(t => !string.IsNullOrWhiteSpace(t))
                    .ToList();
            }
            else
            {
                newQuickReply.Tags = new List<string>();
            }

            bool success = false;

            if (isEditingQuickReply)
            {
                // Modifier la réponse rapide existante
                success = await ChatService.UpdateQuickReplyAsync(editingQuickReplyId, newQuickReply);

                if (success)
                {
                    Logger.LogInformation("Réponse rapide modifiée avec succès: {Id}", editingQuickReplyId);
                }
                else
                {
                    quickReplyError = "Erreur lors de la modification de la réponse rapide";
                }
            }
            else
            {
                // Créer une nouvelle réponse rapide
                var id = await ChatService.CreateQuickReplyAsync(newQuickReply);
                success = id > 0;

                if (success)
                {
                    Logger.LogInformation("Réponse rapide créée avec succès: {Id}", id);
                }
                else
                {
                    quickReplyError = "Erreur lors de la création de la réponse rapide";
                }
            }

            if (success)
            {
                // Recharger les réponses rapides
                await LoadQuickReplies();

                // Fermer le modal
                CloseQuickReplyModal();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'enregistrement de la réponse rapide");
            quickReplyError = $"Erreur: {ex.Message}";
        }
        finally
        {
            isSavingQuickReply = false;
        }
    }

    private async Task DeleteQuickReply()
    {
        if (quickReplyToDelete == null) return;

        try
        {
            isDeletingQuickReply = true;

            var success = await ChatService.DeleteQuickReplyAsync(quickReplyToDelete.Id);

            if (success)
            {
                Logger.LogInformation("Réponse rapide supprimée avec succès: {Id}", quickReplyToDelete.Id);

                // Recharger les réponses rapides
                await LoadQuickReplies();

                // Fermer le modal
                CloseDeleteConfirm();
            }
            else
            {
                Logger.LogError("Erreur lors de la suppression de la réponse rapide {Id}", quickReplyToDelete.Id);
                // TODO: Afficher un message d'erreur
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de la suppression de la réponse rapide");
        }
        finally
        {
            isDeletingQuickReply = false;
        }
    }

    private async Task CopyToClipboard(string text)
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("navigator.clipboard.writeText", text);
            // TODO: Show toast notification
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de la copie dans le presse-papiers");
        }
    }

    private string GetStatusColor(string status)
    {
        return status.ToLower() switch
        {
            "open" => "success",
            "inprogress" => "primary",
            "waiting" => "warning",
            "resolved" => "info",
            "closed" => "secondary",
            "escalated" => "danger",
            _ => "primary"
        };
    }

    private string GetStatusLabel(string status)
    {
        return status switch
        {
            "Open" => "Ouvert",
            "InProgress" => "En cours",
            "Waiting" => "En attente",
            "Resolved" => "Résolu",
            "Closed" => "Fermé",
            "Escalated" => "Escaladé",
            _ => status
        };
    }

    private string GetPriorityColor(string priority)
    {
        return priority.ToLower() switch
        {
            "low" => "secondary",
            "normal" => "info",
            "high" => "warning",
            "urgent" => "danger",
            "critical" => "danger",
            _ => "secondary"
        };
    }

    private string GetPriorityLabel(string priority)
    {
        return priority switch
        {
            "Low" => "Basse",
            "Normal" => "Normale",
            "High" => "Haute",
            "Urgent" => "Urgente",
            "Critical" => "Critique",
            _ => priority
        };
    }

    private string GetRelativeTime(DateTime dateTime)
    {
        var timeSpan = DateTime.UtcNow - dateTime;

        if (timeSpan.TotalMinutes < 1)
            return "À l'instant";
        if (timeSpan.TotalMinutes < 60)
            return $"Il y a {(int)timeSpan.TotalMinutes} min";
        if (timeSpan.TotalHours < 24)
            return $"Il y a {(int)timeSpan.TotalHours}h";
        if (timeSpan.TotalDays < 7)
            return $"Il y a {(int)timeSpan.TotalDays}j";

        return dateTime.ToString("dd/MM/yyyy");
    }

    public async ValueTask DisposeAsync()
    {
        try
        {
            // Désabonner des événements
            if (SignalRService != null)
            {
                SignalRService.OnMessageReceived -= HandleMessageReceived;
                SignalRService.OnUserOnline -= HandleUserOnline;
                SignalRService.OnUserOffline -= HandleUserOffline;
                SignalRService.OnNewConversation -= HandleNewConversation;

                // Disposer la connexion SignalR
                await SignalRService.DisposeAsync();
            }

            Logger.LogInformation("Ressources SignalR libérées");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de la libération des ressources SignalR");
        }
    }
}
