namespace NafaPlace.SellerPortal.Models.Delivery;

public class DeliveryOrderDto
{
    public int Id { get; set; }
    public string OrderNumber { get; set; } = "";
    public string CustomerName { get; set; } = "";
    public string CustomerPhone { get; set; } = "";
    public string ShippingAddress { get; set; } = "";
    public decimal TotalAmount { get; set; }
    public int ItemCount { get; set; }
    public string Status { get; set; } = ""; // pending, shipped, in_transit, delivered, delayed, cancelled
    public string Carrier { get; set; } = "";
    public string TrackingNumber { get; set; } = "";
    public DateTime? ShippedDate { get; set; }
    public DateTime? EstimatedDelivery { get; set; }
    public DateTime? ActualDelivery { get; set; }
    public bool IsDelayed { get; set; }
    public string? DeliveryNotes { get; set; }
    public List<DeliveryTrackingEventDto> TrackingEvents { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class DeliveryTrackingEventDto
{
    public int Id { get; set; }
    public int DeliveryOrderId { get; set; }
    public string Status { get; set; } = "";
    public string Description { get; set; } = "";
    public string? Location { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public DateTime EventDate { get; set; }
    public string? EventBy { get; set; }
    public string? Notes { get; set; }
    public string? PhotoUrl { get; set; }
    public bool IsCustomerVisible { get; set; } = true;
    public bool IsAutomated { get; set; } = false;
}

public class DeliveryStatsDto
{
    public int InTransitCount { get; set; }
    public int DeliveredCount { get; set; }
    public int DelayedCount { get; set; }
    public decimal DeliveryRate { get; set; }
    public double AverageDeliveryTime { get; set; } // en heures
    public int TotalOrders { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
}

public class UpdateDeliveryStatusRequest
{
    public string Status { get; set; } = "";
    public string? Notes { get; set; }
    public string? Location { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public DateTime EventDate { get; set; } = DateTime.UtcNow;
}

public class DeliveryFilterRequest
{
    public string? Status { get; set; }
    public string? Carrier { get; set; }
    public string? SearchTerm { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public bool? IsDelayed { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

public enum DeliveryStatus
{
    Pending = 1,
    Confirmed = 2,
    PickedUp = 3,
    InTransit = 4,
    OutForDelivery = 5,
    Delivered = 6,
    Failed = 7,
    Returned = 8,
    Cancelled = 9,
    Delayed = 10
}

public class CarrierDto
{
    public int Id { get; set; }
    public string Name { get; set; } = "";
    public string Code { get; set; } = "";
    public string Type { get; set; } = ""; // internal, external, partner
    public bool IsActive { get; set; }
    public string? ContactInfo { get; set; }
    public string? TrackingUrl { get; set; }
}
