@page "/notifications/push"
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.JSInterop
@inject IJSRuntime JSRuntime
@inject ILogger<PushNotifications> Logger
@attribute [Authorize(Roles = "Admin")]

<PageTitle>Gestion des Notifications Push - Admin</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-mobile-alt text-primary me-2"></i>
                        Gestion des Notifications Push
                    </h1>
                    <p class="text-muted mb-0">Envoyez des notifications push aux utilisateurs</p>
                </div>
                <button class="btn btn-primary" @onclick="ShowCreateNotificationModal">
                    <i class="fas fa-plus me-2"></i>Nouvelle Notification
                </button>
            </div>
        </div>
    </div>

    <!-- Statistiques -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                                <i class="fas fa-users text-primary fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Abonnés</h6>
                            <h4 class="mb-0">@totalSubscribers.ToString("N0")</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-opacity-10 rounded-3 p-3">
                                <i class="fas fa-paper-plane text-success fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Envoyées Aujourd'hui</h6>
                            <h4 class="mb-0">@todayNotifications.ToString("N0")</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-opacity-10 rounded-3 p-3">
                                <i class="fas fa-eye text-info fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Taux d'Ouverture</h6>
                            <h4 class="mb-0">@openRate.ToString("F1")%</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                                <i class="fas fa-mouse-pointer text-warning fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Taux de Clic</h6>
                            <h4 class="mb-0">@clickRate.ToString("F1")%</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des notifications -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-transparent border-0">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Historique des Notifications
                </h5>
                <div class="d-flex gap-2">
                    <div class="input-group" style="width: 300px;">
                        <input type="text" class="form-control" placeholder="Rechercher..." @bind="searchTerm" @oninput="FilterNotifications">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            @if (isLoading)
            {
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            }
            else if (filteredNotifications?.Any() == true)
            {
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Titre</th>
                                <th>Message</th>
                                <th>Cible</th>
                                <th>Statut</th>
                                <th>Envoyée le</th>
                                <th>Statistiques</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var notification in filteredNotifications)
                            {
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas @GetNotificationIcon(notification.Type) text-@GetNotificationColor(notification.Type) me-2"></i>
                                            <strong>@notification.Title</strong>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-truncate d-inline-block" style="max-width: 200px;" title="@notification.Message">
                                            @notification.Message
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-@GetTargetColor(notification.Target)">
                                            @notification.Target
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-@GetStatusColor(notification.Status)">
                                            @notification.Status
                                        </span>
                                    </td>
                                    <td>@notification.SentAt.ToString("dd/MM/yyyy HH:mm")</td>
                                    <td>
                                        <small class="text-muted">
                                            <i class="fas fa-paper-plane me-1"></i>@notification.Sent
                                            <i class="fas fa-eye ms-2 me-1"></i>@notification.Opened
                                            <i class="fas fa-mouse-pointer ms-2 me-1"></i>@notification.Clicked
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" @onclick="() => ViewNotificationDetails(notification)" title="Voir détails">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-success" @onclick="() => DuplicateNotification(notification)" title="Dupliquer">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" @onclick="() => DeleteNotification(notification)" title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-bell-slash fs-1 text-muted mb-3"></i>
                    <h5 class="text-muted">Aucune notification trouvée</h5>
                    <p class="text-muted">Commencez par créer votre première notification push</p>
                    <button class="btn btn-primary" @onclick="ShowCreateNotificationModal">
                        <i class="fas fa-plus me-2"></i>Créer une notification
                    </button>
                </div>
            }
        </div>
    </div>
</div>

<!-- Modal de création/édition -->
<div class="modal fade" id="notificationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    @(isEditMode ? "Modifier" : "Créer") une Notification Push
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <EditForm Model="currentNotification" OnValidSubmit="SaveNotification">
                    <DataAnnotationsValidator />
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">Titre *</label>
                            <InputText @bind-Value="currentNotification.Title" class="form-control" placeholder="Titre de la notification" />
                            <ValidationMessage For="() => currentNotification.Title" />
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">Type</label>
                            <InputSelect @bind-Value="currentNotification.Type" class="form-select">
                                <option value="info">Information</option>
                                <option value="promotion">Promotion</option>
                                <option value="order">Commande</option>
                                <option value="reminder">Rappel</option>
                                <option value="alert">Alerte</option>
                            </InputSelect>
                        </div>
                        
                        <div class="col-12">
                            <label class="form-label">Message *</label>
                            <InputTextArea @bind-Value="currentNotification.Message" class="form-control" rows="3" placeholder="Contenu de la notification" />
                            <ValidationMessage For="() => currentNotification.Message" />
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">Cible</label>
                            <InputSelect @bind-Value="currentNotification.Target" class="form-select">
                                <option value="all">Tous les utilisateurs</option>
                                <option value="customers">Clients uniquement</option>
                                <option value="sellers">Vendeurs uniquement</option>
                                <option value="new">Nouveaux utilisateurs</option>
                                <option value="inactive">Utilisateurs inactifs</option>
                            </InputSelect>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">URL de redirection</label>
                            <InputText @bind-Value="currentNotification.Url" class="form-control" placeholder="https://..." />
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">Icône</label>
                            <InputText @bind-Value="currentNotification.Icon" class="form-control" placeholder="URL de l'icône" />
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">Programmation</label>
                            <InputSelect @bind-Value="currentNotification.ScheduleType" class="form-select">
                                <option value="now">Envoyer maintenant</option>
                                <option value="scheduled">Programmer</option>
                            </InputSelect>
                        </div>
                        
                        @if (currentNotification.ScheduleType == "scheduled")
                        {
                            <div class="col-md-6">
                                <label class="form-label">Date et heure</label>
                                <input type="datetime-local" @bind="currentNotification.ScheduledAt" class="form-control" />
                            </div>
                        }
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            @(isEditMode ? "Modifier" : "Créer")
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
</div>

@code {
    private bool isLoading = false;
    private bool isEditMode = false;
    private string searchTerm = "";

    // Statistiques
    private int totalSubscribers = 2_456;
    private int todayNotifications = 23;
    private decimal openRate = 68.5m;
    private decimal clickRate = 12.3m;

    // Données
    private List<PushNotificationDto> notifications = new();
    private List<PushNotificationDto> filteredNotifications = new();
    private PushNotificationDto currentNotification = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadNotifications();
    }

    private async Task LoadNotifications()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            // Simuler le chargement
            await Task.Delay(500);

            // Données simulées
            notifications = new List<PushNotificationDto>
            {
                new() { Id = 1, Title = "Nouvelle promotion", Message = "50% de réduction sur tous les smartphones", Type = "promotion", Target = "all", Status = "sent", SentAt = DateTime.Now.AddHours(-2), Sent = 2456, Opened = 1678, Clicked = 234 },
                new() { Id = 2, Title = "Commande expédiée", Message = "Votre commande #12345 a été expédiée", Type = "order", Target = "customers", Status = "sent", SentAt = DateTime.Now.AddHours(-5), Sent = 1, Opened = 1, Clicked = 0 },
                new() { Id = 3, Title = "Rappel panier", Message = "N'oubliez pas les articles dans votre panier", Type = "reminder", Target = "customers", Status = "scheduled", SentAt = DateTime.Now.AddHours(1), Sent = 0, Opened = 0, Clicked = 0 }
            };

            filteredNotifications = notifications;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du chargement des notifications");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void FilterNotifications()
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            filteredNotifications = notifications;
        }
        else
        {
            filteredNotifications = notifications.Where(n =>
                n.Title.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                n.Message.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
            ).ToList();
        }
        StateHasChanged();
    }

    private async Task ShowCreateNotificationModal()
    {
        currentNotification = new PushNotificationDto();
        isEditMode = false;
        await JSRuntime.InvokeVoidAsync("bootstrap.Modal.getOrCreateInstance", "#notificationModal").AsTask();
        await JSRuntime.InvokeVoidAsync("eval", "new bootstrap.Modal(document.getElementById('notificationModal')).show()");
    }

    private async Task SaveNotification()
    {
        try
        {
            if (isEditMode)
            {
                // Modifier notification existante
                var index = notifications.FindIndex(n => n.Id == currentNotification.Id);
                if (index >= 0)
                {
                    notifications[index] = currentNotification;
                }
            }
            else
            {
                // Créer nouvelle notification
                currentNotification.Id = notifications.Count + 1;
                currentNotification.Status = currentNotification.ScheduleType == "now" ? "sent" : "scheduled";
                currentNotification.SentAt = currentNotification.ScheduleType == "now" ? DateTime.Now : currentNotification.ScheduledAt;
                notifications.Insert(0, currentNotification);
            }

            FilterNotifications();
            await JSRuntime.InvokeVoidAsync("eval", "bootstrap.Modal.getInstance(document.getElementById('notificationModal')).hide()");
            await JSRuntime.InvokeVoidAsync("alert", "Notification " + (isEditMode ? "modifiée" : "créée") + " avec succès!");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de la sauvegarde");
            await JSRuntime.InvokeVoidAsync("alert", "Erreur lors de la sauvegarde");
        }
    }

    private async Task ViewNotificationDetails(PushNotificationDto notification)
    {
        await JSRuntime.InvokeVoidAsync("alert", $"Détails de: {notification.Title}");
    }

    private async Task DuplicateNotification(PushNotificationDto notification)
    {
        currentNotification = new PushNotificationDto
        {
            Title = notification.Title + " (Copie)",
            Message = notification.Message,
            Type = notification.Type,
            Target = notification.Target,
            Url = notification.Url,
            Icon = notification.Icon
        };
        isEditMode = false;
        await JSRuntime.InvokeVoidAsync("eval", "new bootstrap.Modal(document.getElementById('notificationModal')).show()");
    }

    private async Task DeleteNotification(PushNotificationDto notification)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", $"Supprimer la notification '{notification.Title}' ?");
        if (confirmed)
        {
            notifications.Remove(notification);
            FilterNotifications();
            await JSRuntime.InvokeVoidAsync("alert", "Notification supprimée avec succès!");
        }
    }

    private string GetNotificationIcon(string type)
    {
        return type switch
        {
            "promotion" => "fa-tag",
            "order" => "fa-shopping-cart",
            "reminder" => "fa-clock",
            "alert" => "fa-exclamation-triangle",
            _ => "fa-info-circle"
        };
    }

    private string GetNotificationColor(string type)
    {
        return type switch
        {
            "promotion" => "success",
            "order" => "primary",
            "reminder" => "warning",
            "alert" => "danger",
            _ => "info"
        };
    }

    private string GetTargetColor(string target)
    {
        return target switch
        {
            "all" => "primary",
            "customers" => "success",
            "sellers" => "info",
            "new" => "warning",
            "inactive" => "secondary",
            _ => "light"
        };
    }

    private string GetStatusColor(string status)
    {
        return status switch
        {
            "sent" => "success",
            "scheduled" => "warning",
            "failed" => "danger",
            _ => "secondary"
        };
    }

    public class PushNotificationDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = "";
        public string Message { get; set; } = "";
        public string Type { get; set; } = "info";
        public string Target { get; set; } = "all";
        public string Status { get; set; } = "";
        public DateTime SentAt { get; set; }
        public string? Url { get; set; }
        public string? Icon { get; set; }
        public string ScheduleType { get; set; } = "now";
        public DateTime ScheduledAt { get; set; } = DateTime.Now.AddHours(1);
        public int Sent { get; set; }
        public int Opened { get; set; }
        public int Clicked { get; set; }
    }
}
