// Fonction pour jouer un son de notification
window.playNotificationSound = function() {
    try {
        // Créer un contexte audio
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        
        // Créer un oscillateur pour générer un son
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        // Connecter l'oscillateur au gain et le gain à la destination
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        // Configurer le son (fréquence et type)
        oscillator.type = 'sine';
        oscillator.frequency.setValueAtTime(800, audioContext.currentTime); // Fréquence de 800 Hz
        
        // Configurer le volume (fade in/out)
        gainNode.gain.setValueAtTime(0, audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
        
        // Jouer le son
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.3);
        
        console.log('🔔 Son de notification joué');
    } catch (error) {
        console.error('Erreur lors de la lecture du son:', error);
    }
};

// Fonction pour demander la permission de notification
window.requestNotificationPermission = async function() {
    if (!("Notification" in window)) {
        console.log("Ce navigateur ne supporte pas les notifications");
        return false;
    }
    
    if (Notification.permission === "granted") {
        return true;
    }
    
    if (Notification.permission !== "denied") {
        const permission = await Notification.requestPermission();
        return permission === "granted";
    }
    
    return false;
};

// Fonction pour afficher une notification de bureau
window.showDesktopNotification = function(title, body, icon) {
    if (Notification.permission === "granted") {
        const notification = new Notification(title, {
            body: body,
            icon: icon || '/images/nafaplace-gradient-modern.svg',
            badge: '/images/nafaplace-gradient-modern.svg',
            tag: 'chat-notification',
            requireInteraction: false,
            silent: false
        });
        
        notification.onclick = function() {
            window.focus();
            notification.close();
        };
        
        // Fermer automatiquement après 5 secondes
        setTimeout(() => notification.close(), 5000);
    }
};

// Fonction pour faire vibrer le téléphone (si supporté)
window.vibrateDevice = function() {
    if ("vibrate" in navigator) {
        navigator.vibrate([200, 100, 200]); // Vibrer 200ms, pause 100ms, vibrer 200ms
    }
};

