@using Microsoft.JSInterop
@using NafaPlace.Reviews.DTOs
@inject IJSRuntime JSRuntime
@inject HttpClient HttpClient

<div class="advanced-review-component">
    <!-- Review Statistics -->
    <div class="review-stats-section mb-4">
        @if (ReviewStats != null)
        {
            <div class="row">
                <div class="col-md-4">
                    <div class="overall-rating">
                        <div class="rating-number">@ReviewStats.AverageRating.ToString("F1")</div>
                        <div class="rating-stars">
                            @for (int i = 1; i <= 5; i++)
                            {
                                <i class="fas fa-star @(i <= ReviewStats.AverageRating ? "filled" : "")"></i>
                            }
                        </div>
                        <div class="rating-count">@ReviewStats.TotalReviews avis</div>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="rating-breakdown">
                        @for (int i = 5; i >= 1; i--)
                        {
                            var ratingCount = ReviewStats.RatingBreakdown.ContainsKey(i) ? ReviewStats.RatingBreakdown[i] : 0;
                            var percentage = ReviewStats.TotalReviews > 0 ? (ratingCount * 100.0 / ReviewStats.TotalReviews) : 0;

                            <div class="rating-bar-row">
                                <span class="rating-label">@i étoile@(i > 1 ? "s" : "")</span>
                                <div class="rating-bar">
                                    <div class="rating-fill" style="width: @percentage%"></div>
                                </div>
                                <span class="rating-percentage">@percentage.ToString("F0")%</span>
                            </div>
                        }
                    </div>
                </div>
            </div>
        }
    </div>

    <!-- Review Filters -->
    <div class="review-filters mb-4">
        <div class="d-flex flex-wrap align-items-center gap-3">
            <div class="filter-group">
                <label class="filter-label">Trier par:</label>
                <select @bind="SortBy" class="form-select form-select-sm">
                    <option value="newest">Plus récents</option>
                    <option value="oldest">Plus anciens</option>
                    <option value="highest">Note la plus élevée</option>
                    <option value="lowest">Note la plus basse</option>
                    <option value="helpful">Plus utiles</option>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Note:</label>
                <select @bind="FilterRating" class="form-select form-select-sm">
                    <option value="">Toutes</option>
                    <option value="5">5 étoiles</option>
                    <option value="4">4 étoiles</option>
                    <option value="3">3 étoiles</option>
                    <option value="2">2 étoiles</option>
                    <option value="1">1 étoile</option>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-toggle">
                    <input type="checkbox" @bind="HasMediaFilter" />
                    <span class="toggle-text">Avec photos/vidéos</span>
                </label>
            </div>

            <div class="filter-group">
                <label class="filter-toggle">
                    <input type="checkbox" @bind="VerifiedFilter" />
                    <span class="toggle-text">Achats vérifiés</span>
                </label>
            </div>
        </div>
    </div>

    <!-- Write Review Button -->
    @if (CanWriteReview)
    {
        <div class="write-review-section mb-4">
            <button class="btn btn-primary btn-lg" @onclick="ShowWriteReviewModal">
                <i class="fas fa-edit me-2"></i>
                Écrire un avis
            </button>
        </div>
    }

    <!-- Reviews List -->
    <div class="reviews-list">
        @if (IsLoading)
        {
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                </div>
            </div>
        }
        else if (Reviews?.Any() == true)
        {
            @foreach (var review in Reviews)
            {
                <div class="review-item">
                    <div class="review-header">
                        <div class="reviewer-info">
                            <div class="reviewer-avatar">
                                <img src="@GetAvatarUrl(review.UserName)" alt="@review.UserName" />
                            </div>
                            <div class="reviewer-details">
                                <div class="reviewer-name">@GetDisplayName(review.UserName)</div>
                                <div class="review-meta">
                                    <div class="review-rating">
                                        @for (int i = 1; i <= 5; i++)
                                        {
                                            <i class="fas fa-star @(i <= review.Rating ? "filled" : "")"></i>
                                        }
                                    </div>
                                    <span class="review-date">@review.CreatedAt.ToString("dd MMMM yyyy")</span>
                                    @if (review.IsVerified)
                                    {
                                        <span class="verified-badge">
                                            <i class="fas fa-check-circle"></i>
                                            Achat vérifié
                                        </span>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="review-actions">
                            <div class="dropdown">
                                <button class="btn btn-link btn-sm" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><button class="dropdown-item" @onclick="() => ReportReview(review.Id)">
                                        <i class="fas fa-flag me-2"></i>Signaler
                                    </button></li>
                                    @if (review.UserId == CurrentUserId)
                                    {
                                        <li><button class="dropdown-item" @onclick="() => EditReview(review)">
                                            <i class="fas fa-edit me-2"></i>Modifier
                                        </button></li>
                                        <li><button class="dropdown-item text-danger" @onclick="() => DeleteReview(review.Id)">
                                            <i class="fas fa-trash me-2"></i>Supprimer
                                        </button></li>
                                    }
                                </ul>
                            </div>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(review.Title))
                    {
                        <div class="review-title">@review.Title</div>
                    }

                    <div class="review-content">
                        <p class="review-text">@review.Comment</p>

                        <!-- Review Media -->
                        @if (review.Media?.Any() == true)
                        {
                            <div class="review-media">
                                <div class="media-grid">
                                    @foreach (var media in review.Media)
                                    {
                                        @if (media.MediaType == "Image")
                                        {
                                            <div class="media-item image-item" @onclick="() => OpenImageModal(review.Media, media)">
                                                <img src="@media.ThumbnailUrl" alt="Review image" class="media-thumbnail" />
                                                <div class="media-overlay">
                                                    <i class="fas fa-expand"></i>
                                                </div>
                                            </div>
                                        }
                                        else if (media.MediaType == "Video")
                                        {
                                            <div class="media-item video-item" @onclick="() => OpenVideoModal(media)">
                                                <img src="@media.ThumbnailUrl" alt="Video thumbnail" class="media-thumbnail" />
                                                <div class="media-overlay">
                                                    <i class="fas fa-play"></i>
                                                </div>
                                                <div class="video-duration">@FormatVideoDuration(media.Duration)</div>
                                            </div>
                                        }
                                    }
                                </div>
                            </div>
                        }
                    </div>

                    <div class="review-footer">
                        <div class="review-helpfulness">
                            <button class="btn btn-outline-primary btn-sm @(review.UserFoundHelpful == true ? "active" : "")"
                                    @onclick="() => MarkHelpful(review.Id, true)">
                                <i class="fas fa-thumbs-up me-1"></i>
                                Utile (@review.HelpfulCount)
                            </button>
                            <button class="btn btn-outline-secondary btn-sm @(review.UserFoundHelpful == false ? "active" : "")"
                                    @onclick="() => MarkHelpful(review.Id, false)">
                                <i class="fas fa-thumbs-down me-1"></i>
                                Pas utile (@review.NotHelpfulCount)
                            </button>
                        </div>
                        @if (review.ReplyCount > 0)
                        {
                            <button class="btn btn-link btn-sm" @onclick="() => ToggleReplies(review.Id)">
                                <i class="fas fa-comments me-1"></i>
                                Voir les réponses (@review.ReplyCount)
                            </button>
                        }
                    </div>

                    <!-- Review Replies -->
                    @if (ExpandedReplies.Contains(review.Id))
                    {
                        <div class="review-replies">
                            <!-- Replies would be loaded here -->
                        </div>
                    }
                </div>
            }

            <!-- Load More Button -->
            @if (HasMoreReviews)
            {
                <div class="text-center mt-4">
                    <button class="btn btn-outline-primary" @onclick="LoadMoreReviews" disabled="@IsLoadingMore">
                        @if (IsLoadingMore)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        Voir plus d'avis
                    </button>
                </div>
            }
        }
        else
        {
            <div class="no-reviews">
                <div class="text-center py-5">
                    <i class="fas fa-star fa-3x text-muted mb-3"></i>
                    <h5>Aucun avis pour le moment</h5>
                    <p class="text-muted">Soyez le premier à laisser un avis sur ce produit.</p>
                    @if (CanWriteReview)
                    {
                        <button class="btn btn-primary" @onclick="ShowWriteReviewModal">
                            Écrire le premier avis
                        </button>
                    }
                </div>
            </div>
        }
    </div>
</div>

<!-- Write/Edit Review Modal -->
@if (ShowReviewModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        @(EditingReview != null ? "Modifier votre avis" : "Écrire un avis")
                    </h5>
                    <button type="button" class="btn-close" @onclick="CloseReviewModal"></button>
                </div>
                <div class="modal-body">
                    <form @onsubmit="SubmitReview" @onsubmit:preventDefault="true">
                        <!-- Rating -->
                        <div class="mb-3">
                            <label class="form-label">Note *</label>
                            <div class="rating-input">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    var star = i;
                                    <button type="button" class="star-btn @(ReviewForm.Rating >= star ? "active" : "")"
                                            @onclick="() => SetRating(star)">
                                        <i class="fas fa-star"></i>
                                    </button>
                                }
                            </div>
                        </div>

                        <!-- Title -->
                        <div class="mb-3">
                            <label class="form-label">Titre (optionnel)</label>
                            <input @bind="ReviewForm.Title" class="form-control" maxlength="100"
                                   placeholder="Résumez votre expérience" />
                        </div>

                        <!-- Comment -->
                        <div class="mb-3">
                            <label class="form-label">Votre avis *</label>
                            <textarea @bind="ReviewForm.Comment" class="form-control" rows="4" maxlength="1000"
                                      placeholder="Partagez votre expérience avec ce produit..." required></textarea>
                            <div class="form-text">@(ReviewForm.Comment?.Length ?? 0)/1000 caractères</div>
                        </div>

                        <!-- Media Upload -->
                        <div class="mb-3">
                            <label class="form-label">Photos et vidéos (optionnel)</label>
                            <div class="media-upload-area">
                                <div class="upload-buttons">
                                    <input type="file" id="imageUpload" multiple accept="image/*" @onchange="HandleImageUpload" style="display: none;" />
                                    <input type="file" id="videoUpload" multiple accept="video/*" @onchange="HandleVideoUpload" style="display: none;" />

                                    <button type="button" class="btn btn-outline-primary" @onclick="@(() => JSRuntime.InvokeVoidAsync("clickElement", "imageUpload"))">
                                        <i class="fas fa-camera me-2"></i>
                                        Ajouter des photos
                                    </button>
                                    <button type="button" class="btn btn-outline-primary" @onclick="@(() => JSRuntime.InvokeVoidAsync("clickElement", "videoUpload"))">
                                        <i class="fas fa-video me-2"></i>
                                        Ajouter des vidéos
                                    </button>
                                </div>

                                <!-- Preview uploaded files -->
                                @if (UploadedFiles.Any())
                                {
                                    <div class="uploaded-files mt-3">
                                        @foreach (var file in UploadedFiles)
                                        {
                                            <div class="uploaded-file">
                                                @if (file.Type.StartsWith("image/"))
                                                {
                                                    <img src="@file.PreviewUrl" alt="Preview" class="file-preview" />
                                                }
                                                else
                                                {
                                                    <div class="video-preview">
                                                        <i class="fas fa-video"></i>
                                                        <span>@file.Name</span>
                                                    </div>
                                                }
                                                <button type="button" class="btn btn-sm btn-danger remove-file-btn"
                                                        @onclick="() => RemoveUploadedFile(file)">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        }
                                    </div>
                                }
                            </div>
                            <div class="form-text">
                                Formats acceptés: JPG, PNG, WebP pour les images. MP4, WebM pour les vidéos.
                                Taille max: 10MB par image, 100MB par vidéo.
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="CloseReviewModal">
                        Annuler
                    </button>
                    <button type="button" class="btn btn-primary" @onclick="SubmitReview"
                            disabled="@(ReviewForm.Rating == 0 || string.IsNullOrEmpty(ReviewForm.Comment) || IsSubmitting)">
                        @if (IsSubmitting)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        @(EditingReview != null ? "Mettre à jour" : "Publier l'avis")
                    </button>
                </div>
            </div>
        </div>
    </div>
}

<!-- Image Modal -->
@if (ShowImageModal && CurrentImageMedia != null)
{
    <div class="image-modal" @onclick="CloseImageModal">
        <div class="image-modal-content" @onclick:stopPropagation="true">
            <div class="image-modal-header">
                <button class="btn-close btn-close-white" @onclick="CloseImageModal"></button>
            </div>
            <div class="image-modal-body">
                <img src="@CurrentImageMedia.Url" alt="Review image" class="modal-image" />
            </div>
            <div class="image-modal-nav">
                @if (AllImageMedia.Count > 1)
                {
                    <button class="nav-btn prev-btn" @onclick="PreviousImage" disabled="@(CurrentImageIndex <= 0)">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <span class="image-counter">@(CurrentImageIndex + 1) / @AllImageMedia.Count</span>
                    <button class="nav-btn next-btn" @onclick="NextImage" disabled="@(CurrentImageIndex >= AllImageMedia.Count - 1)">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                }
            </div>
        </div>
    </div>
}

<!-- Video Modal -->
@if (ShowVideoModal && CurrentVideoMedia != null)
{
    <div class="video-modal" @onclick="CloseVideoModal">
        <div class="video-modal-content" @onclick:stopPropagation="true">
            <div class="video-modal-header">
                <button class="btn-close btn-close-white" @onclick="CloseVideoModal"></button>
            </div>
            <div class="video-modal-body">
                <video controls class="modal-video" src="@CurrentVideoMedia.Url"></video>
            </div>
        </div>
    </div>
}

<style>
    .advanced-review-component {
        max-width: 100%;
    }

    /* Review Statistics */
    .review-stats-section {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .overall-rating {
        text-align: center;
    }

    .rating-number {
        font-size: 3rem;
        font-weight: bold;
        color: #E73C30;
        line-height: 1;
    }

    .rating-stars {
        margin: 0.5rem 0;
    }

    .rating-stars .fa-star {
        font-size: 1.5rem;
        color: #ddd;
        margin: 0 2px;
    }

    .rating-stars .fa-star.filled {
        color: #ffc107;
    }

    .rating-count {
        color: #6c757d;
        font-size: 1.1rem;
    }

    .rating-breakdown {
        padding-left: 2rem;
    }

    .rating-bar-row {
        display: flex;
        align-items: center;
        margin-bottom: 0.75rem;
    }

    .rating-label {
        min-width: 80px;
        font-size: 0.9rem;
        color: #666;
    }

    .rating-bar {
        flex: 1;
        height: 8px;
        background: #f0f0f0;
        border-radius: 4px;
        margin: 0 1rem;
        position: relative;
        overflow: hidden;
    }

    .rating-fill {
        height: 100%;
        background: linear-gradient(90deg, #E73C30, #F96302);
        border-radius: 4px;
        transition: width 0.3s ease;
    }

    .rating-percentage {
        min-width: 40px;
        text-align: right;
        font-size: 0.9rem;
        color: #666;
    }

    /* Review Filters */
    .review-filters {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    .filter-group {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .filter-label {
        font-weight: 600;
        color: #333;
        margin-bottom: 0;
        white-space: nowrap;
    }

    .filter-toggle {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
        margin-bottom: 0;
        font-weight: 500;
    }

    .form-select-sm {
        min-width: 150px;
    }

    /* Write Review Section */
    .write-review-section {
        text-align: center;
    }

    /* Reviews List */
    .reviews-list {
        space-y: 1.5rem;
    }

    .review-item {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        margin-bottom: 1.5rem;
    }

    .review-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .reviewer-info {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .reviewer-avatar img {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        object-fit: cover;
    }

    .reviewer-name {
        font-weight: 600;
        color: #333;
        margin-bottom: 0.25rem;
    }

    .review-meta {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .review-rating .fa-star {
        color: #ddd;
        font-size: 0.9rem;
    }

    .review-rating .fa-star.filled {
        color: #ffc107;
    }

    .review-date {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .verified-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        background: #e8f5e8;
        color: #28a745;
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .review-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.75rem;
    }

    .review-text {
        color: #555;
        line-height: 1.6;
        margin-bottom: 1rem;
    }

    /* Review Media */
    .review-media {
        margin: 1rem 0;
    }

    .media-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 0.75rem;
        max-width: 600px;
    }

    .media-item {
        position: relative;
        aspect-ratio: 1;
        border-radius: 8px;
        overflow: hidden;
        cursor: pointer;
        transition: transform 0.2s ease;
    }

    .media-item:hover {
        transform: scale(1.05);
    }

    .media-thumbnail {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .media-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    .media-item:hover .media-overlay {
        opacity: 1;
    }

    .media-overlay i {
        color: white;
        font-size: 1.5rem;
    }

    .video-duration {
        position: absolute;
        bottom: 5px;
        right: 5px;
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 0.7rem;
    }

    /* Review Footer */
    .review-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #f0f0f0;
    }

    .review-helpfulness {
        display: flex;
        gap: 0.5rem;
    }

    .btn-sm.active {
        background-color: #E73C30;
        border-color: #E73C30;
        color: white;
    }

    /* Review Modal */
    .modal-content {
        border: none;
        border-radius: 15px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .rating-input {
        display: flex;
        gap: 0.25rem;
        margin-bottom: 0.5rem;
    }

    .star-btn {
        background: none;
        border: none;
        color: #ddd;
        font-size: 2rem;
        cursor: pointer;
        transition: color 0.2s ease;
    }

    .star-btn:hover,
    .star-btn.active {
        color: #ffc107;
    }

    .media-upload-area {
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        text-align: center;
        background: #f8f9fa;
    }

    .upload-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .uploaded-files {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 1rem;
    }

    .uploaded-file {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
    }

    .file-preview {
        width: 100%;
        height: 100px;
        object-fit: cover;
    }

    .video-preview {
        width: 100%;
        height: 100px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: #f0f0f0;
        color: #666;
        border-radius: 8px;
    }

    .video-preview i {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .remove-file-btn {
        position: absolute;
        top: 5px;
        right: 5px;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
    }

    /* Image Modal */
    .image-modal,
    .video-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.9);
        z-index: 2000;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .image-modal-content,
    .video-modal-content {
        position: relative;
        max-width: 90vw;
        max-height: 90vh;
        display: flex;
        flex-direction: column;
    }

    .image-modal-header,
    .video-modal-header {
        position: absolute;
        top: 20px;
        right: 20px;
        z-index: 2001;
    }

    .modal-image,
    .modal-video {
        max-width: 100%;
        max-height: calc(90vh - 80px);
        object-fit: contain;
        border-radius: 8px;
    }

    .image-modal-nav {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        align-items: center;
        gap: 1rem;
        background: rgba(0,0,0,0.7);
        padding: 0.5rem 1rem;
        border-radius: 25px;
        color: white;
    }

    .nav-btn {
        background: none;
        border: none;
        color: white;
        font-size: 1.2rem;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 50%;
        transition: background-color 0.2s ease;
    }

    .nav-btn:hover:not(:disabled) {
        background: rgba(255,255,255,0.2);
    }

    .nav-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .image-counter {
        font-size: 0.9rem;
        white-space: nowrap;
    }

    /* No Reviews */
    .no-reviews {
        background: white;
        border-radius: 12px;
        margin: 2rem 0;
    }

    /* Responsive */
    @@media (max-width: 768px) {
        .review-stats-section .row {
            flex-direction: column;
        }

        .rating-breakdown {
            padding-left: 0;
            margin-top: 2rem;
        }

        .review-filters .d-flex {
            flex-direction: column;
            align-items: stretch;
        }

        .filter-group {
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .reviewer-info {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .review-footer {
            flex-direction: column;
            align-items: stretch;
        }

        .media-grid {
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        }

        .upload-buttons {
            flex-direction: column;
        }
    }
</style>

@code {
    [Parameter] public int ProductId { get; set; }
    [Parameter] public string? CurrentUserId { get; set; }
    [Parameter] public bool CanWriteReview { get; set; }

    // State
    private bool IsLoading = true;
    private bool IsLoadingMore = false;
    private bool IsSubmitting = false;
    private bool HasMoreReviews = false;
    private int CurrentPage = 1;
    private const int PageSize = 10;

    // Filters
    private string SortBy = "newest";
    private string FilterRating = "";
    private bool HasMediaFilter = false;
    private bool VerifiedFilter = false;

    // Data
    private ReviewStatsDto? ReviewStats;
    private List<ReviewDto> Reviews = new();
    private HashSet<int> ExpandedReplies = new();

    // Modal states
    private bool ShowReviewModal = false;
    private bool ShowImageModal = false;
    private bool ShowVideoModal = false;
    private ReviewDto? EditingReview = null;

    // Image modal
    private List<ReviewMediaDto> AllImageMedia = new();
    private ReviewMediaDto? CurrentImageMedia;
    private int CurrentImageIndex = 0;

    // Video modal
    private ReviewMediaDto? CurrentVideoMedia;

    // Review form
    private ReviewFormDto ReviewForm = new();
    private List<UploadedFileInfo> UploadedFiles = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadReviewStats();
        await LoadReviews();
    }

    private async Task LoadReviewStats()
    {
        try
        {
            var response = await HttpClient.GetAsync($"/api/v2/reviews/product/{ProductId}/stats");
            if (response.IsSuccessStatusCode)
            {
                ReviewStats = await response.Content.ReadFromJsonAsync<ReviewStatsDto>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading review stats: {ex.Message}");
        }
    }

    private async Task LoadReviews()
    {
        try
        {
            IsLoading = true;
            CurrentPage = 1;

            var query = $"/api/v2/reviews/product/{ProductId}?page={CurrentPage}&pageSize={PageSize}&sortBy={SortBy}";

            if (!string.IsNullOrEmpty(FilterRating))
                query += $"&rating={FilterRating}";

            if (HasMediaFilter)
                query += "&hasMedia=true";

            if (VerifiedFilter)
                query += "&verified=true";

            var response = await HttpClient.GetAsync(query);
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ReviewListDto>();
                if (result != null)
                {
                    Reviews = result.Reviews;
                    HasMoreReviews = result.HasMore;
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading reviews: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadMoreReviews()
    {
        try
        {
            IsLoadingMore = true;
            CurrentPage++;

            var query = $"/api/v2/reviews/product/{ProductId}?page={CurrentPage}&pageSize={PageSize}&sortBy={SortBy}";

            if (!string.IsNullOrEmpty(FilterRating))
                query += $"&rating={FilterRating}";

            if (HasMediaFilter)
                query += "&hasMedia=true";

            if (VerifiedFilter)
                query += "&verified=true";

            var response = await HttpClient.GetAsync(query);
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ReviewListDto>();
                if (result != null)
                {
                    Reviews.AddRange(result.Reviews);
                    HasMoreReviews = result.HasMore;
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading more reviews: {ex.Message}");
        }
        finally
        {
            IsLoadingMore = false;
            StateHasChanged();
        }
    }

    private void ShowWriteReviewModal()
    {
        EditingReview = null;
        ReviewForm = new ReviewFormDto { ProductId = ProductId };
        UploadedFiles.Clear();
        ShowReviewModal = true;
        StateHasChanged();
    }

    private void EditReview(ReviewDto review)
    {
        EditingReview = review;
        ReviewForm = new ReviewFormDto
        {
            ProductId = ProductId,
            Rating = review.Rating,
            Title = review.Title,
            Comment = review.Comment
        };
        UploadedFiles.Clear();
        ShowReviewModal = true;
        StateHasChanged();
    }

    private void CloseReviewModal()
    {
        ShowReviewModal = false;
        EditingReview = null;
        ReviewForm = new();
        UploadedFiles.Clear();
        StateHasChanged();
    }

    private void SetRating(int rating)
    {
        ReviewForm.Rating = rating;
        StateHasChanged();
    }

    private async Task HandleImageUpload(ChangeEventArgs e)
    {
        var files = await JSRuntime.InvokeAsync<IJSObjectReference>("getFiles", "imageUpload");

        if (files != null)
        {
            var length = await files.InvokeAsync<int>("length");
            for (int i = 0; i < length && UploadedFiles.Count < 10; i++)
            {
                var file = await files.InvokeAsync<IJSObjectReference>("item", i);
                var fileType = await file.InvokeAsync<string>("type");
                if (fileType.StartsWith("image/"))
                {
                    var fileName = await file.InvokeAsync<string>("name");
                    var fileSize = await file.InvokeAsync<long>("size");
                    var fileInfo = new UploadedFileInfo
                    {
                        Name = fileName,
                        Size = fileSize,
                        Type = fileType,
                        File = file,
                        PreviewUrl = await JSRuntime.InvokeAsync<string>("URL.createObjectURL", file)
                    };
                    UploadedFiles.Add(fileInfo);
                }
            }
        }
        StateHasChanged();
    }

    private async Task HandleVideoUpload(ChangeEventArgs e)
    {
        var files = await JSRuntime.InvokeAsync<IJSObjectReference>("getFiles", "videoUpload");

        if (files != null)
        {
            var length = await files.InvokeAsync<int>("length");
            for (int i = 0; i < length && UploadedFiles.Count(f => f.Type.StartsWith("video/")) < 3; i++)
            {
                var file = await files.InvokeAsync<IJSObjectReference>("item", i);
                var fileType = await file.InvokeAsync<string>("type");
                if (fileType.StartsWith("video/"))
                {
                    var fileName = await file.InvokeAsync<string>("name");
                    var fileSize = await file.InvokeAsync<long>("size");
                    var fileInfo = new UploadedFileInfo
                    {
                        Name = fileName,
                        Size = fileSize,
                        Type = fileType,
                        File = file
                    };
                    UploadedFiles.Add(fileInfo);
                }
            }
        }
        StateHasChanged();
    }

    private void RemoveUploadedFile(UploadedFileInfo file)
    {
        UploadedFiles.Remove(file);
        StateHasChanged();
    }

    private async Task SubmitReview()
    {
        if (ReviewForm.Rating == 0 || string.IsNullOrEmpty(ReviewForm.Comment) || IsSubmitting)
            return;

        try
        {
            IsSubmitting = true;

            var formData = new MultipartFormDataContent();
            formData.Add(new StringContent(ProductId.ToString()), "ProductId");
            formData.Add(new StringContent(ReviewForm.Rating.ToString()), "Rating");
            formData.Add(new StringContent(ReviewForm.Comment), "Comment");

            if (!string.IsNullOrEmpty(ReviewForm.Title))
                formData.Add(new StringContent(ReviewForm.Title), "Title");

            // Add media files
            foreach (var file in UploadedFiles)
            {
                if (file.File != null)
                {
                    var fileContent = new StreamContent(await JSRuntime.InvokeAsync<Stream>("fileToStream", file.File));
                    fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(file.Type);

                    if (file.Type.StartsWith("image/"))
                    {
                        formData.Add(fileContent, "Images", file.Name);
                    }
                    else if (file.Type.StartsWith("video/"))
                    {
                        formData.Add(fileContent, "Videos", file.Name);
                    }
                }
            }

            var endpoint = EditingReview != null ? $"/api/v2/reviews/{EditingReview.Id}" : "/api/v2/reviews";
            var method = EditingReview != null ? HttpMethod.Put : HttpMethod.Post;

            var response = await HttpClient.SendAsync(new HttpRequestMessage(method, endpoint) { Content = formData });

            if (response.IsSuccessStatusCode)
            {
                await JSRuntime.InvokeVoidAsync("showToast",
                    EditingReview != null ? "Avis modifié avec succès!" : "Avis publié avec succès!",
                    "success");
                CloseReviewModal();
                await LoadReviewStats();
                await LoadReviews();
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("showToast", "Erreur lors de la soumission de l'avis", "error");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error submitting review: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("showToast", "Erreur lors de la soumission de l'avis", "error");
        }
        finally
        {
            IsSubmitting = false;
            StateHasChanged();
        }
    }

    private async Task MarkHelpful(int reviewId, bool isHelpful)
    {
        try
        {
            var response = await HttpClient.PostAsJsonAsync($"/api/v2/reviews/{reviewId}/helpful", isHelpful);
            if (response.IsSuccessStatusCode)
            {
                // Update the review in the list
                var review = Reviews.FirstOrDefault(r => r.Id == reviewId);
                if (review != null)
                {
                    var previousHelpful = review.UserFoundHelpful;
                    review.UserFoundHelpful = isHelpful;

                    // Update counts
                    if (previousHelpful == true && !isHelpful)
                    {
                        review.HelpfulCount--;
                        review.NotHelpfulCount++;
                    }
                    else if (previousHelpful == false && isHelpful)
                    {
                        review.HelpfulCount++;
                        review.NotHelpfulCount--;
                    }
                    else if (previousHelpful == null)
                    {
                        if (isHelpful)
                            review.HelpfulCount++;
                        else
                            review.NotHelpfulCount++;
                    }

                    StateHasChanged();
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error marking review helpful: {ex.Message}");
        }
    }

    private async Task ReportReview(int reviewId)
    {
        var reason = await JSRuntime.InvokeAsync<string>("prompt", "Raison du signalement:");
        if (!string.IsNullOrEmpty(reason))
        {
            try
            {
                var response = await HttpClient.PostAsJsonAsync($"/api/v2/reviews/{reviewId}/report", new { Reason = reason });
                if (response.IsSuccessStatusCode)
                {
                    await JSRuntime.InvokeVoidAsync("showToast", "Avis signalé avec succès", "success");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error reporting review: {ex.Message}");
            }
        }
    }

    private async Task DeleteReview(int reviewId)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Êtes-vous sûr de vouloir supprimer cet avis?");
        if (confirmed)
        {
            try
            {
                var response = await HttpClient.DeleteAsync($"/api/v2/reviews/{reviewId}");
                if (response.IsSuccessStatusCode)
                {
                    Reviews.RemoveAll(r => r.Id == reviewId);
                    await LoadReviewStats();
                    await JSRuntime.InvokeVoidAsync("showToast", "Avis supprimé avec succès", "success");
                    StateHasChanged();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting review: {ex.Message}");
            }
        }
    }

    private void ToggleReplies(int reviewId)
    {
        if (ExpandedReplies.Contains(reviewId))
            ExpandedReplies.Remove(reviewId);
        else
            ExpandedReplies.Add(reviewId);
        StateHasChanged();
    }

    private void OpenImageModal(List<ReviewMediaDto> allMedia, ReviewMediaDto selectedMedia)
    {
        AllImageMedia = allMedia.Where(m => m.MediaType == "Image").ToList();
        CurrentImageMedia = selectedMedia;
        CurrentImageIndex = AllImageMedia.IndexOf(selectedMedia);
        ShowImageModal = true;
        StateHasChanged();
    }

    private void CloseImageModal()
    {
        ShowImageModal = false;
        CurrentImageMedia = null;
        AllImageMedia.Clear();
        StateHasChanged();
    }

    private void PreviousImage()
    {
        if (CurrentImageIndex > 0)
        {
            CurrentImageIndex--;
            CurrentImageMedia = AllImageMedia[CurrentImageIndex];
            StateHasChanged();
        }
    }

    private void NextImage()
    {
        if (CurrentImageIndex < AllImageMedia.Count - 1)
        {
            CurrentImageIndex++;
            CurrentImageMedia = AllImageMedia[CurrentImageIndex];
            StateHasChanged();
        }
    }

    private void OpenVideoModal(ReviewMediaDto videoMedia)
    {
        CurrentVideoMedia = videoMedia;
        ShowVideoModal = true;
        StateHasChanged();
    }

    private void CloseVideoModal()
    {
        ShowVideoModal = false;
        CurrentVideoMedia = null;
        StateHasChanged();
    }

    private string GetAvatarUrl(string userName)
    {
        // Generate a simple avatar URL or use Gravatar
        return $"https://ui-avatars.com/api/?name={Uri.EscapeDataString(userName)}&background=E73C30&color=fff&size=48";
    }

    private string GetDisplayName(string userName)
    {
        // Mask the username for privacy
        if (userName.Length <= 3)
            return userName;
        return $"{userName[0]}***{userName[^1]}";
    }

    private string FormatVideoDuration(int? durationSeconds)
    {
        if (!durationSeconds.HasValue)
            return "";

        var timeSpan = TimeSpan.FromSeconds(durationSeconds.Value);
        return timeSpan.TotalHours >= 1
            ? timeSpan.ToString(@"h\:mm\:ss")
            : timeSpan.ToString(@"m\:ss");
    }

    // DTOs
    public class ReviewStatsDto
    {
        public double AverageRating { get; set; }
        public int TotalReviews { get; set; }
        public Dictionary<int, int> RatingBreakdown { get; set; } = new();
    }

    public class ReviewListDto
    {
        public List<ReviewDto> Reviews { get; set; } = new();
        public bool HasMore { get; set; }
        public int TotalCount { get; set; }
    }

    public class ReviewDto
    {
        public int Id { get; set; }
        public string UserId { get; set; } = "";
        public string UserName { get; set; } = "";
        public int Rating { get; set; }
        public string Comment { get; set; } = "";
        public string? Title { get; set; }
        public bool IsVerified { get; set; }
        public DateTime CreatedAt { get; set; }
        public int HelpfulCount { get; set; }
        public int NotHelpfulCount { get; set; }
        public bool? UserFoundHelpful { get; set; }
        public int ReplyCount { get; set; }
        public List<ReviewMediaDto> Media { get; set; } = new();
    }

    public class ReviewMediaDto
    {
        public int Id { get; set; }
        public string MediaType { get; set; } = "";
        public string FileName { get; set; } = "";
        public string Url { get; set; } = "";
        public string ThumbnailUrl { get; set; } = "";
        public long FileSize { get; set; }
        public DateTime UploadedAt { get; set; }
        public int? Duration { get; set; }
    }

    public class ReviewFormDto
    {
        public int ProductId { get; set; }
        public int Rating { get; set; }
        public string Comment { get; set; } = "";
        public string? Title { get; set; }
    }

    public class UploadedFileInfo
    {
        public string Name { get; set; } = "";
        public long Size { get; set; }
        public string Type { get; set; } = "";
        public object? File { get; set; }
        public string? PreviewUrl { get; set; }
    }
}

<script>
    window.fileToStream = async function(file) {
        return file.stream();
    };

    window.showToast = function(message, type) {
        // Implement toast notification
        console.log(`${type}: ${message}`);
    };
</script>