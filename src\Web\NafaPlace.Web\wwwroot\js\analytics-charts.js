// Scripts pour les graphiques et visualisations analytics
// Utilise Chart.js pour les graphiques avancés avec animations et interactivité

class AnalyticsCharts {
    constructor() {
        this.charts = new Map();
        this.defaultColors = {
            primary: '#667eea',
            secondary: '#764ba2',
            success: '#10b981',
            warning: '#f59e0b',
            danger: '#ef4444',
            info: '#3b82f6',
            light: '#f8f9fa',
            dark: '#1f2937'
        };

        this.gradients = {};
        this.initializeGradients();
    }

    // Initialiser les dégradés
    initializeGradients() {
        // Les dégradés seront créés dynamiquement dans chaque graphique
    }

    // Créer un dégradé
    createGradient(ctx, color1, color2, direction = 'vertical') {
        const gradient = direction === 'vertical'
            ? ctx.createLinearGradient(0, 0, 0, 400)
            : ctx.createLinearGradient(0, 0, 400, 0);

        gradient.addColorStop(0, color1);
        gradient.addColorStop(1, color2);
        return gradient;
    }

    // Graphique de revenus temporels
    initializeRevenueChart(canvasId, data) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return;

        // Détruire le graphique existant
        if (this.charts.has(canvasId)) {
            this.charts.get(canvasId).destroy();
        }

        const gradient = this.createGradient(ctx.getContext('2d'),
            'rgba(102, 126, 234, 0.8)',
            'rgba(102, 126, 234, 0.1)');

        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.map(d => new Date(d.date).toLocaleDateString('fr-FR', {
                    day: '2-digit',
                    month: '2-digit'
                })),
                datasets: [{
                    label: 'Chiffre d\'affaires (GNF)',
                    data: data.map(d => d.value),
                    borderColor: this.defaultColors.primary,
                    backgroundColor: gradient,
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: this.defaultColors.primary,
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        titleColor: '#1f2937',
                        bodyColor: '#6b7280',
                        borderColor: '#e5e7eb',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                return 'Revenus: ' + new Intl.NumberFormat('fr-GN').format(context.parsed.y) + ' GNF';
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6b7280',
                            font: {
                                size: 12
                            }
                        }
                    },
                    y: {
                        grid: {
                            color: '#f3f4f6',
                            borderDash: [5, 5]
                        },
                        ticks: {
                            color: '#6b7280',
                            font: {
                                size: 12
                            },
                            callback: function(value) {
                                return new Intl.NumberFormat('fr-GN', {
                                    notation: 'compact',
                                    compactDisplay: 'short'
                                }).format(value) + ' GNF';
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                animation: {
                    duration: 2000,
                    easing: 'easeOutQuart'
                }
            }
        });

        this.charts.set(canvasId, chart);
        return chart;
    }

    // Graphique de prédictions de ventes
    initializePredictionChart(canvasId, predictions) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return;

        if (this.charts.has(canvasId)) {
            this.charts.get(canvasId).destroy();
        }

        const actualGradient = this.createGradient(ctx.getContext('2d'),
            'rgba(16, 185, 129, 0.8)',
            'rgba(16, 185, 129, 0.1)');

        const predictedGradient = this.createGradient(ctx.getContext('2d'),
            'rgba(59, 130, 246, 0.6)',
            'rgba(59, 130, 246, 0.1)');

        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: predictions.map(p => new Date(p.predictionDate).toLocaleDateString('fr-FR', {
                    day: '2-digit',
                    month: '2-digit'
                })),
                datasets: [
                    {
                        label: 'Prédiction',
                        data: predictions.map(p => p.predictedRevenue),
                        borderColor: this.defaultColors.info,
                        backgroundColor: predictedGradient,
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        borderDash: [5, 5],
                        pointBackgroundColor: this.defaultColors.info,
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 5
                    },
                    {
                        label: 'Borne supérieure',
                        data: predictions.map(p => p.upperBound),
                        borderColor: 'rgba(59, 130, 246, 0.3)',
                        backgroundColor: 'transparent',
                        borderWidth: 1,
                        fill: '+1',
                        tension: 0.4,
                        pointRadius: 0
                    },
                    {
                        label: 'Borne inférieure',
                        data: predictions.map(p => p.lowerBound),
                        borderColor: 'rgba(59, 130, 246, 0.3)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        borderWidth: 1,
                        fill: false,
                        tension: 0.4,
                        pointRadius: 0
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            filter: function(item) {
                                return item.text !== 'Borne supérieure' && item.text !== 'Borne inférieure';
                            },
                            usePointStyle: true,
                            padding: 20,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        titleColor: '#1f2937',
                        bodyColor: '#6b7280',
                        borderColor: '#e5e7eb',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const prediction = predictions[context.dataIndex];
                                return [
                                    'Prédiction: ' + new Intl.NumberFormat('fr-GN').format(context.parsed.y) + ' GNF',
                                    'Confiance: ' + Math.round(prediction.confidenceLevel * 100) + '%',
                                    'Tendance: ' + prediction.trend
                                ];
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6b7280',
                            font: {
                                size: 12
                            }
                        }
                    },
                    y: {
                        grid: {
                            color: '#f3f4f6',
                            borderDash: [5, 5]
                        },
                        ticks: {
                            color: '#6b7280',
                            font: {
                                size: 12
                            },
                            callback: function(value) {
                                return new Intl.NumberFormat('fr-GN', {
                                    notation: 'compact',
                                    compactDisplay: 'short'
                                }).format(value) + ' GNF';
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                animation: {
                    duration: 2500,
                    easing: 'easeOutQuart'
                }
            }
        });

        this.charts.set(canvasId, chart);
        return chart;
    }

    // Graphique en radar pour l'analyse RFM
    initializeRFMChart(canvasId, rfmData) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return;

        if (this.charts.has(canvasId)) {
            this.charts.get(canvasId).destroy();
        }

        const chart = new Chart(ctx, {
            type: 'radar',
            data: {
                labels: ['Récence', 'Fréquence', 'Montant', 'Engagement', 'Fidélité'],
                datasets: rfmData.segments.map((segment, index) => ({
                    label: segment.name,
                    data: [
                        segment.recencyScore,
                        segment.frequencyScore,
                        segment.monetaryScore,
                        segment.engagementScore,
                        segment.loyaltyScore
                    ],
                    borderColor: this.getSegmentColor(index),
                    backgroundColor: this.getSegmentColor(index, 0.2),
                    borderWidth: 2,
                    pointBackgroundColor: this.getSegmentColor(index),
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                }))
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 5,
                        grid: {
                            color: '#f3f4f6'
                        },
                        pointLabels: {
                            font: {
                                size: 12
                            },
                            color: '#6b7280'
                        },
                        ticks: {
                            display: false
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeOutQuart'
                }
            }
        });

        this.charts.set(canvasId, chart);
        return chart;
    }

    // Graphique en aires empilées pour l'analyse de cohort
    initializeCohortChart(canvasId, cohortData) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return;

        if (this.charts.has(canvasId)) {
            this.charts.get(canvasId).destroy();
        }

        const months = Array.from({length: 12}, (_, i) => `Mois ${i + 1}`);

        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: months,
                datasets: cohortData.cohorts.map((cohort, index) => ({
                    label: `Cohorte ${cohort.name}`,
                    data: cohort.retentionRates,
                    borderColor: this.getCohortColor(index),
                    backgroundColor: this.getCohortColor(index, 0.1),
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }))
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 15
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        titleColor: '#1f2937',
                        bodyColor: '#6b7280',
                        borderColor: '#e5e7eb',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + context.parsed.y.toFixed(1) + '%';
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6b7280'
                        }
                    },
                    y: {
                        grid: {
                            color: '#f3f4f6',
                            borderDash: [5, 5]
                        },
                        ticks: {
                            color: '#6b7280',
                            callback: function(value) {
                                return value + '%';
                            }
                        },
                        max: 100
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                animation: {
                    duration: 2000,
                    easing: 'easeOutQuart'
                }
            }
        });

        this.charts.set(canvasId, chart);
        return chart;
    }

    // Graphique de funnel pour le parcours client
    initializeCustomerJourneyChart(canvasId, journeyData) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return;

        if (this.charts.has(canvasId)) {
            this.charts.get(canvasId).destroy();
        }

        const chart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: journeyData.stages.map(stage => stage.name),
                datasets: [{
                    label: 'Nombre de clients',
                    data: journeyData.stages.map(stage => stage.customerCount),
                    backgroundColor: journeyData.stages.map((_, index) =>
                        this.getFunnelColor(index, journeyData.stages.length)),
                    borderColor: this.defaultColors.primary,
                    borderWidth: 1,
                    borderRadius: 8,
                    borderSkipped: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        titleColor: '#1f2937',
                        bodyColor: '#6b7280',
                        borderColor: '#e5e7eb',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const stage = journeyData.stages[context.dataIndex];
                                return [
                                    'Clients: ' + context.parsed.x.toLocaleString(),
                                    'Conversion: ' + stage.conversionRate.toFixed(1) + '%',
                                    'Temps moyen: ' + stage.averageTimeInStage + 'j'
                                ];
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: '#f3f4f6'
                        },
                        ticks: {
                            color: '#6b7280',
                            callback: function(value) {
                                return value.toLocaleString();
                            }
                        }
                    },
                    y: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6b7280'
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeOutQuart'
                }
            }
        });

        this.charts.set(canvasId, chart);
        return chart;
    }

    // Graphique de heatmap pour les performances géographiques
    initializeGeographicHeatmap(canvasId, geographicData) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return;

        // Utilisation d'un graphique en barres horizontales pour représenter les régions
        if (this.charts.has(canvasId)) {
            this.charts.get(canvasId).destroy();
        }

        const chart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: geographicData.map(region => region.region),
                datasets: [
                    {
                        label: 'Revenus (GNF)',
                        data: geographicData.map(region => region.revenue),
                        backgroundColor: geographicData.map((region, index) =>
                            this.getHeatmapColor(region.revenue, geographicData)),
                        borderColor: this.defaultColors.primary,
                        borderWidth: 1,
                        borderRadius: 6,
                        yAxisID: 'y'
                    },
                    {
                        label: 'Croissance (%)',
                        data: geographicData.map(region => region.growthRate * 1000), // Échelle ajustée
                        type: 'line',
                        borderColor: this.defaultColors.warning,
                        backgroundColor: 'transparent',
                        borderWidth: 3,
                        pointRadius: 6,
                        pointBackgroundColor: this.defaultColors.warning,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        titleColor: '#1f2937',
                        bodyColor: '#6b7280',
                        borderColor: '#e5e7eb',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const region = geographicData[context.dataIndex];
                                if (context.datasetIndex === 0) {
                                    return [
                                        'Revenus: ' + new Intl.NumberFormat('fr-GN').format(context.parsed.y) + ' GNF',
                                        'Commandes: ' + region.orders,
                                        'Clients: ' + region.customers
                                    ];
                                } else {
                                    return 'Croissance: ' + region.growthRate.toFixed(1) + '%';
                                }
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6b7280'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        grid: {
                            color: '#f3f4f6'
                        },
                        ticks: {
                            color: '#6b7280',
                            callback: function(value) {
                                return new Intl.NumberFormat('fr-GN', {
                                    notation: 'compact'
                                }).format(value) + ' GNF';
                            }
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false
                        },
                        ticks: {
                            color: '#6b7280',
                            callback: function(value) {
                                return (value / 1000).toFixed(1) + '%';
                            }
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeOutQuart'
                }
            }
        });

        this.charts.set(canvasId, chart);
        return chart;
    }

    // Méthodes utilitaires pour les couleurs
    getSegmentColor(index, opacity = 1) {
        const colors = [
            `rgba(102, 126, 234, ${opacity})`,
            `rgba(16, 185, 129, ${opacity})`,
            `rgba(245, 158, 11, ${opacity})`,
            `rgba(239, 68, 68, ${opacity})`,
            `rgba(139, 92, 246, ${opacity})`
        ];
        return colors[index % colors.length];
    }

    getCohortColor(index, opacity = 1) {
        const hue = (index * 137.508) % 360; // Golden angle pour répartition uniforme
        return `hsla(${hue}, 70%, 50%, ${opacity})`;
    }

    getFunnelColor(index, total) {
        const intensity = 1 - (index / total);
        return `rgba(102, 126, 234, ${0.3 + intensity * 0.7})`;
    }

    getHeatmapColor(value, data) {
        const maxValue = Math.max(...data.map(d => d.revenue));
        const intensity = value / maxValue;
        return `rgba(102, 126, 234, ${0.3 + intensity * 0.7})`;
    }

    // Détruire un graphique spécifique
    destroyChart(canvasId) {
        if (this.charts.has(canvasId)) {
            this.charts.get(canvasId).destroy();
            this.charts.delete(canvasId);
        }
    }

    // Détruire tous les graphiques
    destroyAllCharts() {
        this.charts.forEach(chart => chart.destroy());
        this.charts.clear();
    }

    // Redimensionner tous les graphiques
    resizeAllCharts() {
        this.charts.forEach(chart => chart.resize());
    }

    // Export d'un graphique en image
    exportChart(canvasId, fileName = 'chart.png') {
        if (this.charts.has(canvasId)) {
            const chart = this.charts.get(canvasId);
            const url = chart.toBase64Image();

            const link = document.createElement('a');
            link.download = fileName;
            link.href = url;
            link.click();
        }
    }

    // Mettre à jour les données d'un graphique
    updateChartData(canvasId, newData) {
        if (this.charts.has(canvasId)) {
            const chart = this.charts.get(canvasId);
            chart.data = newData;
            chart.update('active');
        }
    }
}

// Instance globale
window.analyticsCharts = new AnalyticsCharts();

// Fonctions globales pour l'interopérabilité avec Blazor
window.initializeRevenueChart = (canvasId, data) => {
    return window.analyticsCharts.initializeRevenueChart(canvasId, data);
};

window.initializePredictionChart = (canvasId, predictions) => {
    return window.analyticsCharts.initializePredictionChart(canvasId, predictions);
};

window.initializeRFMChart = (canvasId, rfmData) => {
    return window.analyticsCharts.initializeRFMChart(canvasId, rfmData);
};

window.initializeCohortChart = (canvasId, cohortData) => {
    return window.analyticsCharts.initializeCohortChart(canvasId, cohortData);
};

window.initializeCustomerJourneyChart = (canvasId, journeyData) => {
    return window.analyticsCharts.initializeCustomerJourneyChart(canvasId, journeyData);
};

window.initializeGeographicHeatmap = (canvasId, geographicData) => {
    return window.analyticsCharts.initializeGeographicHeatmap(canvasId, geographicData);
};

window.destroyChart = (canvasId) => {
    window.analyticsCharts.destroyChart(canvasId);
};

window.exportAnalyticsChart = (canvasId, fileName) => {
    window.analyticsCharts.exportChart(canvasId, fileName);
};

window.updateAnalyticsChart = (canvasId, newData) => {
    window.analyticsCharts.updateChartData(canvasId, newData);
};

// Export de rapport analytics
window.downloadAnalyticsReport = (dashboardData) => {
    try {
        const report = {
            generatedAt: new Date().toISOString(),
            period: `${dashboardData.fromDate} à ${dashboardData.toDate}`,
            summary: {
                totalRevenue: dashboardData.totalRevenue,
                totalOrders: dashboardData.totalOrders,
                activeCustomers: dashboardData.activeCustomers,
                averageOrderValue: dashboardData.averageOrderValue,
                conversionRate: dashboardData.conversionRate
            },
            categoryPerformance: dashboardData.categoryPerformance,
            topProducts: dashboardData.topProducts,
            geographicData: dashboardData.geographicData
        };

        const blob = new Blob([JSON.stringify(report, null, 2)], {
            type: 'application/json'
        });

        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `analytics-report-${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        URL.revokeObjectURL(url);
    } catch (error) {
        console.error('Erreur lors de l\'export du rapport:', error);
    }
};

// Redimensionnement automatique
window.addEventListener('resize', () => {
    window.analyticsCharts.resizeAllCharts();
});

// Configuration globale Chart.js
if (typeof Chart !== 'undefined') {
    Chart.defaults.font.family = "'Segoe UI', system-ui, -apple-system, sans-serif";
    Chart.defaults.font.size = 12;
    Chart.defaults.color = '#6b7280';
    Chart.defaults.borderColor = '#e5e7eb';
    Chart.defaults.backgroundColor = 'rgba(102, 126, 234, 0.1)';

    // Configuration des animations par défaut
    Chart.defaults.animation.duration = 1500;
    Chart.defaults.animation.easing = 'easeOutQuart';

    // Configuration des interactions
    Chart.defaults.interaction.intersect = false;
    Chart.defaults.interaction.mode = 'index';

    // Configuration des tooltips
    Chart.defaults.plugins.tooltip.cornerRadius = 8;
    Chart.defaults.plugins.tooltip.backgroundColor = 'rgba(255, 255, 255, 0.95)';
    Chart.defaults.plugins.tooltip.titleColor = '#1f2937';
    Chart.defaults.plugins.tooltip.bodyColor = '#6b7280';
    Chart.defaults.plugins.tooltip.borderColor = '#e5e7eb';
    Chart.defaults.plugins.tooltip.borderWidth = 1;
}

console.log('📊 Analytics Charts initialisé pour NafaPlace');