@page "/"
@using NafaPlace.Web.Services
@using NafaPlace.Web.Models.Catalog
@using NafaPlace.Web.Models.Cart
@using NafaPlace.Web.Models.Wishlist
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@using NafaPlace.Web.Components.Reviews
@inject NavigationManager NavigationManager
@inject IProductService ProductService
@inject ICategoryService CategoryService
@inject ICartService CartService
@inject IWishlistService WishlistService
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider

<!-- Hero Section Immersive -->
<section class="hero-immersive mb-4 mb-md-5">
    <div class="hero-video-background">
        <video autoplay muted loop class="hero-video">
            <source src="/videos/nafaplace-hero.mp4" type="video/mp4">
        </video>
        <div class="hero-overlay"></div>
    </div>

    <div class="container hero-content">
        <div class="row align-items-center min-vh-75">
            <div class="col-12 col-lg-6 hero-text-section">
                <div class="hero-badge mb-3">
                    <i class="bi bi-star-fill text-warning me-2"></i>
                    <span>Marketplace #1 en Afrique de l'Ouest</span>
                </div>

                <h1 class="hero-title mb-4">
                    <span class="hero-title-main">Découvrez l'Afrique</span>
                    <span class="hero-title-accent">Authentique</span>
                </h1>

                <p class="hero-description mb-4">
                    Plus de <strong>50,000 produits</strong> authentiques,
                    <strong>2,000+ vendeurs</strong> vérifiés dans 15 pays africains.
                    Livraison rapide et paiements sécurisés.
                </p>

                <div class="hero-stats mb-4">
                    <div class="stat-item">
                        <div class="stat-number">50K+</div>
                        <div class="stat-label">Produits</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">2K+</div>
                        <div class="stat-label">Vendeurs</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">15</div>
                        <div class="stat-label">Pays</div>
                    </div>
                </div>

                <div class="hero-actions">
                    <button class="btn btn-hero-primary btn-lg me-3" @onclick="NavigateToProducts">
                        <i class="bi bi-compass me-2"></i>
                        Explorer Maintenant
                    </button>
                    <button class="btn btn-hero-secondary btn-lg" @onclick="ShowVideoModal">
                        <i class="bi bi-play-circle me-2"></i>
                        Voir la Vidéo
                    </button>
                </div>

                <div class="hero-trust-indicators mt-4">
                    <div class="trust-item">
                        <i class="bi bi-shield-check text-success"></i>
                        <span>Paiements Sécurisés</span>
                    </div>
                    <div class="trust-item">
                        <i class="bi bi-truck text-primary"></i>
                        <span>Livraison Rapide</span>
                    </div>
                    <div class="trust-item">
                        <i class="bi bi-award text-warning"></i>
                        <span>Vendeurs Vérifiés</span>
                    </div>
                </div>
            </div>

            <div class="col-12 col-lg-6 hero-visual-section">
                <div class="hero-product-showcase">
                    <div class="floating-product-card card-1">
                        <img src="/images/showcase/product-1.jpg" alt="Produit 1">
                        <div class="product-info">
                            <h6>Artisanat Traditionnel</h6>
                            <span class="price">25,000 GNF</span>
                        </div>
                    </div>

                    <div class="floating-product-card card-2">
                        <img src="/images/showcase/product-2.jpg" alt="Produit 2">
                        <div class="product-info">
                            <h6>Mode Africaine</h6>
                            <span class="price">45,000 GNF</span>
                        </div>
                    </div>

                    <div class="floating-product-card card-3">
                        <img src="/images/showcase/product-3.jpg" alt="Produit 3">
                        <div class="product-info">
                            <h6>Électronique</h6>
                            <span class="price">120,000 GNF</span>
                        </div>
                    </div>

                    <div class="hero-main-visual">
                        <div class="africa-map-container">
                            <svg class="africa-map" viewBox="0 0 400 400">
                                <!-- SVG de la carte d'Afrique avec animations -->
                                <path class="country-path" d="M100,100 L300,100 L300,300 L100,300 Z" />
                                <!-- Points animés pour les villes -->
                                <circle class="city-point" cx="150" cy="150" r="3" />
                                <circle class="city-point" cx="200" cy="180" r="3" />
                                <circle class="city-point" cx="250" cy="200" r="3" />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="scroll-indicator">
        <div class="scroll-arrow">
            <i class="bi bi-chevron-down"></i>
        </div>
        <span>Découvrir</span>
    </div>
</section>

<!-- Catégories Modernes avec Design Hexagonal -->
<section class="categories-modern-section py-5">
    <div class="container">
        <div class="section-header text-center mb-5">
            <div class="section-badge">
                <i class="bi bi-grid-3x3-gap me-2"></i>
                <span>Explorez par Catégorie</span>
            </div>
            <h2 class="section-title">
                Découvrez nos <span class="text-gradient">Catégories</span>
            </h2>
            <p class="section-subtitle">
                Des produits authentiques dans chaque catégorie, sélectionnés avec soin
            </p>
        </div>

        @if (categories == null)
        {
            <div class="text-center">
                <div class="loading-animation">
                    <div class="loading-spinner"></div>
                    <p class="mt-3">Chargement des catégories...</p>
                </div>
            </div>
        }
        else
        {
            <div class="categories-grid">
                <!-- Catégorie Principale - Toutes les Catégories -->
                <div class="category-card-modern featured-category" @onclick="NavigateToAllCategories">
                    <div class="category-icon-container">
                        <div class="category-icon-bg"></div>
                        <i class="bi bi-grid-3x3-gap category-icon"></i>
                    </div>
                    <div class="category-content">
                        <h3 class="category-title">Toutes les Catégories</h3>
                        <p class="category-description">Explorez notre collection complète</p>
                        <div class="category-stats">
                            <span class="stat-badge">50K+ Produits</span>
                        </div>
                    </div>
                    <div class="category-hover-effect"></div>
                </div>

                @foreach (var category in categories.Take(5))
                {
                    <div class="category-card-modern" @onclick="() => NavigateToCategory(category.Id)">
                        <div class="category-image-container">
                            <img src="@CategoryService.GetImageUrl(category)"
                                 alt="@category.Name"
                                 class="category-image">
                            <div class="category-overlay">
                                <i class="bi bi-arrow-right category-arrow"></i>
                            </div>
                        </div>
                        <div class="category-content">
                            <h4 class="category-title">@category.Name</h4>
                            <p class="category-description">Découvrez nos meilleurs produits</p>
                            <div class="category-stats">
                                <span class="stat-badge">@GetCategoryProductCount(category.Id) Produits</span>
                            </div>
                        </div>
                        <div class="category-hover-effect"></div>
                    </div>
                }
            </div>

            <!-- Bouton Voir Plus -->
            <div class="text-center mt-5">
                <button class="btn btn-outline-primary btn-lg px-5" @onclick="NavigateToAllCategories">
                    <i class="bi bi-plus-circle me-2"></i>
                    Voir Toutes les Catégories
                </button>
            </div>
        }
    </div>

    <!-- Éléments décoratifs -->
    <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
    </div>
</section>

<!-- Offres du Jour - Design Premium -->
<section class="featured-products-section py-5">
    <div class="container">
        <div class="section-header-advanced">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <div class="section-badge-premium">
                        <i class="bi bi-lightning-charge me-2"></i>
                        <span>Offres Limitées</span>
                        <div class="badge-glow"></div>
                    </div>
                    <h2 class="section-title-premium">
                        Offres du <span class="text-gradient-fire">Jour</span>
                    </h2>
                    <p class="section-subtitle-premium">
                        Sélection exclusive de nos meilleurs produits avec des réductions exceptionnelles
                    </p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <div class="countdown-timer">
                        <div class="timer-label">Se termine dans :</div>
                        <div class="timer-display">
                            <div class="timer-unit">
                                <span class="timer-number">23</span>
                                <span class="timer-text">H</span>
                            </div>
                            <div class="timer-separator">:</div>
                            <div class="timer-unit">
                                <span class="timer-number">45</span>
                                <span class="timer-text">M</span>
                            </div>
                            <div class="timer-separator">:</div>
                            <div class="timer-unit">
                                <span class="timer-number">12</span>
                                <span class="timer-text">S</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if (featuredProducts == null)
        {
            <div class="products-loading">
                <div class="loading-grid">
                    @for (int i = 0; i < 8; i++)
                    {
                        <div class="product-skeleton">
                            <div class="skeleton-image"></div>
                            <div class="skeleton-content">
                                <div class="skeleton-line skeleton-title"></div>
                                <div class="skeleton-line skeleton-price"></div>
                                <div class="skeleton-line skeleton-button"></div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        }
        else
        {
            <div class="products-grid-premium">
                @foreach (var product in GetProductsToShow(featuredProducts, 8))
                {
                    <div class="product-card-premium @(product.Stock <= 0 ? "out-of-stock" : "")">
                        <div class="product-image-container">
                            @if (product.DiscountPercentage > 0)
                            {
                                <div class="discount-badge">
                                    <span class="discount-percent">-@product.DiscountPercentage%</span>
                                    <div class="discount-bg"></div>
                                </div>
                            }

                            <div class="product-image-wrapper">
                                <img src="@(product.Images.Any() ? ProductService.GetImageUrl(product.Images.First()) : "/images/placeholder.png")"
                                     alt="@product.Name"
                                     class="product-image">
                                <div class="image-overlay">
                                    <div class="quick-actions">
                                        <button class="quick-action-btn" title="Aperçu rapide">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button class="quick-action-btn" title="Comparer">
                                            <i class="bi bi-arrow-left-right"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            @if (product.Stock <= 0)
                            {
                                <div class="stock-overlay">
                                    <span class="stock-text">Rupture de Stock</span>
                                </div>
                            }
                        </div>

                        <div class="product-content">
                            <div class="product-rating">
                                <div class="stars">
                                    @for (int i = 1; i <= 5; i++)
                                    {
                                        <i class="bi @(i <= product.Rating ? "bi-star-fill" : "bi-star") star"></i>
                                    }
                                </div>
                                <span class="rating-count">(@product.ReviewCount)</span>
                            </div>

                            <h3 class="product-title">
                                <a href="/catalog/products/@product.Id">@product.Name</a>
                            </h3>

                            <p class="product-description">
                                @(product.ShortDescription ?? product.Description?.Substring(0, Math.Min(product.Description.Length, 80)) ?? "")
                            </p>

                            <div class="product-pricing">
                                <span class="current-price">@product.Price.ToString("N0") GNF</span>
                                @if (product.OldPrice.HasValue && product.OldPrice > product.Price)
                                {
                                    <span class="old-price">@product.OldPrice.Value.ToString("N0") GNF</span>
                                }
                            </div>

                            <div class="product-actions">
                                @if (product.Stock > 0)
                                {
                                    <button class="btn-add-to-cart" @onclick="() => AddToCart(product.Id)">
                                        <i class="bi bi-cart-plus me-2"></i>
                                        <span>Ajouter au Panier</span>
                                        <div class="btn-ripple"></div>
                                    </button>
                                }
                                else
                                {
                                    <button class="btn-out-of-stock" disabled>
                                        <i class="bi bi-x-circle me-2"></i>
                                        <span>Stock Épuisé</span>
                                    </button>
                                }

                                <button class="btn-wishlist" title="Ajouter aux favoris">
                                    <i class="bi bi-heart"></i>
                                </button>
                            </div>
                        </div>

                        <div class="product-hover-effect"></div>
                    </div>
                }
            </div>

            <div class="section-footer text-center mt-5">
                <a href="/catalog" class="btn-view-all-premium">
                    <span>Voir Toutes les Offres</span>
                    <i class="bi bi-arrow-right ms-2"></i>
                    <div class="btn-glow"></div>
                </a>
            </div>
        }
    </div>
</section>

<!-- Section Témoignages et Social Proof -->
<section class="testimonials-social-section py-5">
    <div class="container">
        <div class="row">
            <!-- Témoignages Clients -->
            <div class="col-lg-8">
                <div class="testimonials-container">
                    <div class="section-header-testimonials">
                        <div class="testimonials-badge">
                            <i class="bi bi-chat-heart me-2"></i>
                            <span>Témoignages Clients</span>
                        </div>
                        <h2 class="testimonials-title">
                            Ce que disent nos <span class="text-gradient">Clients</span>
                        </h2>
                    </div>

                    <div class="testimonials-grid">
                        <div class="testimonial-card featured">
                            <div class="testimonial-content">
                                <div class="quote-icon">
                                    <i class="bi bi-quote"></i>
                                </div>
                                <p class="testimonial-text">
                                    "NafaPlace a révolutionné ma façon d'acheter en ligne. La qualité des produits africains est exceptionnelle et la livraison ultra rapide !"
                                </p>
                                <div class="testimonial-rating">
                                    @for (int i = 0; i < 5; i++)
                                    {
                                        <i class="bi bi-star-fill"></i>
                                    }
                                </div>
                            </div>
                            <div class="testimonial-author">
                                <img src="/images/testimonials/client-1.jpg" alt="Aminata Diallo" class="author-avatar">
                                <div class="author-info">
                                    <h4 class="author-name">Aminata Diallo</h4>
                                    <p class="author-location">Conakry, Guinée</p>
                                </div>
                                <div class="verified-badge">
                                    <i class="bi bi-patch-check-fill"></i>
                                </div>
                            </div>
                        </div>

                        <div class="testimonial-card">
                            <div class="testimonial-content">
                                <p class="testimonial-text">
                                    "Service client exceptionnel ! J'ai eu un problème avec ma commande et ils l'ont résolu en moins de 2 heures."
                                </p>
                                <div class="testimonial-rating">
                                    @for (int i = 0; i < 5; i++)
                                    {
                                        <i class="bi bi-star-fill"></i>
                                    }
                                </div>
                            </div>
                            <div class="testimonial-author">
                                <img src="/images/testimonials/client-2.jpg" alt="Mamadou Bah" class="author-avatar">
                                <div class="author-info">
                                    <h4 class="author-name">Mamadou Bah</h4>
                                    <p class="author-location">Dakar, Sénégal</p>
                                </div>
                            </div>
                        </div>

                        <div class="testimonial-card">
                            <div class="testimonial-content">
                                <p class="testimonial-text">
                                    "Les produits artisanaux sont magnifiques et authentiques. Je recommande vivement NafaPlace !"
                                </p>
                                <div class="testimonial-rating">
                                    @for (int i = 0; i < 5; i++)
                                    {
                                        <i class="bi bi-star-fill"></i>
                                    }
                                </div>
                            </div>
                            <div class="testimonial-author">
                                <img src="/images/testimonials/client-3.jpg" alt="Fatou Keita" class="author-avatar">
                                <div class="author-info">
                                    <h4 class="author-name">Fatou Keita</h4>
                                    <p class="author-location">Bamako, Mali</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Social Proof et Stats -->
            <div class="col-lg-4">
                <div class="social-proof-container">
                    <div class="trust-stats">
                        <h3 class="trust-title">Ils nous font confiance</h3>

                        <div class="stat-item-large">
                            <div class="stat-icon">
                                <i class="bi bi-people-fill"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number">50,000+</div>
                                <div class="stat-label">Clients Satisfaits</div>
                            </div>
                        </div>

                        <div class="stat-item-large">
                            <div class="stat-icon">
                                <i class="bi bi-star-fill"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number">4.9/5</div>
                                <div class="stat-label">Note Moyenne</div>
                            </div>
                        </div>

                        <div class="stat-item-large">
                            <div class="stat-icon">
                                <i class="bi bi-truck"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number">98%</div>
                                <div class="stat-label">Livraisons Réussies</div>
                            </div>
                        </div>
                    </div>

                    <div class="recent-activity">
                        <h4 class="activity-title">Activité Récente</h4>
                        <div class="activity-feed">
                            <div class="activity-item">
                                <div class="activity-avatar">
                                    <img src="/images/avatars/user-1.jpg" alt="User">
                                </div>
                                <div class="activity-content">
                                    <p><strong>Aissata</strong> a acheté un <em>Boubou Traditionnel</em></p>
                                    <span class="activity-time">Il y a 2 minutes</span>
                                </div>
                            </div>

                            <div class="activity-item">
                                <div class="activity-avatar">
                                    <img src="/images/avatars/user-2.jpg" alt="User">
                                </div>
                                <div class="activity-content">
                                    <p><strong>Ibrahim</strong> a ajouté 3 produits au panier</p>
                                    <span class="activity-time">Il y a 5 minutes</span>
                                </div>
                            </div>

                            <div class="activity-item">
                                <div class="activity-avatar">
                                    <img src="/images/avatars/user-3.jpg" alt="User">
                                </div>
                                <div class="activity-content">
                                    <p><strong>Mariam</strong> a laissé un avis 5 étoiles</p>
                                    <span class="activity-time">Il y a 8 minutes</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="promo-card">
                        <div class="promo-icon">
                            <i class="bi bi-gift"></i>
                        </div>
                        <div class="promo-content">
                            <h4>Première Commande</h4>
                            <p>Livraison gratuite avec le code <strong>BIENVENUE</strong></p>
                            <button class="btn-promo" @onclick="NavigateToProducts">
                                En Profiter
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Nouveaux arrivages -->
<section class="products-section">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2>Nouveaux arrivages</h2>
                <p class="section-subtitle">Les derniers produits ajoutés par nos vendeurs</p>
            </div>
            <a href="/catalog" class="btn-view-all">Voir tout</a>
        </div>
        <div class="row g-4">
            @if (newProducts == null)
            {
                <div class="col-12 text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            }
            else
            {
                @foreach (var product in GetProductsToShow(newProducts, 8))
                {
                    <div class="col-12 col-sm-6 col-md-3">
                        <div class="card product-card h-100 @(product.Stock <= 0 ? "product-out-of-stock" : "")">
                            <div class="position-relative">
                                <span class="badge-new">NOUVEAU</span>
                                <a href="/catalog/products/@product.Id">
                                    <img src="@(product.Images.Any() ? ProductService.GetImageUrl(product.Images.First()) : "/images/placeholder.png")"
                                         class="card-img-top" alt="@product.Name">
                                </a>
                                @if (product.Stock <= 0)
                                {
                                    <div class="product-out-of-stock-overlay">
                                        <div class="product-out-of-stock-text">
                                            RUPTURE DE STOCK
                                        </div>
                                    </div>
                                }
                            </div>
                            <div class="card-body">
                                <h5 class="card-title"><a href="/catalog/products/@product.Id" class="text-decoration-none text-dark">@product.Name</a></h5>
                                <div class="d-flex mb-2">
                                    <StarRating Rating="product.Rating" ShowRatingText="false" CssClass="me-2" />
                                    <small>(@product.ReviewCount)</small>
                                </div>
                                <p class="card-text">@(product.ShortDescription ?? product.Description?.Substring(0, Math.Min(product.Description.Length, 100)) ?? "")</p>
                                <div class="mb-3">
                                    <span class="product-price">@product.Price.ToString("N0") GNF</span>
                                </div>
                                <div class="d-flex">
                                    @if (product.Stock > 0)
                                    {
                                        <button class="btn btn-primary me-2" @onclick="() => AddToCart(product.Id)">
                                            <i class="fas fa-shopping-cart me-1"></i> Ajouter
                                        </button>
                                    }
                                    else
                                    {
                                        <button class="btn btn-secondary me-2" disabled>
                                            <i class="fas fa-times me-1"></i> Stock épuisé
                                        </button>
                                    }
                                    <NafaPlace.Web.Shared.Components.WishlistButton Product="product" />
                                </div>
                            </div>
                        </div>
                    </div>
                }
            }
        </div>
    </div>
</section>

<!-- Newsletter et Engagement Avancée -->
<section class="newsletter-engagement-section py-5">
    <div class="container">
        <div class="newsletter-container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="newsletter-content">
                        <div class="newsletter-badge">
                            <i class="bi bi-envelope-heart me-2"></i>
                            <span>Restez Connecté</span>
                        </div>

                        <h2 class="newsletter-title">
                            Ne manquez aucune <span class="text-gradient">Offre Exclusive</span>
                        </h2>

                        <p class="newsletter-description">
                            Rejoignez plus de <strong>25,000 abonnés</strong> et recevez en avant-première :
                        </p>

                        <div class="benefits-list">
                            <div class="benefit-item">
                                <i class="bi bi-check-circle-fill"></i>
                                <span>Offres exclusives et codes promo</span>
                            </div>
                            <div class="benefit-item">
                                <i class="bi bi-check-circle-fill"></i>
                                <span>Nouveaux produits en avant-première</span>
                            </div>
                            <div class="benefit-item">
                                <i class="bi bi-check-circle-fill"></i>
                                <span>Conseils et tendances mode africaine</span>
                            </div>
                            <div class="benefit-item">
                                <i class="bi bi-check-circle-fill"></i>
                                <span>Invitations aux événements spéciaux</span>
                            </div>
                        </div>

                        <div class="newsletter-form">
                            <div class="form-group">
                                <input type="email"
                                       class="newsletter-input"
                                       placeholder="Votre adresse email"
                                       @bind="newsletterEmail">
                                <button class="newsletter-btn" @onclick="SubscribeNewsletter">
                                    <span class="btn-text">S'abonner</span>
                                    <i class="bi bi-arrow-right btn-icon"></i>
                                    <div class="btn-ripple"></div>
                                </button>
                            </div>
                            <p class="newsletter-privacy">
                                <i class="bi bi-shield-check me-1"></i>
                                Vos données sont protégées. Désabonnement en un clic.
                            </p>
                        </div>

                        <div class="social-proof-mini">
                            <div class="subscriber-avatars">
                                <img src="/images/avatars/sub-1.jpg" alt="Abonné" class="subscriber-avatar">
                                <img src="/images/avatars/sub-2.jpg" alt="Abonné" class="subscriber-avatar">
                                <img src="/images/avatars/sub-3.jpg" alt="Abonné" class="subscriber-avatar">
                                <img src="/images/avatars/sub-4.jpg" alt="Abonné" class="subscriber-avatar">
                                <div class="subscriber-count">+25K</div>
                            </div>
                            <p class="social-proof-text">
                                <strong>25,847 personnes</strong> nous font déjà confiance
                            </p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="newsletter-visual">
                        <div class="newsletter-mockup">
                            <div class="mockup-phone">
                                <div class="phone-screen">
                                    <div class="notification-preview">
                                        <div class="notification-header">
                                            <img src="/images/nafaplace-logo-simple.svg" alt="NafaPlace" class="notification-logo">
                                            <span class="notification-time">Maintenant</span>
                                        </div>
                                        <div class="notification-content">
                                            <h4>🔥 Offre Flash : -50%</h4>
                                            <p>Collection Boubous Traditionnels</p>
                                            <div class="notification-image">
                                                <img src="/images/categories/fashion.jpg" alt="Promo">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="floating-elements">
                                <div class="floating-discount">
                                    <span>-30%</span>
                                </div>
                                <div class="floating-heart">
                                    <i class="bi bi-heart-fill"></i>
                                </div>
                                <div class="floating-star">
                                    <i class="bi bi-star-fill"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Recommandations IA Intégrée -->
        <div class="ai-recommendations-section mt-5">
            <div class="section-header-ai">
                <div class="ai-badge">
                    <i class="bi bi-robot me-2"></i>
                    <span>Intelligence Artificielle</span>
                </div>
                <h3 class="ai-title">Recommandé Spécialement pour Vous</h3>
                <p class="ai-subtitle">Notre IA analyse vos préférences pour vous proposer les meilleurs produits</p>
            </div>

            <div class="ai-products-grid">
                @if (recommendedProducts != null && recommendedProducts.Any())
                {
                    @foreach (var product in recommendedProducts.Take(4))
                    {
                        <div class="ai-product-card">
                            <div class="ai-badge-product">
                                <i class="bi bi-cpu"></i>
                                <span>IA</span>
                            </div>
                            <div class="product-image-ai">
                                <img src="@product.MainImageUrl" alt="@product.Name">
                            </div>
                            <div class="product-content-ai">
                                <h4 class="product-title-ai">@product.Name</h4>
                                <div class="product-rating-ai">
                                    @for (int i = 1; i <= 5; i++)
                                    {
                                        <i class="bi @(i <= product.Rating ? "bi-star-fill" : "bi-star")"></i>
                                    }
                                </div>
                                <div class="product-price-ai">@product.Price.ToString("N0") GNF</div>
                                <button class="btn-add-ai" @onclick="() => AddToCart(product.Id)">
                                    <i class="bi bi-cart-plus me-1"></i>
                                    Ajouter
                                </button>
                            </div>
                        </div>
                    }
                }
                else
                {
                    @for (int i = 0; i < 4; i++)
                    {
                        <div class="ai-product-skeleton">
                            <div class="skeleton-image-ai"></div>
                            <div class="skeleton-content-ai">
                                <div class="skeleton-line"></div>
                                <div class="skeleton-line short"></div>
                                <div class="skeleton-button"></div>
                            </div>
                        </div>
                    }
                }
            </div>

            <div class="text-center mt-4">
                <a href="/recommendations" class="btn-view-ai">
                    <i class="bi bi-robot me-2"></i>
                    Voir Toutes les Recommandations IA
                    <i class="bi bi-arrow-right ms-2"></i>
                </a>
            </div>
        </div>
    </div>
</section>



@code {
    private IEnumerable<CategoryDto> categories = Array.Empty<CategoryDto>();
    private IEnumerable<ProductDto> featuredProducts = Array.Empty<ProductDto>();
    private IEnumerable<ProductDto> newProducts = Array.Empty<ProductDto>();
    private IEnumerable<ProductDto> recommendedProducts = Array.Empty<ProductDto>();
    private string? _userId;

    protected override async Task OnInitializedAsync()
    {
        // Récupérer l'UserId de l'utilisateur connecté
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        if (user.Identity?.IsAuthenticated == true)
        {
            _userId = user.FindFirst(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
            Console.WriteLine($"🔍 DEBUG Index: UserId récupéré = '{_userId}'");
        }
        else
        {
            Console.WriteLine("❌ DEBUG Index: Utilisateur non authentifié");
        }

        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            categories = await CategoryService.GetAllCategoriesAsync();

            // Charger les produits en vedette
            featuredProducts = await ProductService.GetFeaturedProductsAsync(16);
            // S'assurer qu'on a au moins 8 produits en vedette
            if (featuredProducts.Count() < 8)
            {
                var allProducts = await ProductService.GetAllProductsAsync();
                featuredProducts = allProducts.Take(8).ToList();
            }

            // Charger les nouveaux produits
            newProducts = await ProductService.GetNewProductsAsync(16);
            // S'assurer qu'on a au moins 8 nouveaux produits
            if (newProducts.Count() < 8)
            {
                var allProducts = await ProductService.GetAllProductsAsync();
                newProducts = allProducts.Take(8).ToList();
            }

            // Charger les recommandations (pour l'instant, utiliser les produits populaires)
            recommendedProducts = await ProductService.GetAllProductsAsync();
            recommendedProducts = recommendedProducts.Take(8).ToList();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading data: {ex.Message}");
            // Gérer l'erreur
        }
    }

    private void NavigateToProducts()
    {
        NavigationManager.NavigateTo("/catalog/products");
    }

    private void NavigateToAbout()
    {
        NavigationManager.NavigateTo("/about");
    }

    private void NavigateToCategory(int categoryId)
    {
        NavigationManager.NavigateTo($"/catalog?categoryId={categoryId}");
    }

    private void NavigateToAllCategories()
    {
        NavigationManager.NavigateTo("/catalog");
    }

    private async Task AddToCart(int productId)
    {
        Console.WriteLine($"🔍 DEBUG Index: Tentative d'ajout au panier - ProductId: {productId}");

        string userId = _userId ?? string.Empty;

        if (string.IsNullOrEmpty(userId))
        {
            // Utiliser un ID de session temporaire pour les utilisateurs non connectés
            userId = await GetOrCreateGuestUserId();
            Console.WriteLine($"🔍 DEBUG Index: Utilisateur invité - GuestId: {userId}");
        }

        try
        {
            Console.WriteLine($"🛒 DEBUG Index: Création de l'item panier - ProductId: {productId}, Quantity: 1");
            var cartItem = new CartItemCreateDto { ProductId = productId, Quantity = 1 };

            Console.WriteLine($"📡 DEBUG Index: Appel API AddItemToCartAsync...");
            var result = await CartService.AddItemToCartAsync(userId, cartItem);

            Console.WriteLine($"✅ DEBUG Index: Produit ajouté avec succès - ItemCount: {result?.ItemCount ?? 0}");

            // Notification de succès
            await JSRuntime.InvokeVoidAsync("showToast", $"✅ Produit ajouté au panier !", "success");
            Console.WriteLine($"Produit {productId} ajouté au panier.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ DEBUG Index: Erreur lors de l'ajout au panier: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("showToast", $"❌ Erreur: {ex.Message}", "error");
        }
    }

    private async Task<string> GetOrCreateGuestUserId()
    {
        try
        {
            var guestId = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "guestUserId");
            if (string.IsNullOrEmpty(guestId))
            {
                guestId = $"guest_{Random.Shared.Next(1, int.MaxValue)}";
                await JSRuntime.InvokeVoidAsync("localStorage.setItem", "guestUserId", guestId);
                Console.WriteLine($"🔍 DEBUG Index: Nouvel ID invité créé: {guestId}");
            }
            else
            {
                Console.WriteLine($"🔍 DEBUG Index: ID invité existant récupéré: {guestId}");
            }
            return guestId;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ DEBUG Index: Erreur lors de la gestion de l'ID invité: {ex.Message}");
            return $"guest_{Random.Shared.Next(1, int.MaxValue)}";
        }
    }

    private IEnumerable<ProductDto> GetProductsToShow(IEnumerable<ProductDto> products, int maxCount)
    {
        var allProducts = products.ToList();

        // Si on a 4 produits ou moins, les retourner tous
        if (allProducts.Count <= 4)
        {
            return allProducts;
        }

        // Prendre jusqu'à maxCount produits
        var productsToShow = allProducts.Take(maxCount).ToList();

        // Calculer combien de lignes complètes on peut faire
        var completeRows = productsToShow.Count / 4;
        var targetCount = completeRows * 4;

        // S'assurer qu'on a au moins 4 produits (1 ligne complète)
        if (targetCount < 4 && allProducts.Count >= 4)
        {
            targetCount = 4;
        }

        return allProducts.Take(targetCount);
    }

    private string GetCategoryProductCount(int categoryId)
    {
        // Simulation du nombre de produits par catégorie
        // Dans une vraie implémentation, ceci devrait venir du service
        var random = new Random(categoryId);
        var count = random.Next(50, 500);
        return count.ToString();
    }

    private async Task ShowVideoModal()
    {
        // Logique pour afficher la modal vidéo
        await JSRuntime.InvokeVoidAsync("showVideoModal");
    }

    private string newsletterEmail = "";

    private async Task SubscribeNewsletter()
    {
        if (string.IsNullOrWhiteSpace(newsletterEmail))
        {
            await JSRuntime.InvokeVoidAsync("showToast", "Veuillez saisir votre adresse email", "warning");
            return;
        }

        if (!IsValidEmail(newsletterEmail))
        {
            await JSRuntime.InvokeVoidAsync("showToast", "Veuillez saisir une adresse email valide", "error");
            return;
        }

        try
        {
            // Ici vous pouvez ajouter la logique pour sauvegarder l'email en base
            // await NewsletterService.SubscribeAsync(newsletterEmail);

            await JSRuntime.InvokeVoidAsync("showToast", "Merci ! Vous êtes maintenant abonné à notre newsletter", "success");
            newsletterEmail = "";
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("showToast", "Une erreur s'est produite. Veuillez réessayer.", "error");
        }
    }

    private bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }


}
