# 🚀 Guide de Déploiement NafaPlace sur Render

## 🎯 Objectif
Ce guide vous permet de déployer NafaPlace sur Render **sans affecter votre environnement local**.

## ✅ Prérequis
- Compte Render.com
- Git configuré
- Projet NafaPlace fonctionnel en local

## 🔧 Configuration Initiale (Une seule fois)

### 1. Connecter votre repository GitHub à Render
1. Allez sur [render.com](https://render.com)
2. Connectez votre compte GitHub
3. Sélectionnez le repository `nafaplace`

### 2. Créer la base de données PostgreSQL
1. Dans Render Dashboard → "New" → "PostgreSQL"
2. Nom: `nafaplace-postgres`
3. Database Name: `nafaplace`
4. User: `nafaplace`
5. Region: `Frankfurt` (ou votre région préférée)
6. Plan: `Free` (pour commencer)

### 3. Configurer les variables d'environnement secrètes
Dans Render Dashboard, pour chaque service, ajoutez ces variables :

#### Variables communes à tous les services :
```
ASPNETCORE_ENVIRONMENT=Production
ASPNETCORE_URLS=http://+:10000
```

#### Variables spécifiques :
- **Cloudinary** (pour les images) :
  - `CLOUDINARY_CLOUD_NAME=votre_cloud_name`
  - `CLOUDINARY_API_KEY=votre_api_key`
  - `CLOUDINARY_API_SECRET=votre_api_secret`

- **Stripe** (pour les paiements) :
  - `STRIPE_PUBLISHABLE_KEY=pk_live_...`
  - `STRIPE_SECRET_KEY=sk_live_...`

- **Orange Money** :
  - `ORANGEMONEY_MERCHANT_ID=votre_merchant_id`
  - `ORANGEMONEY_SECRET_KEY=votre_secret_key`

## 🚀 Déploiement

### Option 1 : Déploiement automatique avec le script
```powershell
# Déployer tous les services
.\deploy-render.ps1

# Déployer seulement les services API
.\deploy-render.ps1 -OnlyServices

# Déployer seulement les portails web
.\deploy-render.ps1 -OnlyPortals

# Déployer un service spécifique
.\deploy-render.ps1 -ServiceName "nafaplace-identity-api"
```

### Option 2 : Déploiement manuel via Render Dashboard
1. Utilisez le fichier `render.yaml` pour créer tous les services
2. Render détectera automatiquement les changements sur GitHub
3. Les déploiements se feront automatiquement à chaque push

## 🔍 Vérification du Déploiement

### URLs de Production :
- **🌐 Site Web Principal** : https://nafaplace-web.onrender.com
- **👨‍💼 Portail Admin** : https://nafaplace-admin-portal.onrender.com  
- **🏪 Portail Vendeur** : https://nafaplace-seller-portal.onrender.com
- **🔗 API Gateway** : https://nafaplace-api-gateway.onrender.com

### Tests de Santé :
```powershell
# Tester l'API Gateway
curl https://nafaplace-api-gateway.onrender.com/health

# Tester l'Identity API
curl https://nafaplace-identity-api.onrender.com/health

# Tester le Catalog API
curl https://nafaplace-catalog-api.onrender.com/health
```

## 🛡️ Sécurité et Bonnes Pratiques

### 1. Variables d'Environnement
- ❌ Ne jamais commiter les clés secrètes
- ✅ Utiliser les variables d'environnement Render
- ✅ Différencier les clés de développement et production

### 2. Base de Données
- ✅ Utiliser PostgreSQL managé par Render
- ✅ Sauvegardes automatiques activées
- ✅ Connexions SSL forcées en production

### 3. Monitoring
- ✅ Logs centralisés dans Render Dashboard
- ✅ Alertes configurées pour les erreurs
- ✅ Métriques de performance surveillées

## 🔄 Mise à Jour Continue

### Déploiement Automatique
Chaque `git push` sur la branche `master` déclenche automatiquement :
1. Build des images Docker
2. Tests de santé
3. Déploiement progressif
4. Vérification des endpoints

### Rollback Rapide
En cas de problème :
```bash
# Via Render Dashboard
1. Aller dans le service concerné
2. Cliquer sur "Rollback" 
3. Sélectionner la version précédente
```

## 🏠 Préservation de l'Environnement Local

### ✅ Ce qui est préservé :
- Votre `docker-compose.yml` local
- Vos bases de données locales
- Vos configurations de développement
- Vos ports locaux (8080, 8081, 8082)

### ✅ Ce qui est séparé :
- Configuration de production dans `appsettings.Production.json`
- Dockerfiles spécifiques Render (`*.render`)
- Variables d'environnement de production
- Base de données de production

## 🆘 Dépannage

### Problème : Service ne démarre pas
1. Vérifier les logs dans Render Dashboard
2. Vérifier les variables d'environnement
3. Vérifier la connexion à la base de données

### Problème : Erreur de connexion entre services
1. Vérifier les URLs des services dans `render.yaml`
2. Vérifier que tous les services sont déployés
3. Vérifier les règles CORS

### Problème : Base de données inaccessible
1. Vérifier que PostgreSQL est démarré
2. Vérifier la chaîne de connexion
3. Vérifier les migrations de base de données

## 📞 Support
- Documentation Render : https://render.com/docs
- Logs en temps réel : Render Dashboard → Service → Logs
- Métriques : Render Dashboard → Service → Metrics

---

**💡 Rappel Important :** Votre environnement local continue de fonctionner normalement avec `docker-compose up --build -d`
