using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System.Security.Claims;

namespace NafaPlace.Delivery.Infrastructure.Hubs;

/// <summary>
/// Hub SignalR pour les notifications de livraison en temps réel
/// Gère les notifications push pour clients, livreurs et administrateurs
/// </summary>
[Authorize]
public class DeliveryNotificationHub : Hub
{
    private readonly ILogger<DeliveryNotificationHub> _logger;

    public DeliveryNotificationHub(ILogger<DeliveryNotificationHub> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Connexion d'un utilisateur au hub de notifications
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        var userId = Context.UserIdentifier;
        var userRole = Context.User?.FindFirst(ClaimTypes.Role)?.Value;

        _logger.LogInformation("Utilisateur {UserId} connecté au hub de notifications avec le rôle {Role}", userId, userRole);

        // Joindre le groupe personnel
        if (!string.IsNullOrEmpty(userId))
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, $"User_{userId}");
        }

        // Joindre les groupes selon le rôle
        switch (userRole)
        {
            case "DeliveryPerson":
                await Groups.AddToGroupAsync(Context.ConnectionId, "DeliveryPersons_Notifications");
                break;
            case "Admin":
            case "Manager":
                await Groups.AddToGroupAsync(Context.ConnectionId, "Admin_Notifications");
                break;
            default:
                await Groups.AddToGroupAsync(Context.ConnectionId, "Customer_Notifications");
                break;
        }

        await base.OnConnectedAsync();
    }

    /// <summary>
    /// Déconnexion d'un utilisateur du hub
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var userId = Context.UserIdentifier;
        _logger.LogInformation("Utilisateur {UserId} déconnecté du hub de notifications", userId);

        if (exception != null)
        {
            _logger.LogError(exception, "Erreur lors de la déconnexion de {UserId}", userId);
        }

        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// S'abonner aux notifications d'une livraison spécifique
    /// </summary>
    public async Task SubscribeToDeliveryNotifications(int deliveryId)
    {
        var userId = Context.UserIdentifier;
        if (string.IsNullOrEmpty(userId))
            return;

        var groupName = $"DeliveryNotifications_{deliveryId}";
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);

        _logger.LogInformation("Utilisateur {UserId} s'est abonné aux notifications de la livraison {DeliveryId}", userId, deliveryId);
    }

    /// <summary>
    /// Se désabonner des notifications d'une livraison
    /// </summary>
    public async Task UnsubscribeFromDeliveryNotifications(int deliveryId)
    {
        var groupName = $"DeliveryNotifications_{deliveryId}";
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);

        _logger.LogInformation("Utilisateur {UserId} s'est désabonné des notifications de la livraison {DeliveryId}", Context.UserIdentifier, deliveryId);
    }

    /// <summary>
    /// Marquer une notification comme lue
    /// </summary>
    public async Task MarkNotificationAsRead(string notificationId)
    {
        var userId = Context.UserIdentifier;
        if (string.IsNullOrEmpty(userId))
            return;

        _logger.LogInformation("Notification {NotificationId} marquée comme lue par {UserId}", notificationId, userId);

        // Ici on pourrait mettre à jour la base de données
        await Clients.Caller.SendAsync("NotificationMarkedAsRead", notificationId);
    }

    /// <summary>
    /// Demander les notifications non lues
    /// </summary>
    public async Task RequestUnreadNotifications()
    {
        var userId = Context.UserIdentifier;
        if (string.IsNullOrEmpty(userId))
            return;

        _logger.LogInformation("Demande de notifications non lues pour {UserId}", userId);

        // Simuler des notifications non lues - à remplacer par la vraie logique
        var unreadNotifications = new[]
        {
            new {
                Id = Guid.NewGuid().ToString(),
                Title = "Livraison en cours",
                Message = "Votre commande #12345 est en route",
                Type = "delivery_update",
                Timestamp = DateTime.UtcNow.AddMinutes(-10),
                IsRead = false
            }
        };

        await Clients.Caller.SendAsync("UnreadNotificationsResponse", unreadNotifications);
    }

    /// <summary>
    /// S'abonner aux notifications de proximité (quand le livreur approche)
    /// </summary>
    public async Task SubscribeToProximityNotifications(int deliveryId)
    {
        var userId = Context.UserIdentifier;
        if (string.IsNullOrEmpty(userId))
            return;

        var groupName = $"ProximityNotifications_{deliveryId}";
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);

        _logger.LogInformation("Utilisateur {UserId} s'est abonné aux notifications de proximité pour la livraison {DeliveryId}", userId, deliveryId);
    }

    /// <summary>
    /// S'abonner aux alertes d'urgence (pour les administrateurs)
    /// </summary>
    [Authorize(Roles = "Admin,Manager")]
    public async Task SubscribeToEmergencyAlerts()
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, "EmergencyAlerts");
        _logger.LogInformation("Administrateur {UserId} s'est abonné aux alertes d'urgence", Context.UserIdentifier);
    }

    /// <summary>
    /// Configurer les préférences de notifications
    /// </summary>
    public async Task UpdateNotificationPreferences(object preferences)
    {
        var userId = Context.UserIdentifier;
        if (string.IsNullOrEmpty(userId))
            return;

        _logger.LogInformation("Préférences de notifications mises à jour pour {UserId}", userId);

        // Ici on sauvegarderait les préférences en base
        await Clients.Caller.SendAsync("NotificationPreferencesUpdated", preferences);
    }

    /// <summary>
    /// Tester l'envoi d'une notification
    /// </summary>
    [Authorize(Roles = "Admin")]
    public async Task SendTestNotification(string title, string message)
    {
        var userId = Context.UserIdentifier;

        var testNotification = new
        {
            Id = Guid.NewGuid().ToString(),
            Title = title,
            Message = message,
            Type = "test",
            Timestamp = DateTime.UtcNow,
            IsRead = false
        };

        await Clients.Caller.SendAsync("TestNotificationReceived", testNotification);
        _logger.LogInformation("Notification de test envoyée par l'administrateur {UserId}", userId);
    }
}

/// <summary>
/// Service pour l'envoi de notifications via SignalR
/// </summary>
public interface IDeliveryNotificationService
{
    /// <summary>
    /// Envoie une notification à un utilisateur spécifique
    /// </summary>
    Task SendNotificationToUserAsync(string userId, object notification);

    /// <summary>
    /// Envoie une notification à tous les abonnés d'une livraison
    /// </summary>
    Task SendDeliveryNotificationAsync(int deliveryId, object notification);

    /// <summary>
    /// Envoie une notification de proximité
    /// </summary>
    Task SendProximityNotificationAsync(int deliveryId, object notification);

    /// <summary>
    /// Envoie une alerte d'urgence aux administrateurs
    /// </summary>
    Task SendEmergencyAlertAsync(object alert);

    /// <summary>
    /// Envoie une notification à tous les livreurs
    /// </summary>
    Task SendNotificationToAllDeliveryPersonsAsync(object notification);

    /// <summary>
    /// Envoie une notification à tous les clients
    /// </summary>
    Task SendNotificationToAllCustomersAsync(object notification);
}

/// <summary>
/// Implémentation du service de notifications SignalR
/// </summary>
public class DeliveryNotificationService : IDeliveryNotificationService
{
    private readonly IHubContext<DeliveryNotificationHub> _hubContext;
    private readonly ILogger<DeliveryNotificationService> _logger;

    public DeliveryNotificationService(IHubContext<DeliveryNotificationHub> hubContext, ILogger<DeliveryNotificationService> logger)
    {
        _hubContext = hubContext;
        _logger = logger;
    }

    public async Task SendNotificationToUserAsync(string userId, object notification)
    {
        try
        {
            await _hubContext.Clients.Group($"User_{userId}").SendAsync("NotificationReceived", notification);
            _logger.LogInformation("Notification envoyée à l'utilisateur {UserId}", userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de notification à l'utilisateur {UserId}", userId);
        }
    }

    public async Task SendDeliveryNotificationAsync(int deliveryId, object notification)
    {
        try
        {
            await _hubContext.Clients.Group($"DeliveryNotifications_{deliveryId}").SendAsync("DeliveryNotificationReceived", notification);
            _logger.LogInformation("Notification de livraison envoyée pour la livraison {DeliveryId}", deliveryId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de notification pour la livraison {DeliveryId}", deliveryId);
        }
    }

    public async Task SendProximityNotificationAsync(int deliveryId, object notification)
    {
        try
        {
            await _hubContext.Clients.Group($"ProximityNotifications_{deliveryId}").SendAsync("ProximityNotificationReceived", notification);
            _logger.LogInformation("Notification de proximité envoyée pour la livraison {DeliveryId}", deliveryId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de notification de proximité pour la livraison {DeliveryId}", deliveryId);
        }
    }

    public async Task SendEmergencyAlertAsync(object alert)
    {
        try
        {
            await _hubContext.Clients.Group("EmergencyAlerts").SendAsync("EmergencyAlertReceived", alert);
            _logger.LogInformation("Alerte d'urgence envoyée aux administrateurs");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de l'alerte d'urgence");
        }
    }

    public async Task SendNotificationToAllDeliveryPersonsAsync(object notification)
    {
        try
        {
            await _hubContext.Clients.Group("DeliveryPersons_Notifications").SendAsync("BroadcastNotificationReceived", notification);
            _logger.LogInformation("Notification broadcast envoyée à tous les livreurs");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de notification broadcast aux livreurs");
        }
    }

    public async Task SendNotificationToAllCustomersAsync(object notification)
    {
        try
        {
            await _hubContext.Clients.Group("Customer_Notifications").SendAsync("BroadcastNotificationReceived", notification);
            _logger.LogInformation("Notification broadcast envoyée à tous les clients");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de notification broadcast aux clients");
        }
    }
}