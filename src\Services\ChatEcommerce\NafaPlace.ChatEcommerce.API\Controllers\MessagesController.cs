using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.ChatEcommerce.Application.DTOs;
using NafaPlace.ChatEcommerce.Application.Services;

namespace NafaPlace.ChatEcommerce.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class MessagesController : ControllerBase
{
    private readonly IChatEcommerceService _chatService;
    private readonly ILogger<MessagesController> _logger;

    public MessagesController(IChatEcommerceService chatService, ILogger<MessagesController> logger)
    {
        _chatService = chatService;
        _logger = logger;
    }

    /// <summary>
    /// Envoyer un message
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<int>> SendMessage([FromBody] SendMessageDto dto)
    {
        try
        {
            var messageId = await _chatService.SendMessageAsync(dto);
            return Ok(messageId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi du message");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtenir les messages d'une conversation
    /// </summary>
    [HttpGet("conversation/{conversationId}")]
    public async Task<ActionResult<List<MessageDto>>> GetConversationMessages(
        int conversationId, 
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 50)
    {
        try
        {
            var messages = await _chatService.GetConversationMessagesAsync(conversationId, page, pageSize);
            return Ok(messages);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des messages de la conversation {ConversationId}", conversationId);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Marquer un message comme lu
    /// </summary>
    [HttpPut("{messageId}/read")]
    public async Task<ActionResult<bool>> MarkMessageAsRead(int messageId, [FromBody] MarkMessageAsReadRequest request)
    {
        try
        {
            var result = await _chatService.MarkMessageAsReadAsync(messageId, request.UserId);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du marquage du message {MessageId} comme lu", messageId);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtenir le nombre de messages non lus pour un utilisateur
    /// </summary>
    [HttpGet("unread-count/{userId}")]
    public async Task<ActionResult<int>> GetUnreadMessageCount(string userId)
    {
        try
        {
            var count = await _chatService.GetUnreadMessageCountAsync(userId);
            return Ok(count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du comptage des messages non lus pour l'utilisateur {UserId}", userId);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Rechercher des messages
    /// </summary>
    [HttpGet("search")]
    public async Task<ActionResult<List<MessageDto>>> SearchMessages(
        [FromQuery] string query, 
        [FromQuery] int? conversationId = null)
    {
        try
        {
            var messages = await _chatService.SearchMessagesAsync(query, conversationId);
            return Ok(messages);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche des messages");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }
}

// DTOs pour les requêtes
public class MarkMessageAsReadRequest
{
    public string UserId { get; set; } = string.Empty;
}
