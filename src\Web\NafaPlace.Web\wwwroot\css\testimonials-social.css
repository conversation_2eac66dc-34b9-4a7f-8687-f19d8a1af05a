/* Testimonials and Social Proof Section */
.testimonials-social-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f1f3f4 100%);
    position: relative;
    overflow: hidden;
}

/* Testimonials Container */
.testimonials-container {
    margin-bottom: 2rem;
}

.section-header-testimonials {
    text-align: center;
    margin-bottom: 3rem;
}

.testimonials-badge {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    padding: 8px 20px;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1rem;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.testimonials-title {
    font-size: 2.2rem;
    font-weight: 800;
    color: #003366;
    margin-bottom: 0.5rem;
}

/* Testimonials Grid */
.testimonials-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    align-items: start;
}

.testimonial-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
}

.testimonial-card.featured {
    grid-row: span 2;
    background: linear-gradient(135deg, #003366 0%, #004488 100%);
    color: white;
    padding: 2.5rem;
}

.testimonial-card.featured .testimonial-text {
    color: rgba(255, 255, 255, 0.95);
    font-size: 1.1rem;
}

.testimonial-card.featured .author-name {
    color: white;
}

.testimonial-card.featured .author-location {
    color: rgba(255, 255, 255, 0.8);
}

/* Testimonial Content */
.testimonial-content {
    margin-bottom: 1.5rem;
}

.quote-icon {
    width: 50px;
    height: 50px;
    background: rgba(249, 99, 2, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.quote-icon i {
    font-size: 1.5rem;
    color: #F96302;
}

.testimonial-card.featured .quote-icon {
    background: rgba(255, 255, 255, 0.1);
}

.testimonial-card.featured .quote-icon i {
    color: white;
}

.testimonial-text {
    font-size: 1rem;
    line-height: 1.6;
    color: #495057;
    margin-bottom: 1rem;
    font-style: italic;
}

.testimonial-rating {
    display: flex;
    gap: 3px;
    margin-bottom: 1rem;
}

.testimonial-rating i {
    color: #FFD700;
    font-size: 1rem;
}

/* Testimonial Author */
.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
}

.author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #f8f9fa;
}

.testimonial-card.featured .author-avatar {
    border-color: rgba(255, 255, 255, 0.3);
}

.author-info {
    flex: 1;
}

.author-name {
    font-size: 1rem;
    font-weight: 700;
    color: #003366;
    margin-bottom: 0.25rem;
}

.author-location {
    font-size: 0.85rem;
    color: #6c757d;
    margin: 0;
}

.verified-badge {
    color: #4CAF50;
    font-size: 1.2rem;
}

.testimonial-card.featured .verified-badge {
    color: #FFD700;
}

/* Social Proof Container */
.social-proof-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Trust Stats */
.trust-stats {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.trust-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: #003366;
    margin-bottom: 1.5rem;
    text-align: center;
}

.stat-item-large {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.stat-item-large:last-child {
    border-bottom: none;
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #F96302, #E73C30);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.3rem;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 800;
    color: #003366;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* Recent Activity */
.recent-activity {
    background: white;
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.activity-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: #003366;
    margin-bottom: 1rem;
}

.activity-feed {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.activity-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.activity-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.activity-content {
    flex: 1;
}

.activity-content p {
    font-size: 0.85rem;
    color: #495057;
    margin-bottom: 0.25rem;
    line-height: 1.4;
}

.activity-time {
    font-size: 0.75rem;
    color: #6c757d;
}

/* Promo Card */
.promo-card {
    background: linear-gradient(135deg, #FF6B35, #F59E0B);
    color: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.promo-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: rotate 10s linear infinite;
}

.promo-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    position: relative;
    z-index: 2;
}

.promo-content {
    position: relative;
    z-index: 2;
}

.promo-content h4 {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.promo-content p {
    font-size: 0.9rem;
    margin-bottom: 1rem;
    opacity: 0.95;
}

.btn-promo {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 10px 25px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    cursor: pointer;
    backdrop-filter: blur(10px);
}

.btn-promo:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Animations */
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 992px) {
    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .testimonial-card.featured {
        grid-row: span 1;
    }
    
    .social-proof-container {
        margin-top: 2rem;
    }
}

@media (max-width: 768px) {
    .testimonials-title {
        font-size: 1.8rem;
    }
    
    .testimonial-card {
        padding: 1.5rem;
    }
    
    .testimonial-card.featured {
        padding: 2rem;
    }
    
    .trust-stats,
    .recent-activity,
    .promo-card {
        padding: 1.5rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .stat-icon {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }
}

@media (max-width: 576px) {
    .testimonials-social-section {
        padding: 3rem 0;
    }
    
    .testimonials-title {
        font-size: 1.6rem;
    }
    
    .testimonial-card,
    .testimonial-card.featured {
        padding: 1.25rem;
    }
    
    .testimonial-text {
        font-size: 0.95rem;
    }
    
    .author-avatar {
        width: 40px;
        height: 40px;
    }
    
    .activity-avatar {
        width: 30px;
        height: 30px;
    }
    
    .activity-content p {
        font-size: 0.8rem;
    }
    
    .promo-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }
}
