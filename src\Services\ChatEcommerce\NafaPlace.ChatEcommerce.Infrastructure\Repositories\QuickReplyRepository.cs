using Microsoft.EntityFrameworkCore;
using NafaPlace.ChatEcommerce.Application.Repositories;
using NafaPlace.ChatEcommerce.Domain.Entities;
using NafaPlace.ChatEcommerce.Infrastructure.Data;

namespace NafaPlace.ChatEcommerce.Infrastructure.Repositories;

public class QuickReplyRepository : IQuickReplyRepository
{
    private readonly ChatEcommerceDbContext _context;

    public QuickReplyRepository(ChatEcommerceDbContext context)
    {
        _context = context;
    }

    public async Task<List<QuickReply>> GetAllAsync()
    {
        return await _context.QuickReplies
            .OrderBy(q => q.SortOrder)
            .ThenBy(q => q.Title)
            .ToListAsync();
    }

    public async Task<List<QuickReply>> GetByCategoryAsync(string category)
    {
        return await _context.QuickReplies
            .Where(q => q.Category == category)
            .OrderBy(q => q.SortOrder)
            .ThenBy(q => q.Title)
            .ToListAsync();
    }

    public async Task<List<QuickReply>> GetActiveAsync()
    {
        return await _context.QuickReplies
            .Where(q => q.IsActive)
            .OrderBy(q => q.SortOrder)
            .ThenBy(q => q.Title)
            .ToListAsync();
    }

    public async Task<QuickReply?> GetByIdAsync(int id)
    {
        return await _context.QuickReplies.FindAsync(id);
    }

    public async Task<int> CreateAsync(QuickReply quickReply)
    {
        _context.QuickReplies.Add(quickReply);
        await _context.SaveChangesAsync();
        return quickReply.Id;
    }

    public async Task<bool> UpdateAsync(QuickReply quickReply)
    {
        quickReply.UpdatedAt = DateTime.UtcNow;
        _context.QuickReplies.Update(quickReply);
        return await _context.SaveChangesAsync() > 0;
    }

    public async Task<bool> DeleteAsync(int id)
    {
        var quickReply = await _context.QuickReplies.FindAsync(id);
        if (quickReply == null) return false;

        _context.QuickReplies.Remove(quickReply);
        return await _context.SaveChangesAsync() > 0;
    }
}

public class FAQRepository : IFAQRepository
{
    private readonly ChatEcommerceDbContext _context;

    public FAQRepository(ChatEcommerceDbContext context)
    {
        _context = context;
    }

    public async Task<List<FAQ>> GetAllAsync()
    {
        return await _context.FAQs
            .OrderBy(f => f.SortOrder)
            .ThenBy(f => f.Question)
            .ToListAsync();
    }

    public async Task<List<FAQ>> GetByCategoryAsync(string category)
    {
        return await _context.FAQs
            .Where(f => f.Category == category)
            .OrderBy(f => f.SortOrder)
            .ThenBy(f => f.Question)
            .ToListAsync();
    }

    public async Task<List<FAQ>> GetActiveAsync()
    {
        return await _context.FAQs
            .Where(f => f.IsActive)
            .OrderBy(f => f.SortOrder)
            .ThenBy(f => f.Question)
            .ToListAsync();
    }

    public async Task<List<FAQ>> SearchAsync(string query)
    {
        return await _context.FAQs
            .Where(f => f.IsActive && 
                       (f.Question.Contains(query) || f.Answer.Contains(query)))
            .OrderBy(f => f.SortOrder)
            .ToListAsync();
    }

    public async Task<FAQ?> GetByIdAsync(int id)
    {
        return await _context.FAQs.FindAsync(id);
    }

    public async Task<int> CreateAsync(FAQ faq)
    {
        _context.FAQs.Add(faq);
        await _context.SaveChangesAsync();
        return faq.Id;
    }

    public async Task<bool> UpdateAsync(FAQ faq)
    {
        faq.UpdatedAt = DateTime.UtcNow;
        _context.FAQs.Update(faq);
        return await _context.SaveChangesAsync() > 0;
    }

    public async Task<bool> DeleteAsync(int id)
    {
        var faq = await _context.FAQs.FindAsync(id);
        if (faq == null) return false;

        _context.FAQs.Remove(faq);
        return await _context.SaveChangesAsync() > 0;
    }

    public async Task<bool> RecordFeedbackAsync(int faqId, bool isHelpful)
    {
        var faq = await _context.FAQs.FindAsync(faqId);
        if (faq == null) return false;

        if (isHelpful)
            faq.HelpfulCount++;
        else
            faq.NotHelpfulCount++;

        faq.UpdatedAt = DateTime.UtcNow;
        return await _context.SaveChangesAsync() > 0;
    }
}

public class ChatSessionRepository : IChatSessionRepository
{
    private readonly ChatEcommerceDbContext _context;

    public ChatSessionRepository(ChatEcommerceDbContext context)
    {
        _context = context;
    }

    public async Task<ChatSession?> GetBySessionIdAsync(string sessionId)
    {
        return await _context.ChatSessions
            .FirstOrDefaultAsync(s => s.SessionId == sessionId);
    }

    public async Task<ChatSession?> GetByUserIdAsync(string userId)
    {
        return await _context.ChatSessions
            .FirstOrDefaultAsync(s => s.UserId == userId);
    }

    public async Task<List<ChatSession>> GetOnlineUsersAsync()
    {
        return await _context.ChatSessions
            .Where(s => s.IsOnline)
            .OrderByDescending(s => s.LastActivity)
            .ToListAsync();
    }

    public async Task<int> CreateAsync(ChatSession session)
    {
        _context.ChatSessions.Add(session);
        await _context.SaveChangesAsync();
        return session.Id;
    }

    public async Task<bool> UpdateAsync(ChatSession session)
    {
        session.LastActivity = DateTime.UtcNow;
        _context.ChatSessions.Update(session);
        return await _context.SaveChangesAsync() > 0;
    }

    public async Task<bool> UpdatePresenceAsync(string userId, bool isOnline)
    {
        var session = await GetByUserIdAsync(userId);
        if (session == null) return false;

        session.IsOnline = isOnline;
        session.LastActivity = DateTime.UtcNow;
        return await _context.SaveChangesAsync() > 0;
    }

    public async Task<bool> DeleteAsync(int id)
    {
        var session = await _context.ChatSessions.FindAsync(id);
        if (session == null) return false;

        _context.ChatSessions.Remove(session);
        return await _context.SaveChangesAsync() > 0;
    }

    public async Task<bool> CleanupInactiveSessionsAsync(TimeSpan inactiveThreshold)
    {
        var cutoffTime = DateTime.UtcNow - inactiveThreshold;
        var inactiveSessions = await _context.ChatSessions
            .Where(s => s.LastActivity < cutoffTime)
            .ToListAsync();

        if (inactiveSessions.Any())
        {
            _context.ChatSessions.RemoveRange(inactiveSessions);
            return await _context.SaveChangesAsync() > 0;
        }

        return true;
    }
}
