using Microsoft.Extensions.Logging;
using NafaPlace.Inventory.Application.DTOs;
using NafaPlace.Inventory.Application.Interfaces;
using NafaPlace.Inventory.Domain.Enums;
using NafaPlace.Inventory.Domain.Models;

namespace NafaPlace.Inventory.Application.Services;

public interface IStockReservationService
{
    Task<ReservationResult> ReserveStockForCartAsync(string userId, string? sessionId, List<CartItemReservation> items);
    Task<ReservationResult> ReserveStockAsync(int productId, int quantity, string userId, string? sessionId = null, int expirationMinutes = 30, string? reason = null);
    Task<bool> ReleaseReservationAsync(string reservationId, string reason);
    Task<bool> ReleaseUserReservationsAsync(string userId, string? sessionId = null);
    Task<bool> ExtendReservationAsync(string reservationId, int additionalMinutes);
    Task<bool> ConvertReservationToOrderAsync(string reservationId, string orderId);
    Task<List<StockReservationDto>> GetUserReservationsAsync(string userId, string? sessionId = null);
    Task<int> CleanupExpiredReservationsAsync();
    Task<bool> ValidateReservationAsync(string reservationId);
}

public class CartItemReservation
{
    public int ProductId { get; set; }
    public int Quantity { get; set; }
    public string ProductName { get; set; } = string.Empty;
}

public class ReservationResult
{
    public bool Success { get; set; }
    public string? ReservationId { get; set; }
    public string? ErrorMessage { get; set; }
    public List<string> FailedProducts { get; set; } = new();
    public DateTime? ExpiresAt { get; set; }
}

public class StockReservationService : IStockReservationService
{
    private readonly IInventoryRepository _inventoryRepository;
    private readonly IProductStockRepository _productStockRepository;
    private readonly ILogger<StockReservationService> _logger;

    public StockReservationService(
        IInventoryRepository inventoryRepository,
        IProductStockRepository productStockRepository,
        ILogger<StockReservationService> logger)
    {
        _inventoryRepository = inventoryRepository;
        _productStockRepository = productStockRepository;
        _logger = logger;
    }

    public async Task<ReservationResult> ReserveStockForCartAsync(string userId, string? sessionId, List<CartItemReservation> items)
    {
        var result = new ReservationResult();
        var reservationId = Guid.NewGuid().ToString();
        var expiresAt = DateTime.UtcNow.AddMinutes(30); // 30 minutes par défaut

        try
        {
            _logger.LogInformation("Starting cart stock reservation for user {UserId}, session {SessionId}", userId, sessionId);

            // Nettoyer d'abord les réservations expirées
            await CleanupExpiredReservationsAsync();

            // Valider que tous les stocks sont disponibles avant de réserver
            var validationResults = new List<(int ProductId, bool IsValid, int AvailableStock)>();
            
            foreach (var item in items)
            {
                var validation = await _productStockRepository.ValidateStockAvailabilityAsync(item.ProductId, item.Quantity);
                validationResults.Add((item.ProductId, validation.IsValid, validation.AvailableStock));
                
                if (!validation.IsValid)
                {
                    result.FailedProducts.Add($"{item.ProductName} (Demandé: {item.Quantity}, Disponible: {validation.AvailableStock})");
                }
            }

            // Si tous les produits ne sont pas disponibles, échouer
            if (result.FailedProducts.Any())
            {
                result.Success = false;
                result.ErrorMessage = $"Stock insuffisant pour {result.FailedProducts.Count} produit(s)";
                return result;
            }

            // Créer les réservations
            var reservationsCreated = new List<StockReservation>();
            
            foreach (var item in items)
            {
                var reservation = new StockReservation
                {
                    ProductId = item.ProductId,
                    UserId = userId,
                    SessionId = sessionId ?? Guid.NewGuid().ToString(),
                    Quantity = item.Quantity,
                    Status = ReservationStatus.Active,
                    ReservedAt = DateTime.UtcNow,
                    ExpiresAt = expiresAt,
                    Reason = "Réservation panier"
                };

                var created = await _inventoryRepository.CreateReservationAsync(reservation);
                if (created != null)
                {
                    reservationsCreated.Add(created);
                    _logger.LogInformation("Stock reserved for product {ProductId}: {Quantity} units", item.ProductId, item.Quantity);
                }
                else
                {
                    // Si une réservation échoue, annuler toutes les précédentes
                    foreach (var createdReservation in reservationsCreated)
                    {
                        await ReleaseReservationAsync(createdReservation.Id.ToString(), "Échec de réservation partielle");
                    }
                    
                    result.Success = false;
                    result.ErrorMessage = $"Échec de la réservation pour {item.ProductName}";
                    return result;
                }
            }

            result.Success = true;
            result.ReservationId = reservationId;
            result.ExpiresAt = expiresAt;
            
            _logger.LogInformation("Cart stock reservation completed successfully for user {UserId}", userId);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cart stock reservation for user {UserId}", userId);
            result.Success = false;
            result.ErrorMessage = "Erreur lors de la réservation du stock";
            return result;
        }
    }

    public async Task<ReservationResult> ReserveStockAsync(int productId, int quantity, string userId, string? sessionId = null, int expirationMinutes = 30, string? reason = null)
    {
        var result = new ReservationResult();

        try
        {
            // Valider la disponibilité du stock
            var validation = await _productStockRepository.ValidateStockAvailabilityAsync(productId, quantity);
            if (!validation.IsValid)
            {
                result.Success = false;
                result.ErrorMessage = $"Stock insuffisant. Disponible: {validation.AvailableStock}, Demandé: {quantity}";
                return result;
            }

            // Créer la réservation
            var reservation = new StockReservation
            {
                ProductId = productId,
                UserId = userId,
                SessionId = sessionId ?? Guid.NewGuid().ToString(),
                Quantity = quantity,
                Status = ReservationStatus.Active,
                ReservedAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddMinutes(expirationMinutes),
                Reason = reason ?? "Réservation de stock"
            };

            var created = await _inventoryRepository.CreateReservationAsync(reservation);
            if (created != null)
            {
                result.Success = true;
                result.ReservationId = created.Id.ToString();
                result.ExpiresAt = created.ExpiresAt;
                
                _logger.LogInformation("Stock reserved for product {ProductId}: {Quantity} units, expires at {ExpiresAt}", 
                    productId, quantity, created.ExpiresAt);
            }
            else
            {
                result.Success = false;
                result.ErrorMessage = "Échec de la création de la réservation";
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reserving stock for product {ProductId}", productId);
            result.Success = false;
            result.ErrorMessage = "Erreur lors de la réservation du stock";
            return result;
        }
    }

    public async Task<bool> ReleaseReservationAsync(string reservationId, string reason)
    {
        try
        {
            var success = await _inventoryRepository.ReleaseReservationAsync(int.Parse(reservationId), reason);
            if (success)
            {
                _logger.LogInformation("Stock reservation {ReservationId} released: {Reason}", reservationId, reason);
            }
            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error releasing reservation {ReservationId}", reservationId);
            return false;
        }
    }

    public async Task<bool> ReleaseUserReservationsAsync(string userId, string? sessionId = null)
    {
        try
        {
            var reservations = await GetUserReservationsAsync(userId, sessionId);
            var releasedCount = 0;

            foreach (var reservation in reservations)
            {
                if (await ReleaseReservationAsync(reservation.Id.ToString(), "Libération utilisateur"))
                {
                    releasedCount++;
                }
            }

            _logger.LogInformation("Released {Count} reservations for user {UserId}", releasedCount, userId);
            return releasedCount > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error releasing user reservations for {UserId}", userId);
            return false;
        }
    }

    public async Task<bool> ExtendReservationAsync(string reservationId, int additionalMinutes)
    {
        try
        {
            // Cette méthode nécessiterait une implémentation dans le repository
            // Pour l'instant, on retourne false
            _logger.LogWarning("ExtendReservationAsync not implemented yet");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extending reservation {ReservationId}", reservationId);
            return false;
        }
    }

    public async Task<bool> ConvertReservationToOrderAsync(string reservationId, string orderId)
    {
        try
        {
            // Cette méthode convertirait une réservation en commande confirmée
            // Pour l'instant, on libère simplement la réservation
            return await ReleaseReservationAsync(reservationId, $"Converti en commande {orderId}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting reservation {ReservationId} to order", reservationId);
            return false;
        }
    }

    public async Task<List<StockReservationDto>> GetUserReservationsAsync(string userId, string? sessionId = null)
    {
        try
        {
            // Cette méthode nécessiterait une implémentation dans le repository
            // Pour l'instant, on retourne une liste vide
            return new List<StockReservationDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user reservations for {UserId}", userId);
            return new List<StockReservationDto>();
        }
    }

    public async Task<int> CleanupExpiredReservationsAsync()
    {
        try
        {
            return await _inventoryRepository.CleanupExpiredReservationsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired reservations");
            return 0;
        }
    }

    public async Task<bool> ValidateReservationAsync(string reservationId)
    {
        try
        {
            // Cette méthode vérifierait si une réservation est toujours valide
            // Pour l'instant, on retourne true
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating reservation {ReservationId}", reservationId);
            return false;
        }
    }
}
