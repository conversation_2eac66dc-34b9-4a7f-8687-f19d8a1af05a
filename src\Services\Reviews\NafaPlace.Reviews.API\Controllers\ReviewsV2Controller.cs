using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Reviews.Application.Services;
using NafaPlace.Reviews.DTOs;
using System.Security.Claims;

namespace NafaPlace.Reviews.API.Controllers;

[ApiController]
[Route("api/v2/[controller]")]
public class ReviewsV2Controller : ControllerBase
{
    private readonly IReviewService _reviewService;
    private readonly IReviewMediaService _reviewMediaService;
    private readonly IReviewModerationService _reviewModerationService;
    private readonly ILogger<ReviewsV2Controller> _logger;

    public ReviewsV2Controller(
        IReviewService reviewService,
        IReviewMediaService reviewMediaService,
        IReviewModerationService reviewModerationService,
        ILogger<ReviewsV2Controller> logger)
    {
        _reviewService = reviewService;
        _reviewMediaService = reviewMediaService;
        _reviewModerationService = reviewModerationService;
        _logger = logger;
    }

    /// <summary>
    /// Create a new review with media support
    /// </summary>
    [HttpPost]
    [Authorize]
    public async Task<IActionResult> CreateReview([FromForm] CreateReviewWithMediaRequest request)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            // Validate request
            if (request.Rating < 1 || request.Rating > 5)
                return BadRequest("Rating must be between 1 and 5");

            if (string.IsNullOrEmpty(request.Comment?.Trim()))
                return BadRequest("Comment is required");

            // Check if user can review this product
            var canReview = await _reviewService.CanUserReviewProductAsync(userId, request.ProductId);
            if (!canReview)
                return BadRequest("You can only review products you have purchased");

            // Create review
            var reviewDto = new CreateReviewDto
            {
                ProductId = request.ProductId,
                UserId = userId,
                Rating = request.Rating,
                Comment = request.Comment.Trim(),
                Title = request.Title?.Trim()
            };

            var review = await _reviewService.CreateReviewAsync(reviewDto);

            // Process media files if any
            if (request.Images?.Count > 0 || request.Videos?.Count > 0)
            {
                var mediaResult = await _reviewMediaService.ProcessReviewMediaAsync(
                    review.Id,
                    request.Images,
                    request.Videos);

                if (!mediaResult.Success)
                {
                    _logger.LogWarning("Failed to process media for review {ReviewId}: {Error}",
                        review.Id, mediaResult.ErrorMessage);
                }
            }

            // Auto-moderate the review
            await _reviewModerationService.ModerateReviewAsync(review.Id);

            return Ok(new { ReviewId = review.Id, Message = "Review submitted successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating review for product {ProductId}", request.ProductId);
            return StatusCode(500, "An error occurred while creating the review");
        }
    }

    /// <summary>
    /// Get reviews for a product with enhanced filtering
    /// </summary>
    [HttpGet("product/{productId}")]
    public async Task<IActionResult> GetProductReviews(
        int productId,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] int? rating = null,
        [FromQuery] bool? hasMedia = null,
        [FromQuery] string sortBy = "newest",
        [FromQuery] bool? verified = null)
    {
        try
        {
            var filter = new ReviewFilterDto
            {
                ProductId = productId,
                Rating = rating,
                HasMedia = hasMedia,
                SortBy = sortBy,
                VerifiedOnly = verified,
                Page = page,
                PageSize = Math.Min(pageSize, 50) // Limit page size
            };

            var result = await _reviewService.GetProductReviewsAsync(filter);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting reviews for product {ProductId}", productId);
            return StatusCode(500, "An error occurred while retrieving reviews");
        }
    }

    /// <summary>
    /// Get review statistics for a product
    /// </summary>
    [HttpGet("product/{productId}/stats")]
    public async Task<IActionResult> GetProductReviewStats(int productId)
    {
        try
        {
            var stats = await _reviewService.GetProductReviewStatsAsync(productId);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting review stats for product {ProductId}", productId);
            return StatusCode(500, "An error occurred while retrieving review statistics");
        }
    }

    /// <summary>
    /// Mark review as helpful
    /// </summary>
    [HttpPost("{reviewId}/helpful")]
    [Authorize]
    public async Task<IActionResult> MarkReviewHelpful(int reviewId, [FromBody] bool isHelpful)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            await _reviewService.MarkReviewHelpfulAsync(reviewId, userId, isHelpful);
            return Ok(new { Message = "Review helpfulness updated" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking review {ReviewId} as helpful", reviewId);
            return StatusCode(500, "An error occurred while updating review helpfulness");
        }
    }

    /// <summary>
    /// Report a review
    /// </summary>
    [HttpPost("{reviewId}/report")]
    [Authorize]
    public async Task<IActionResult> ReportReview(int reviewId, [FromBody] ReportReviewDto reportDto)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            reportDto.ReviewId = reviewId;
            reportDto.ReportedByUserId = userId;

            await _reviewModerationService.ReportReviewAsync(reviewId, reportDto, userId);
            return Ok(new { Message = "Review reported successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reporting review {ReviewId}", reviewId);
            return StatusCode(500, "An error occurred while reporting the review");
        }
    }

    /// <summary>
    /// Get trending/featured reviews
    /// </summary>
    [HttpGet("trending")]
    public async Task<IActionResult> GetTrendingReviews([FromQuery] int count = 10)
    {
        try
        {
            var reviews = await _reviewService.GetTrendingReviewsAsync(count);
            return Ok(reviews);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting trending reviews");
            return StatusCode(500, "An error occurred while retrieving trending reviews");
        }
    }

    /// <summary>
    /// Get user's reviews
    /// </summary>
    [HttpGet("user/my-reviews")]
    [Authorize]
    public async Task<IActionResult> GetUserReviews([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            var reviews = await _reviewService.GetUserReviewsAsync(userId, page, pageSize);
            return Ok(reviews);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user reviews");
            return StatusCode(500, "An error occurred while retrieving user reviews");
        }
    }

    /// <summary>
    /// Update user's review
    /// </summary>
    [HttpPut("{reviewId}")]
    [Authorize]
    public async Task<IActionResult> UpdateReview(int reviewId, [FromForm] UpdateReviewRequest request)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            // Verify ownership
            var review = await _reviewService.GetReviewByIdAsync(reviewId);
            if (review == null || review.UserId != userId)
                return NotFound();

            // Update review
            var updateDto = new UpdateReviewDto
            {
                Id = reviewId,
                Rating = request.Rating,
                Comment = request.Comment?.Trim(),
                Title = request.Title?.Trim()
            };

            await _reviewService.UpdateReviewAsync(updateDto);

            // Process new media if any
            if (request.NewImages?.Count > 0 || request.NewVideos?.Count > 0)
            {
                await _reviewMediaService.ProcessReviewMediaAsync(
                    reviewId,
                    request.NewImages,
                    request.NewVideos);
            }

            // Remove media if requested
            if (request.RemoveMediaIds?.Count > 0)
            {
                await _reviewMediaService.RemoveReviewMediaAsync(reviewId, request.RemoveMediaIds);
            }

            // Re-moderate the updated review
            await _reviewModerationService.ModerateReviewAsync(reviewId);

            return Ok(new { Message = "Review updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating review {ReviewId}", reviewId);
            return StatusCode(500, "An error occurred while updating the review");
        }
    }

    /// <summary>
    /// Delete user's review
    /// </summary>
    [HttpDelete("{reviewId}")]
    [Authorize]
    public async Task<IActionResult> DeleteReview(int reviewId)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            // Verify ownership
            var review = await _reviewService.GetReviewByIdAsync(reviewId);
            if (review == null || review.UserId != userId)
                return NotFound();

            await _reviewService.DeleteReviewAsync(reviewId, userId);
            return Ok(new { Message = "Review deleted successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting review {ReviewId}", reviewId);
            return StatusCode(500, "An error occurred while deleting the review");
        }
    }

    /// <summary>
    /// Get review insights for a product (admin only)
    /// </summary>
    [HttpGet("product/{productId}/insights")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> GetProductReviewInsights(int productId)
    {
        try
        {
            var insights = await _reviewService.GetProductReviewInsightsAsync(productId);
            return Ok(insights);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting review insights for product {ProductId}", productId);
            return StatusCode(500, "An error occurred while retrieving review insights");
        }
    }

    /// <summary>
    /// Bulk moderate reviews (admin only)
    /// </summary>
    [HttpPost("moderate/bulk")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> BulkModerateReviews([FromBody] BulkModerationRequest request)
    {
        try
        {
            await _reviewModerationService.BulkModerateReviewsAsync(request.ReviewIds, request.Action, request.Reason);
            return Ok(new { Message = $"Bulk moderation completed for {request.ReviewIds.Count} reviews" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in bulk moderation");
            return StatusCode(500, "An error occurred during bulk moderation");
        }
    }
}

// Request DTOs
public class CreateReviewWithMediaRequest
{
    public int ProductId { get; set; }
    public int Rating { get; set; }
    public string Comment { get; set; } = "";
    public string? Title { get; set; }
    public List<IFormFile>? Images { get; set; }
    public List<IFormFile>? Videos { get; set; }
}

public class UpdateReviewRequest
{
    public int Rating { get; set; }
    public string Comment { get; set; } = "";
    public string? Title { get; set; }
    public List<IFormFile>? NewImages { get; set; }
    public List<IFormFile>? NewVideos { get; set; }
    public List<int>? RemoveMediaIds { get; set; }
}

public class BulkModerationRequest
{
    public List<int> ReviewIds { get; set; } = new();
    public string Action { get; set; } = "";
    public string? Reason { get; set; }
}