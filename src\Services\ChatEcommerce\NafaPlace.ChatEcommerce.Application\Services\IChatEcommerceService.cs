using NafaPlace.ChatEcommerce.Application.DTOs;

namespace NafaPlace.ChatEcommerce.Application.Services;

public interface IChatEcommerceService
{
    // Gestion des conversations
    Task<int> CreateConversationAsync(CreateConversationDto conversation);
    Task<ConversationDto?> GetConversationAsync(int conversationId);
    Task<List<ConversationDto>> GetConversationsAsync(ConversationFilterDto filter);
    Task<List<ConversationDto>> GetUserConversationsAsync(string userId);
    Task<List<ConversationDto>> GetSellerConversationsAsync(string sellerId);
    Task<bool> UpdateConversationStatusAsync(int conversationId, string status, string? reason = null);
    Task<bool> CloseConversationAsync(int conversationId, string closedBy, string? reason = null);
    Task<bool> ReopenConversationAsync(int conversationId);
    Task<bool> AssignConversationToSellerAsync(int conversationId, string sellerId);
    
    // Gestion des messages
    Task<int> SendMessageAsync(SendMessageDto message);
    Task<List<MessageDto>> GetConversationMessagesAsync(int conversationId, int page = 1, int pageSize = 50);
    Task<bool> MarkMessageAsReadAsync(int messageId, string userId);
    Task<bool> MarkConversationAsReadAsync(int conversationId, string userId);
    Task<int> GetUnreadMessageCountAsync(string userId);
    
    // Support produit
    Task<int> CreateProductInquiryAsync(string customerId, string customerName, int productId, string message);
    Task<int> CreateOrderSupportAsync(string customerId, string customerName, int orderId, string message, string issueType);
    Task<List<ConversationDto>> GetProductConversationsAsync(int productId);
    Task<List<ConversationDto>> GetOrderConversationsAsync(int orderId);
    
    // FAQ et réponses rapides
    Task<List<FAQDto>> GetFAQsAsync(string? category = null);
    Task<List<FAQDto>> SearchFAQsAsync(string query);
    Task<List<QuickReplyDto>> GetQuickRepliesAsync(string? category = null);
    Task<int> CreateQuickReplyAsync(Domain.Entities.QuickReply quickReply);
    Task<bool> UpdateQuickReplyAsync(Domain.Entities.QuickReply quickReply);
    Task<bool> DeleteQuickReplyAsync(int id);
    Task<bool> RecordFAQFeedbackAsync(int faqId, bool isHelpful);
    
    // Sessions et présence
    Task<bool> UpdateUserPresenceAsync(string userId, bool isOnline);
    Task<List<ChatSessionDto>> GetOnlineUsersAsync();
    Task<bool> IsUserOnlineAsync(string userId);
    
    // Statistiques
    Task<ConversationStatsDto> GetConversationStatsAsync(string? userId = null, DateTime? startDate = null, DateTime? endDate = null);
    Task<Dictionary<string, int>> GetConversationsByTypeAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<Dictionary<string, int>> GetConversationsByStatusAsync(DateTime? startDate = null, DateTime? endDate = null);
    
    // Recherche et filtrage
    Task<List<ConversationDto>> SearchConversationsAsync(string query, ConversationFilterDto? filter = null);
    Task<List<MessageDto>> SearchMessagesAsync(string query, int? conversationId = null);
    
    // Notifications temps réel (SignalR)
    Task NotifyNewMessageAsync(int conversationId, MessageDto message);
    Task NotifyTypingAsync(int conversationId, TypingIndicatorDto typing);
    Task NotifyUserStatusAsync(string userId, bool isOnline);
    Task NotifyConversationStatusAsync(int conversationId, string status);
}
