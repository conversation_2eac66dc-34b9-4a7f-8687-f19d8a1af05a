using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Analytics.Application.Services;
using NafaPlace.Analytics.Domain.DTOs;
using System.Security.Claims;

namespace NafaPlace.Analytics.API.Controllers;

/// <summary>
/// Contrôleur pour l'analytics avancé et les prédictions IA
/// Fournit des insights métier, prédictions de ventes et analyses comportementales
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
[Authorize]
public class AdvancedAnalyticsController : ControllerBase
{
    private readonly IAdvancedAnalyticsService _analyticsService;
    private readonly IPredictiveAnalyticsService _predictiveService;
    private readonly ICustomerInsightsService _customerInsightsService;
    private readonly IMarketAnalysisService _marketAnalysisService;
    private readonly ILogger<AdvancedAnalyticsController> _logger;

    public AdvancedAnalyticsController(
        IAdvancedAnalyticsService analyticsService,
        IPredictiveAnalyticsService predictiveService,
        ICustomerInsightsService customerInsightsService,
        IMarketAnalysisService marketAnalysisService,
        ILogger<AdvancedAnalyticsController> logger)
    {
        _analyticsService = analyticsService;
        _predictiveService = predictiveService;
        _customerInsightsService = customerInsightsService;
        _marketAnalysisService = marketAnalysisService;
        _logger = logger;
    }

    /// <summary>
    /// Tableau de bord analytics complet avec KPIs et métriques clés
    /// </summary>
    [HttpGet("dashboard")]
    public async Task<IActionResult> GetAnalyticsDashboard([FromQuery] DateTime? fromDate, [FromQuery] DateTime? toDate)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddMonths(-1);
            var to = toDate ?? DateTime.UtcNow;

            var dashboard = await _analyticsService.GetComprehensiveDashboardAsync(from, to);
            return Ok(dashboard);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération du tableau de bord analytics");
            return StatusCode(500, new { Error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Prédictions de ventes basées sur l'IA
    /// </summary>
    [HttpGet("predictions/sales")]
    public async Task<IActionResult> GetSalesPredictions([FromQuery] int daysAhead = 30)
    {
        try
        {
            var predictions = await _predictiveService.PredictSalesAsync(daysAhead);
            return Ok(predictions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la prédiction des ventes");
            return StatusCode(500, new { Error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Prédiction de la demande pour des produits spécifiques
    /// </summary>
    [HttpGet("predictions/demand")]
    public async Task<IActionResult> GetDemandPredictions([FromQuery] List<int> productIds, [FromQuery] int daysAhead = 14)
    {
        try
        {
            var predictions = await _predictiveService.PredictDemandAsync(productIds, daysAhead);
            return Ok(predictions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la prédiction de demande");
            return StatusCode(500, new { Error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Prédiction du taux de désabonnement (churn) des clients
    /// </summary>
    [HttpGet("predictions/churn")]
    public async Task<IActionResult> GetChurnPredictions()
    {
        try
        {
            var churnAnalysis = await _predictiveService.PredictCustomerChurnAsync();
            return Ok(churnAnalysis);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la prédiction de churn");
            return StatusCode(500, new { Error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Analyse du comportement client avancée
    /// </summary>
    [HttpGet("customer-insights")]
    public async Task<IActionResult> GetCustomerInsights([FromQuery] string? segment = null)
    {
        try
        {
            var insights = await _customerInsightsService.GetAdvancedCustomerInsightsAsync(segment);
            return Ok(insights);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'analyse des insights clients");
            return StatusCode(500, new { Error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Segmentation automatique des clients par IA
    /// </summary>
    [HttpGet("customer-segmentation")]
    public async Task<IActionResult> GetCustomerSegmentation()
    {
        try
        {
            var segmentation = await _customerInsightsService.GetAICustomerSegmentationAsync();
            return Ok(segmentation);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la segmentation client");
            return StatusCode(500, new { Error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Recommandations personnalisées pour un client
    /// </summary>
    [HttpGet("recommendations/{customerId}")]
    public async Task<IActionResult> GetPersonalizedRecommendations(string customerId, [FromQuery] int count = 10)
    {
        try
        {
            var recommendations = await _customerInsightsService.GetPersonalizedRecommendationsAsync(customerId, count);
            return Ok(recommendations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la génération de recommandations pour {CustomerId}", customerId);
            return StatusCode(500, new { Error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Analyse de la concurrence et positionnement marché
    /// </summary>
    [HttpGet("market-analysis")]
    public async Task<IActionResult> GetMarketAnalysis([FromQuery] string? category = null)
    {
        try
        {
            var analysis = await _marketAnalysisService.GetMarketPositionAnalysisAsync(category);
            return Ok(analysis);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'analyse de marché");
            return StatusCode(500, new { Error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Optimisation des prix basée sur l'IA
    /// </summary>
    [HttpGet("pricing-optimization")]
    public async Task<IActionResult> GetPricingOptimization([FromQuery] List<int> productIds)
    {
        try
        {
            var optimization = await _marketAnalysisService.GetAIPricingOptimizationAsync(productIds);
            return Ok(optimization);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'optimisation des prix");
            return StatusCode(500, new { Error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Analyse des tendances de vente en temps réel
    /// </summary>
    [HttpGet("trends/real-time")]
    public async Task<IActionResult> GetRealTimeTrends()
    {
        try
        {
            var trends = await _analyticsService.GetRealTimeTrendsAsync();
            return Ok(trends);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des tendances temps réel");
            return StatusCode(500, new { Error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Analyse de cohort pour comprendre la rétention client
    /// </summary>
    [HttpGet("cohort-analysis")]
    public async Task<IActionResult> GetCohortAnalysis([FromQuery] DateTime startDate, [FromQuery] int periodMonths = 12)
    {
        try
        {
            var analysis = await _analyticsService.GetCohortAnalysisAsync(startDate, periodMonths);
            return Ok(analysis);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'analyse de cohort");
            return StatusCode(500, new { Error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Analyse RFM (Récence, Fréquence, Montant) des clients
    /// </summary>
    [HttpGet("rfm-analysis")]
    public async Task<IActionResult> GetRFMAnalysis()
    {
        try
        {
            var analysis = await _customerInsightsService.GetRFMAnalysisAsync();
            return Ok(analysis);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'analyse RFM");
            return StatusCode(500, new { Error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Détection d'anomalies dans les ventes
    /// </summary>
    [HttpGet("anomaly-detection")]
    public async Task<IActionResult> GetAnomalyDetection([FromQuery] DateTime? fromDate, [FromQuery] DateTime? toDate)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var to = toDate ?? DateTime.UtcNow;

            var anomalies = await _analyticsService.DetectAnomaliesAsync(from, to);
            return Ok(anomalies);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la détection d'anomalies");
            return StatusCode(500, new { Error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Attribution marketing - ROI des canaux d'acquisition
    /// </summary>
    [HttpGet("marketing-attribution")]
    public async Task<IActionResult> GetMarketingAttribution([FromQuery] DateTime? fromDate, [FromQuery] DateTime? toDate)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddMonths(-3);
            var to = toDate ?? DateTime.UtcNow;

            var attribution = await _analyticsService.GetMarketingAttributionAsync(from, to);
            return Ok(attribution);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'analyse d'attribution marketing");
            return StatusCode(500, new { Error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Optimisation de l'inventaire basée sur l'IA
    /// </summary>
    [HttpGet("inventory-optimization")]
    public async Task<IActionResult> GetInventoryOptimization([FromQuery] int warehouseId)
    {
        try
        {
            var optimization = await _predictiveService.OptimizeInventoryAsync(warehouseId);
            return Ok(optimization);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'optimisation d'inventaire pour l'entrepôt {WarehouseId}", warehouseId);
            return StatusCode(500, new { Error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Analyse de la performance des vendeurs
    /// </summary>
    [HttpGet("seller-performance")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<IActionResult> GetSellerPerformanceAnalysis([FromQuery] DateTime? fromDate, [FromQuery] DateTime? toDate)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddMonths(-1);
            var to = toDate ?? DateTime.UtcNow;

            var performance = await _analyticsService.GetSellerPerformanceAnalysisAsync(from, to);
            return Ok(performance);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'analyse de performance des vendeurs");
            return StatusCode(500, new { Error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Rapport d'analytics personnalisé
    /// </summary>
    [HttpPost("custom-report")]
    public async Task<IActionResult> GenerateCustomReport([FromBody] CustomReportRequestDto request)
    {
        try
        {
            var report = await _analyticsService.GenerateCustomReportAsync(request);
            return Ok(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la génération du rapport personnalisé");
            return StatusCode(500, new { Error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Entraîner les modèles d'IA avec de nouvelles données
    /// </summary>
    [HttpPost("train-models")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> TrainMLModels([FromBody] TrainModelsRequestDto request)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            var result = await _predictiveService.TrainModelsAsync(request, userId);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'entraînement des modèles ML");
            return StatusCode(500, new { Error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Obtenir les métriques de performance des modèles d'IA
    /// </summary>
    [HttpGet("model-performance")]
    [Authorize(Roles = "Admin,DataScientist")]
    public async Task<IActionResult> GetModelPerformanceMetrics()
    {
        try
        {
            var metrics = await _predictiveService.GetModelPerformanceMetricsAsync();
            return Ok(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des métriques de modèles");
            return StatusCode(500, new { Error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Export des données d'analytics en différents formats
    /// </summary>
    [HttpGet("export")]
    public async Task<IActionResult> ExportAnalyticsData([FromQuery] ExportRequestDto request)
    {
        try
        {
            var exportResult = await _analyticsService.ExportDataAsync(request);

            return request.Format.ToLower() switch
            {
                "csv" => File(exportResult.Data, "text/csv", exportResult.FileName),
                "excel" => File(exportResult.Data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", exportResult.FileName),
                "pdf" => File(exportResult.Data, "application/pdf", exportResult.FileName),
                _ => File(exportResult.Data, "application/json", exportResult.FileName)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'export des données analytics");
            return StatusCode(500, new { Error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Obtenir des insights automatiques générés par l'IA
    /// </summary>
    [HttpGet("ai-insights")]
    public async Task<IActionResult> GetAIGeneratedInsights()
    {
        try
        {
            var insights = await _analyticsService.GenerateAIInsightsAsync();
            return Ok(insights);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la génération d'insights IA");
            return StatusCode(500, new { Error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Configuration des alertes automatiques
    /// </summary>
    [HttpPost("alerts/configure")]
    public async Task<IActionResult> ConfigureAlerts([FromBody] AlertConfigurationDto configuration)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            configuration.UserId = userId;
            var result = await _analyticsService.ConfigureAlertsAsync(configuration);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la configuration des alertes");
            return StatusCode(500, new { Error = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Obtenir les alertes actives pour l'utilisateur
    /// </summary>
    [HttpGet("alerts/active")]
    public async Task<IActionResult> GetActiveAlerts()
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            var alerts = await _analyticsService.GetActiveAlertsAsync(userId);
            return Ok(alerts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des alertes actives");
            return StatusCode(500, new { Error = "Erreur interne du serveur" });
        }
    }
}