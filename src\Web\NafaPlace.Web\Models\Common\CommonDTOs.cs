using System.ComponentModel.DataAnnotations;

namespace NafaPlace.Web.Models.Common
{


    public class PaymentDto
    {
        public int Id { get; set; }
        public string PaymentId { get; set; } = string.Empty;
        public int OrderId { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "GNF";
        public string PaymentMethod { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? ProcessedAt { get; set; }
        public string TransactionId { get; set; } = string.Empty;
        public string GatewayResponse { get; set; } = string.Empty;
        public string FailureReason { get; set; } = string.Empty;
    }



    public class SearchResultDto<T>
    {
        public List<T> Items { get; set; } = new();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }
        public string Query { get; set; } = string.Empty;
        public Dictionary<string, object> Filters { get; set; } = new();
        public TimeSpan SearchTime { get; set; }
    }

    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public T? Data { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<string> Errors { get; set; } = new();
        public int StatusCode { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    public class PagedResult<T>
    {
        public List<T> Items { get; set; } = new();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasNextPage => Page < TotalPages;
        public bool HasPreviousPage => Page > 1;
    }

    public class FilterDto
    {
        public string Name { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public string Operator { get; set; } = "equals";
        public string Type { get; set; } = "string";
    }

    public class SortDto
    {
        public string Field { get; set; } = string.Empty;
        public string Direction { get; set; } = "asc";
    }
}
