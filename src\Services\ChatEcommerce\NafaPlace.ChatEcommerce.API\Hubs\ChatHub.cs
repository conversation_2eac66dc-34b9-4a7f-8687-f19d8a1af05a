using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using System.Security.Claims;

namespace NafaPlace.ChatEcommerce.API.Hubs;

/// <summary>
/// Hub SignalR pour la messagerie en temps réel entre clients et vendeurs
/// </summary>
[Authorize]
public class ChatHub : Hub
{
    private readonly ILogger<ChatHub> _logger;
    private static readonly Dictionary<string, HashSet<string>> _userConnections = new();
    private static readonly Dictionary<string, string> _connectionUsers = new();
    // Mapping SellerId -> UserId pour tracker les vendeurs en ligne
    private static readonly Dictionary<string, string> _sellerToUserMapping = new();

    public ChatHub(ILogger<ChatHub> logger)
    {
        _logger = logger;
    }

    public override async Task OnConnectedAsync()
    {
        var userId = GetUserId();
        var sellerId = GetSellerId();
        var connectionId = Context.ConnectionId;

        if (!string.IsNullOrEmpty(userId))
        {
            lock (_userConnections)
            {
                if (!_userConnections.ContainsKey(userId))
                {
                    _userConnections[userId] = new HashSet<string>();
                }
                _userConnections[userId].Add(connectionId);
                _connectionUsers[connectionId] = userId;

                // Si c'est un vendeur, stocker le mapping SellerId -> UserId
                if (!string.IsNullOrEmpty(sellerId))
                {
                    _sellerToUserMapping[sellerId] = userId;
                    _logger.LogInformation("Vendeur connecté - SellerId: {SellerId}, UserId: {UserId}", sellerId, userId);
                }
            }

            _logger.LogInformation("Utilisateur {UserId} connecté avec ConnectionId {ConnectionId}", userId, connectionId);

            // Notifier les autres utilisateurs que cet utilisateur est en ligne
            await Clients.Others.SendAsync("UserOnline", userId);

            // Si c'est un vendeur, notifier aussi avec le SellerId
            if (!string.IsNullOrEmpty(sellerId))
            {
                await Clients.Others.SendAsync("SellerOnline", sellerId);
            }
        }

        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var connectionId = Context.ConnectionId;
        string? userId = null;
        string? sellerId = null;
        bool shouldNotifyOffline = false;

        lock (_userConnections)
        {
            if (_connectionUsers.TryGetValue(connectionId, out userId))
            {
                _connectionUsers.Remove(connectionId);

                if (_userConnections.ContainsKey(userId))
                {
                    _userConnections[userId].Remove(connectionId);

                    // Si l'utilisateur n'a plus de connexions actives
                    if (_userConnections[userId].Count == 0)
                    {
                        _userConnections.Remove(userId);
                        shouldNotifyOffline = true;

                        // Nettoyer le mapping SellerId si c'est un vendeur
                        var sellerEntry = _sellerToUserMapping.FirstOrDefault(x => x.Value == userId);
                        if (!string.IsNullOrEmpty(sellerEntry.Key))
                        {
                            sellerId = sellerEntry.Key;
                            _sellerToUserMapping.Remove(sellerEntry.Key);
                            _logger.LogInformation("Vendeur déconnecté - SellerId: {SellerId}", sellerId);
                        }
                    }
                }
            }
        }

        // Notifier en dehors du lock
        if (shouldNotifyOffline && !string.IsNullOrEmpty(userId))
        {
            await Clients.Others.SendAsync("UserOffline", userId);

            // Si c'est un vendeur, notifier aussi avec le SellerId
            if (!string.IsNullOrEmpty(sellerId))
            {
                await Clients.Others.SendAsync("SellerOffline", sellerId);
            }
        }

        _logger.LogInformation("Utilisateur {UserId} déconnecté (ConnectionId: {ConnectionId})", userId ?? "Unknown", connectionId);

        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// Rejoindre une conversation spécifique
    /// </summary>
    public async Task JoinConversation(int conversationId)
    {
        var userId = GetUserId();
        var groupName = $"Conversation_{conversationId}";

        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
        _logger.LogInformation("Utilisateur {UserId} a rejoint la conversation {ConversationId}", userId, conversationId);

        // Notifier les autres membres de la conversation
        await Clients.OthersInGroup(groupName).SendAsync("UserJoinedConversation", userId, conversationId);
    }

    /// <summary>
    /// Quitter une conversation spécifique
    /// </summary>
    public async Task LeaveConversation(int conversationId)
    {
        var userId = GetUserId();
        var groupName = $"Conversation_{conversationId}";

        await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
        _logger.LogInformation("Utilisateur {UserId} a quitté la conversation {ConversationId}", userId, conversationId);

        // Notifier les autres membres de la conversation
        await Clients.OthersInGroup(groupName).SendAsync("UserLeftConversation", userId, conversationId);
    }

    /// <summary>
    /// Envoyer un message dans une conversation
    /// </summary>
    public async Task SendMessage(int conversationId, string message, string senderName)
    {
        var userId = GetUserId();
        var groupName = $"Conversation_{conversationId}";

        var messageData = new
        {
            ConversationId = conversationId,
            SenderId = userId,
            SenderName = senderName,
            Message = message,
            Timestamp = DateTime.UtcNow
        };

        // Envoyer le message à tous les membres de la conversation
        await Clients.Group(groupName).SendAsync("ReceiveMessage", messageData);

        _logger.LogInformation("Message envoyé par {UserId} dans la conversation {ConversationId}", userId, conversationId);
    }

    /// <summary>
    /// Indiquer qu'un utilisateur est en train de taper
    /// </summary>
    public async Task SendTypingIndicator(int conversationId, bool isTyping)
    {
        var userId = GetUserId();
        var groupName = $"Conversation_{conversationId}";

        // Envoyer l'indicateur de frappe aux autres membres de la conversation
        await Clients.OthersInGroup(groupName).SendAsync("TypingIndicator", new
        {
            ConversationId = conversationId,
            UserId = userId,
            IsTyping = isTyping
        });
    }

    /// <summary>
    /// Marquer les messages comme lus
    /// </summary>
    public async Task MarkMessagesAsRead(int conversationId, int lastReadMessageId)
    {
        var userId = GetUserId();
        var groupName = $"Conversation_{conversationId}";

        // Notifier les autres membres que les messages ont été lus
        await Clients.OthersInGroup(groupName).SendAsync("MessagesRead", new
        {
            ConversationId = conversationId,
            UserId = userId,
            LastReadMessageId = lastReadMessageId,
            Timestamp = DateTime.UtcNow
        });

        _logger.LogInformation("Utilisateur {UserId} a marqué les messages comme lus jusqu'à {MessageId} dans la conversation {ConversationId}", 
            userId, lastReadMessageId, conversationId);
    }

    /// <summary>
    /// Notifier qu'une nouvelle conversation a été créée
    /// </summary>
    public async Task NotifyNewConversation(string sellerId, object conversationData)
    {
        // Envoyer la notification au vendeur spécifique
        if (_userConnections.TryGetValue(sellerId, out var connections))
        {
            foreach (var connectionId in connections)
            {
                await Clients.Client(connectionId).SendAsync("NewConversation", conversationData);
            }
        }

        _logger.LogInformation("Nouvelle conversation notifiée au vendeur {SellerId}", sellerId);
    }

    /// <summary>
    /// Obtenir le nombre d'utilisateurs en ligne
    /// </summary>
    public async Task<int> GetOnlineUsersCount()
    {
        return await Task.FromResult(_userConnections.Count);
    }

    /// <summary>
    /// Vérifier si un utilisateur est en ligne
    /// </summary>
    public async Task<bool> IsUserOnline(string userId)
    {
        var isOnline = _userConnections.ContainsKey(userId);
        _logger.LogInformation("🔍 Vérification statut pour userId: {UserId} - Résultat: {IsOnline}", userId, isOnline);
        _logger.LogInformation("📋 Utilisateurs connectés actuellement: {ConnectedUsers}",
            string.Join(", ", _userConnections.Keys));
        return await Task.FromResult(isOnline);
    }

    /// <summary>
    /// Vérifier si un vendeur est en ligne par son SellerId
    /// </summary>
    public async Task<bool> IsSellerOnline(string sellerId)
    {
        // Utiliser le mapping SellerId -> UserId
        if (_sellerToUserMapping.TryGetValue(sellerId, out var userId))
        {
            // Vérifier si le userId est toujours connecté
            var isOnline = _userConnections.ContainsKey(userId);
            _logger.LogInformation("✅ Vendeur {SellerId} (userId: {UserId}) est {Status}",
                sellerId, userId, isOnline ? "EN LIGNE" : "HORS LIGNE");
            return await Task.FromResult(isOnline);
        }

        _logger.LogInformation("❌ Vendeur {SellerId} est hors ligne (pas dans le mapping)", sellerId);
        _logger.LogInformation("📋 Vendeurs connectés: {Sellers}",
            string.Join(", ", _sellerToUserMapping.Keys));
        return await Task.FromResult(false);
    }

    /// <summary>
    /// Obtenir l'ID de l'utilisateur connecté à partir des claims
    /// </summary>
    private string GetUserId()
    {
        var userIdClaim = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value
                       ?? Context.User?.FindFirst("sub")?.Value
                       ?? Context.User?.FindFirst("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier")?.Value;

        return userIdClaim ?? string.Empty;
    }

    /// <summary>
    /// Obtenir le SellerId de l'utilisateur connecté à partir des claims
    /// </summary>
    private string GetSellerId()
    {
        var sellerIdClaim = Context.User?.FindFirst("SellerId")?.Value
                         ?? Context.User?.FindFirst("sellerId")?.Value;

        return sellerIdClaim ?? string.Empty;
    }

    /// <summary>
    /// Obtenir le nom de l'utilisateur connecté à partir des claims
    /// </summary>
    private string GetUserName()
    {
        var userNameClaim = Context.User?.FindFirst(ClaimTypes.Name)?.Value
                         ?? Context.User?.FindFirst("name")?.Value
                         ?? Context.User?.FindFirst("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name")?.Value;

        return userNameClaim ?? "Unknown";
    }
}

