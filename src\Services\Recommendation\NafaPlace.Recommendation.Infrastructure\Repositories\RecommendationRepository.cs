using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NafaPlace.Recommendation.Application.DTOs;
using NafaPlace.Recommendation.Application.Interfaces;
using NafaPlace.Recommendation.Domain.Entities;
using NafaPlace.Recommendation.Infrastructure.Data;

namespace NafaPlace.Recommendation.Infrastructure.Repositories;

public class RecommendationRepository : IRecommendationRepository
{
    private readonly RecommendationDbContext _context;
    private readonly ILogger<RecommendationRepository> _logger;

    public RecommendationRepository(RecommendationDbContext context, ILogger<RecommendationRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<bool> RecordInteractionAsync(UserInteractionDto interaction)
    {
        try
        {
            var entity = new UserInteraction
            {
                UserId = interaction.UserId,
                ProductId = interaction.ProductId,
                Type = (Domain.Entities.InteractionType)(int)interaction.InteractionType,
                Weight = interaction.Value ?? 1.0,
                SessionId = interaction.SessionId,
                Context = System.Text.Json.JsonSerializer.Serialize(interaction.Context ?? new Dictionary<string, object>()),
                Timestamp = interaction.Timestamp
            };

            _context.UserInteractions.Add(entity);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Interaction enregistrée: {UserId} -> {ProductId} ({InteractionType})", 
                interaction.UserId, interaction.ProductId, interaction.InteractionType);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'enregistrement de l'interaction");
            return false;
        }
    }

    public async Task<UserPreferenceDto?> GetUserPreferencesAsync(string userId)
    {
        try
        {
            var preferences = await _context.UserPreferences
                .Where(p => p.UserId == userId)
                .ToListAsync();

            if (!preferences.Any())
                return null;

            var categoryPrefs = preferences
                .Where(p => p.PreferenceType == "category")
                .ToDictionary(p => int.Parse(p.PreferenceValue), p => (double)p.Score);

            var brandPrefs = preferences
                .Where(p => p.PreferenceType == "brand")
                .ToDictionary(p => p.PreferenceValue, p => (double)p.Score);

            var featurePrefs = preferences
                .Where(p => p.PreferenceType == "feature")
                .ToDictionary(p => p.PreferenceValue, p => (double)p.Score);

            return new UserPreferenceDto
            {
                UserId = userId,
                CategoryPreferences = categoryPrefs,
                BrandPreferences = brandPrefs,
                FeaturePreferences = featurePrefs,
                LastUpdated = preferences.Max(p => p.UpdatedAt)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des préférences utilisateur");
            return null;
        }
    }

    public async Task<PersonalizationProfileDto?> GetPersonalizationProfileAsync(string userId)
    {
        try
        {
            // Simuler un profil de personnalisation
            var profile = new PersonalizationProfileDto
            {
                UserId = userId,
                Segment = UserSegment.RegularCustomer,
                ProfileCompleteness = 0.75,
                PreferredCategories = new List<int> { 1, 3, 5 },
                PreferredBrands = new List<string> { "Samsung", "Apple", "Nike" },
                AveragePurchaseAmount = 150000, // GNF
                TotalPurchases = 12,
                LastPurchaseDate = DateTime.UtcNow.AddDays(-15),
                Preferences = new Dictionary<string, double>
                {
                    ["price_sensitivity"] = 0.6,
                    ["brand_loyalty"] = 0.8,
                    ["quality_focus"] = 0.9
                }
            };

            return profile;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération du profil de personnalisation");
            return null;
        }
    }

    public async Task<ProductRecommendationDto?> GetProductDetailsAsync(int productId)
    {
        try
        {
            // Simuler les détails d'un produit
            var product = new ProductRecommendationDto
            {
                ProductId = productId,
                Name = $"Produit {productId}",
                Description = $"Description du produit {productId}",
                Price = 25000 + (productId * 1000),
                ImageUrl = $"/images/product-{productId}.jpg",
                Score = 0.8,
                Reason = "Produit populaire",
                CategoryId = (productId % 5) + 1,
                CategoryName = $"Catégorie {(productId % 5) + 1}",
                Brand = $"Marque {productId % 10}",
                Rating = 4.0 + (productId % 10) * 0.1,
                ReviewCount = 50 + (productId % 100),
                IsInStock = true,
                DiscountPercentage = productId % 3 == 0 ? 10 : 0
            };

            return product;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des détails du produit");
            return null;
        }
    }

    public async Task<List<FrequentItemDto>> GetFrequentlyBoughtTogetherAsync(int productId, int limit)
    {
        try
        {
            var frequentItems = new List<FrequentItemDto>();

            for (int i = 1; i <= limit; i++)
            {
                frequentItems.Add(new FrequentItemDto
                {
                    ProductId = productId + (i * 20),
                    Name = $"Produit Fréquent {i}",
                    Price = 18000 + (i * 2500),
                    ImageUrl = $"/images/frequent-{productId}-{i}.jpg",
                    Confidence = 0.9 - (i * 0.1),
                    CoOccurrenceCount = 100 - (i * 10),
                    IsInStock = true
                });
            }

            return frequentItems;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des produits fréquemment achetés ensemble");
            return new List<FrequentItemDto>();
        }
    }

    public async Task<bool> UpdateUserPreferencesAsync(UserPreferenceDto preferences)
    {
        try
        {
            // Supprimer les anciennes préférences
            var existingPrefs = await _context.UserPreferences
                .Where(p => p.UserId == preferences.UserId)
                .ToListAsync();

            _context.UserPreferences.RemoveRange(existingPrefs);

            // Ajouter les nouvelles préférences
            var newPrefs = new List<UserPreference>();

            foreach (var categoryPref in preferences.CategoryPreferences)
            {
                newPrefs.Add(new UserPreference
                {
                    UserId = preferences.UserId,
                    PreferenceType = "category",
                    PreferenceValue = categoryPref.Key.ToString(),
                    Score = (decimal)categoryPref.Value,
                    Confidence = 0.8m,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                });
            }

            foreach (var brandPref in preferences.BrandPreferences)
            {
                newPrefs.Add(new UserPreference
                {
                    UserId = preferences.UserId,
                    PreferenceType = "brand",
                    PreferenceValue = brandPref.Key,
                    Score = (decimal)brandPref.Value,
                    Confidence = 0.8m,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                });
            }

            _context.UserPreferences.AddRange(newPrefs);
            await _context.SaveChangesAsync();

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour des préférences utilisateur");
            return false;
        }
    }

    public async Task<bool> CreateUserPreferencesAsync(UserPreferenceDto preferences)
    {
        return await UpdateUserPreferencesAsync(preferences);
    }

    public async Task<bool> CreatePersonalizationProfileAsync(PersonalizationProfileDto profile)
    {
        try
        {
            // Dans une vraie implémentation, on sauvegarderait le profil en base
            _logger.LogInformation("Profil de personnalisation créé pour {UserId}", profile.UserId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création du profil de personnalisation");
            return false;
        }
    }

    public async Task<bool> InvalidateUserCacheAsync(string userId)
    {
        try
        {
            // Dans une vraie implémentation, on invaliderait le cache Redis
            _logger.LogInformation("Cache invalidé pour {UserId}", userId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'invalidation du cache");
            return false;
        }
    }
}


