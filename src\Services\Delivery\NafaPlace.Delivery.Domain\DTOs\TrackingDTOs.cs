using System.ComponentModel.DataAnnotations;

namespace NafaPlace.Delivery.Domain.DTOs;

/// <summary>
/// DTO pour la mise à jour de position GPS du livreur
/// </summary>
public class UpdatePositionDto
{
    [Required]
    [Range(-90, 90, ErrorMessage = "La latitude doit être entre -90 et 90")]
    public double Latitude { get; set; }

    [Required]
    [Range(-180, 180, ErrorMessage = "La longitude doit être entre -180 et 180")]
    public double Longitude { get; set; }

    /// <summary>
    /// Vitesse en km/h (optionnel)
    /// </summary>
    [Range(0, 200, ErrorMessage = "La vitesse doit être entre 0 et 200 km/h")]
    public double? Speed { get; set; }

    /// <summary>
    /// Direction en degrés (0-360)
    /// </summary>
    [Range(0, 360, ErrorMessage = "La direction doit être entre 0 et 360 degrés")]
    public double? Heading { get; set; }

    /// <summary>
    /// Précision GPS en mètres
    /// </summary>
    [Range(0, 1000, ErrorMessage = "La précision doit être entre 0 et 1000 mètres")]
    public double? Accuracy { get; set; }

    /// <summary>
    /// ID du livreur (sera défini par le contrôleur)
    /// </summary>
    public string DeliveryPersonId { get; set; } = "";

    /// <summary>
    /// Horodatage de la position (sera défini par le contrôleur)
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Mode de transport (vélo, moto, voiture, à pied)
    /// </summary>
    public string? TransportMode { get; set; }

    /// <summary>
    /// Statut du livreur (en route, arrivé, en attente)
    /// </summary>
    public string? Status { get; set; }
}

/// <summary>
/// DTO pour démarrer le suivi d'une livraison
/// </summary>
public class StartTrackingDto
{
    public int DeliveryId { get; set; }
    public string DeliveryPersonId { get; set; } = "";
    public DateTime StartTime { get; set; } = DateTime.UtcNow;

    [Required]
    public double StartLatitude { get; set; }

    [Required]
    public double StartLongitude { get; set; }

    /// <summary>
    /// Notes du livreur au démarrage
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Photo de départ (optionnel)
    /// </summary>
    public string? StartPhoto { get; set; }
}

/// <summary>
/// DTO pour terminer le suivi d'une livraison
/// </summary>
public class CompleteTrackingDto
{
    public int DeliveryId { get; set; }
    public string DeliveryPersonId { get; set; } = "";
    public DateTime CompletionTime { get; set; } = DateTime.UtcNow;

    [Required]
    public double FinalLatitude { get; set; }

    [Required]
    public double FinalLongitude { get; set; }

    /// <summary>
    /// Position finale
    /// </summary>
    public PositionDto FinalPosition => new()
    {
        Latitude = FinalLatitude,
        Longitude = FinalLongitude,
        Timestamp = CompletionTime
    };

    /// <summary>
    /// Notes de fin de livraison
    /// </summary>
    public string? CompletionNotes { get; set; }

    /// <summary>
    /// Photo de livraison (optionnel)
    /// </summary>
    public string? DeliveryPhoto { get; set; }

    /// <summary>
    /// Signature du destinataire (base64)
    /// </summary>
    public string? RecipientSignature { get; set; }

    /// <summary>
    /// Code de confirmation
    /// </summary>
    public string? ConfirmationCode { get; set; }
}

/// <summary>
/// DTO pour mettre à jour le statut d'une livraison avec géolocalisation
/// </summary>
public class UpdateDeliveryStatusDto
{
    public int DeliveryId { get; set; }
    public string DeliveryPersonId { get; set; } = "";

    [Required]
    public string Status { get; set; } = "";

    public double? CurrentLatitude { get; set; }
    public double? CurrentLongitude { get; set; }

    public PositionDto? CurrentPosition => CurrentLatitude.HasValue && CurrentLongitude.HasValue
        ? new PositionDto
        {
            Latitude = CurrentLatitude.Value,
            Longitude = CurrentLongitude.Value,
            Timestamp = UpdateTime
        }
        : null;

    public DateTime UpdateTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Message personnalisé pour ce changement de statut
    /// </summary>
    public string? StatusMessage { get; set; }

    /// <summary>
    /// Photo associée au changement de statut
    /// </summary>
    public string? StatusPhoto { get; set; }
}

/// <summary>
/// DTO pour une position GPS
/// </summary>
public class PositionDto
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public DateTime Timestamp { get; set; }
    public double? Speed { get; set; }
    public double? Heading { get; set; }
    public double? Accuracy { get; set; }
    public string? Status { get; set; }
}

/// <summary>
/// DTO pour la réponse de position actuelle d'une livraison
/// </summary>
public class DeliveryPositionDto
{
    public int DeliveryId { get; set; }
    public string DeliveryPersonId { get; set; } = "";
    public string DeliveryPersonName { get; set; } = "";
    public string DeliveryPersonPhone { get; set; } = "";
    public PositionDto CurrentPosition { get; set; } = new();
    public string Status { get; set; } = "";
    public DateTime LastUpdate { get; set; }
    public EstimatedArrivalDto? EstimatedArrival { get; set; }
    public string? TransportMode { get; set; }
    public string? DeliveryPersonPhoto { get; set; }
}

/// <summary>
/// DTO pour l'historique de suivi d'une livraison
/// </summary>
public class DeliveryTrackingHistoryDto
{
    public int DeliveryId { get; set; }
    public List<TrackingEventDto> Events { get; set; } = new();
    public List<PositionDto> Route { get; set; } = new();
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public double? TotalDistance { get; set; }
    public TimeSpan? TotalDuration { get; set; }
}

/// <summary>
/// DTO pour un événement de suivi
/// </summary>
public class TrackingEventDto
{
    public string EventType { get; set; } = "";
    public string Description { get; set; } = "";
    public DateTime Timestamp { get; set; }
    public PositionDto? Position { get; set; }
    public string? Photo { get; set; }
    public string? Notes { get; set; }
}

/// <summary>
/// DTO pour le temps estimé d'arrivée
/// </summary>
public class EstimatedArrivalDto
{
    public DateTime EstimatedTime { get; set; }
    public int MinutesRemaining { get; set; }
    public double DistanceRemaining { get; set; }
    public string DistanceUnit { get; set; } = "km";
    public string TrafficCondition { get; set; } = "normal";
    public double Confidence { get; set; } // 0-1 (0-100%)
}

/// <summary>
/// DTO pour les livraisons actives d'un livreur
/// </summary>
public class ActiveDeliveryDto
{
    public int Id { get; set; }
    public string OrderNumber { get; set; } = "";
    public string CustomerName { get; set; } = "";
    public string CustomerPhone { get; set; } = "";
    public string DeliveryAddress { get; set; } = "";
    public double DeliveryLatitude { get; set; }
    public double DeliveryLongitude { get; set; }
    public DateTime ScheduledTime { get; set; }
    public string Status { get; set; } = "";
    public string Priority { get; set; } = "normal";
    public decimal DeliveryFee { get; set; }
    public string PaymentMethod { get; set; } = "";
    public bool PaymentRequired { get; set; }
    public List<DeliveryItemDto> Items { get; set; } = new();
    public string? SpecialInstructions { get; set; }
}

/// <summary>
/// DTO pour les articles d'une livraison
/// </summary>
public class DeliveryItemDto
{
    public string ProductName { get; set; } = "";
    public int Quantity { get; set; }
    public decimal Price { get; set; }
    public string? Notes { get; set; }
}

/// <summary>
/// DTO pour les résultats d'opérations de suivi
/// </summary>
public class TrackingResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public string? TrackingId { get; set; }
    public DateTime? Timestamp { get; set; }
}