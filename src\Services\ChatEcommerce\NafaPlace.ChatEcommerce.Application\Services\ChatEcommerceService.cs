using Microsoft.Extensions.Logging;
using NafaPlace.ChatEcommerce.Application.DTOs;
using NafaPlace.ChatEcommerce.Application.Repositories;
using NafaPlace.ChatEcommerce.Domain.Entities;
using System.Text.Json;

namespace NafaPlace.ChatEcommerce.Application.Services;

public class ChatEcommerceService : IChatEcommerceService
{
    private readonly IConversationRepository _conversationRepository;
    private readonly IMessageRepository _messageRepository;
    private readonly IQuickReplyRepository _quickReplyRepository;
    private readonly IFAQRepository _faqRepository;
    private readonly IChatSessionRepository _sessionRepository;
    private readonly ILogger<ChatEcommerceService> _logger;

    public ChatEcommerceService(
        IConversationRepository conversationRepository,
        IMessageRepository messageRepository,
        IQuickReplyRepository quickReplyRepository,
        IFAQRepository faqRepository,
        IChatSessionRepository sessionRepository,
        ILogger<ChatEcommerceService> logger)
    {
        _conversationRepository = conversationRepository;
        _messageRepository = messageRepository;
        _quickReplyRepository = quickReplyRepository;
        _faqRepository = faqRepository;
        _sessionRepository = sessionRepository;
        _logger = logger;
    }

    // Gestion des conversations
    public async Task<int> CreateConversationAsync(CreateConversationDto dto)
    {
        try
        {
            var conversation = new Conversation
            {
                CustomerId = dto.CustomerId,
                CustomerName = dto.CustomerName,
                CustomerEmail = dto.CustomerEmail,
                SellerId = dto.SellerId,
                ProductId = dto.ProductId,
                OrderId = dto.OrderId,
                Subject = dto.Subject,
                Type = Enum.Parse<ConversationType>(dto.Type),
                Priority = Enum.Parse<ConversationPriority>(dto.Priority),
                Category = dto.Category,
                Tags = dto.Tags.Any() ? JsonSerializer.Serialize(dto.Tags) : null,
                Metadata = dto.Metadata.Any() ? JsonSerializer.Serialize(dto.Metadata) : null,
                Status = ConversationStatus.Open,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            var conversationId = await _conversationRepository.CreateAsync(conversation);

            // Créer le message initial si fourni
            if (!string.IsNullOrEmpty(dto.InitialMessage))
            {
                await SendMessageAsync(new SendMessageDto
                {
                    ConversationId = conversationId,
                    SenderId = dto.CustomerId,
                    SenderName = dto.CustomerName,
                    SenderType = "Customer",
                    Content = dto.InitialMessage
                });
            }

            _logger.LogInformation("Conversation créée avec succès: {ConversationId}", conversationId);
            return conversationId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de la conversation");
            throw;
        }
    }

    public async Task<ConversationDto?> GetConversationAsync(int conversationId)
    {
        try
        {
            var conversation = await _conversationRepository.GetByIdAsync(conversationId);
            return conversation != null ? MapToDto(conversation) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de la conversation {ConversationId}", conversationId);
            throw;
        }
    }

    public async Task<List<ConversationDto>> GetConversationsAsync(ConversationFilterDto filter)
    {
        try
        {
            var conversations = await _conversationRepository.GetByFilterAsync(filter);
            return conversations.Select(MapToDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des conversations");
            throw;
        }
    }

    public async Task<List<ConversationDto>> GetUserConversationsAsync(string userId)
    {
        try
        {
            var conversations = await _conversationRepository.GetByUserIdAsync(userId);
            return conversations.Select(MapToDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des conversations de l'utilisateur {UserId}", userId);
            throw;
        }
    }

    public async Task<List<ConversationDto>> GetSellerConversationsAsync(string sellerId)
    {
        try
        {
            var conversations = await _conversationRepository.GetBySellerIdAsync(sellerId);
            return conversations.Select(MapToDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des conversations du vendeur {SellerId}", sellerId);
            throw;
        }
    }

    public async Task<bool> UpdateConversationStatusAsync(int conversationId, string status, string? reason = null)
    {
        try
        {
            var conversation = await _conversationRepository.GetByIdAsync(conversationId);
            if (conversation == null) return false;

            conversation.Status = Enum.Parse<ConversationStatus>(status);
            conversation.UpdatedAt = DateTime.UtcNow;

            if (status == "Closed" && !string.IsNullOrEmpty(reason))
            {
                conversation.CloseReason = reason;
                conversation.ClosedAt = DateTime.UtcNow;
            }

            return await _conversationRepository.UpdateAsync(conversation);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour du statut de la conversation {ConversationId}", conversationId);
            throw;
        }
    }

    public async Task<bool> CloseConversationAsync(int conversationId, string closedBy, string? reason = null)
    {
        try
        {
            var conversation = await _conversationRepository.GetByIdAsync(conversationId);
            if (conversation == null) return false;

            conversation.Status = ConversationStatus.Closed;
            conversation.ClosedBy = closedBy;
            conversation.CloseReason = reason;
            conversation.ClosedAt = DateTime.UtcNow;
            conversation.UpdatedAt = DateTime.UtcNow;

            return await _conversationRepository.UpdateAsync(conversation);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la fermeture de la conversation {ConversationId}", conversationId);
            throw;
        }
    }

    public async Task<bool> ReopenConversationAsync(int conversationId)
    {
        try
        {
            var conversation = await _conversationRepository.GetByIdAsync(conversationId);
            if (conversation == null) return false;

            conversation.Status = ConversationStatus.Open;
            conversation.ClosedBy = null;
            conversation.CloseReason = null;
            conversation.ClosedAt = null;
            conversation.UpdatedAt = DateTime.UtcNow;

            return await _conversationRepository.UpdateAsync(conversation);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la réouverture de la conversation {ConversationId}", conversationId);
            throw;
        }
    }

    public async Task<bool> AssignConversationToSellerAsync(int conversationId, string sellerId)
    {
        try
        {
            var conversation = await _conversationRepository.GetByIdAsync(conversationId);
            if (conversation == null) return false;

            conversation.SellerId = sellerId;
            conversation.Status = ConversationStatus.InProgress;
            conversation.UpdatedAt = DateTime.UtcNow;

            return await _conversationRepository.UpdateAsync(conversation);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'assignation de la conversation {ConversationId} au vendeur {SellerId}", conversationId, sellerId);
            throw;
        }
    }

    // Gestion des messages
    public async Task<int> SendMessageAsync(SendMessageDto dto)
    {
        try
        {
            var message = new Message
            {
                ConversationId = dto.ConversationId,
                SenderId = dto.SenderId,
                SenderName = dto.SenderName,
                SenderType = Enum.Parse<SenderType>(dto.SenderType),
                Content = dto.Content,
                MessageType = Enum.Parse<MessageType>(dto.MessageType),
                AttachmentUrl = dto.AttachmentUrl,
                AttachmentType = dto.AttachmentType,
                AttachmentSize = dto.AttachmentSize,
                Metadata = dto.Metadata.Any() ? JsonSerializer.Serialize(dto.Metadata) : null,
                Timestamp = DateTime.UtcNow
            };

            var messageId = await _messageRepository.CreateAsync(message);

            // Mettre à jour la conversation
            var conversation = await _conversationRepository.GetByIdAsync(dto.ConversationId);
            if (conversation != null)
            {
                conversation.LastMessageAt = DateTime.UtcNow;
                conversation.UpdatedAt = DateTime.UtcNow;
                conversation.HasUnreadMessages = true;
                conversation.UnreadCount++;
                await _conversationRepository.UpdateAsync(conversation);
            }

            _logger.LogInformation("Message envoyé avec succès: {MessageId}", messageId);
            return messageId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi du message");
            throw;
        }
    }

    public async Task<List<MessageDto>> GetConversationMessagesAsync(int conversationId, int page = 1, int pageSize = 50)
    {
        try
        {
            var messages = await _messageRepository.GetByConversationIdAsync(conversationId, page, pageSize);
            return messages.Select(MapToDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des messages de la conversation {ConversationId}", conversationId);
            throw;
        }
    }

    public async Task<bool> MarkMessageAsReadAsync(int messageId, string userId)
    {
        try
        {
            return await _messageRepository.MarkAsReadAsync(messageId, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du marquage du message {MessageId} comme lu", messageId);
            throw;
        }
    }

    public async Task<bool> MarkConversationAsReadAsync(int conversationId, string userId)
    {
        try
        {
            return await _messageRepository.MarkConversationAsReadAsync(conversationId, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du marquage de la conversation {ConversationId} comme lue", conversationId);
            throw;
        }
    }

    public async Task<int> GetUnreadMessageCountAsync(string userId)
    {
        try
        {
            return await _messageRepository.CountUnreadAsync(userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du comptage des messages non lus pour l'utilisateur {UserId}", userId);
            throw;
        }
    }

    // Méthodes de mapping
    private ConversationDto MapToDto(Conversation conversation)
    {
        return new ConversationDto
        {
            Id = conversation.Id,
            CustomerId = conversation.CustomerId,
            CustomerName = conversation.CustomerName,
            CustomerEmail = conversation.CustomerEmail,
            SellerId = conversation.SellerId,
            SellerName = conversation.SellerName,
            ProductId = conversation.ProductId,
            ProductName = conversation.ProductName,
            OrderId = conversation.OrderId,
            Subject = conversation.Subject,
            Type = conversation.Type.ToString(),
            Status = conversation.Status.ToString(),
            Priority = conversation.Priority.ToString(),
            Category = conversation.Category,
            CreatedAt = conversation.CreatedAt,
            UpdatedAt = conversation.UpdatedAt,
            LastMessageAt = conversation.LastMessageAt,
            ClosedAt = conversation.ClosedAt,
            ClosedBy = conversation.ClosedBy,
            CloseReason = conversation.CloseReason,
            HasUnreadMessages = conversation.HasUnreadMessages,
            UnreadCount = conversation.UnreadCount,
            Tags = !string.IsNullOrEmpty(conversation.Tags) 
                ? JsonSerializer.Deserialize<List<string>>(conversation.Tags) ?? new List<string>()
                : new List<string>(),
            Metadata = !string.IsNullOrEmpty(conversation.Metadata) 
                ? JsonSerializer.Deserialize<Dictionary<string, object>>(conversation.Metadata) ?? new Dictionary<string, object>()
                : new Dictionary<string, object>(),
            Messages = conversation.Messages?.Select(MapToDto).ToList() ?? new List<MessageDto>(),
            LastMessage = conversation.Messages?.OrderByDescending(m => m.Timestamp).FirstOrDefault()?.Content
        };
    }

    private MessageDto MapToDto(Message message)
    {
        return new MessageDto
        {
            Id = message.Id,
            ConversationId = message.ConversationId,
            SenderId = message.SenderId,
            SenderName = message.SenderName,
            SenderType = message.SenderType.ToString(),
            Content = message.Content,
            MessageType = message.MessageType.ToString(),
            Timestamp = message.Timestamp,
            IsRead = message.IsRead,
            ReadAt = message.ReadAt,
            IsEdited = message.IsEdited,
            EditedAt = message.EditedAt,
            AttachmentUrl = message.AttachmentUrl,
            AttachmentType = message.AttachmentType,
            AttachmentSize = message.AttachmentSize,
            Metadata = !string.IsNullOrEmpty(message.Metadata)
                ? JsonSerializer.Deserialize<Dictionary<string, object>>(message.Metadata) ?? new Dictionary<string, object>()
                : new Dictionary<string, object>()
        };
    }

    // Support produit
    public async Task<int> CreateProductInquiryAsync(string customerId, string customerName, int productId, string message)
    {
        var dto = new CreateConversationDto
        {
            CustomerId = customerId,
            CustomerName = customerName,
            ProductId = productId,
            Subject = $"Question sur le produit #{productId}",
            Type = "ProductInquiry",
            Priority = "Normal",
            Category = "Produit",
            InitialMessage = message
        };

        return await CreateConversationAsync(dto);
    }

    public async Task<int> CreateOrderSupportAsync(string customerId, string customerName, int orderId, string message, string issueType)
    {
        var dto = new CreateConversationDto
        {
            CustomerId = customerId,
            CustomerName = customerName,
            OrderId = orderId,
            Subject = $"Support commande #{orderId} - {issueType}",
            Type = "OrderSupport",
            Priority = "High",
            Category = "Commande",
            InitialMessage = message
        };

        return await CreateConversationAsync(dto);
    }

    public async Task<List<ConversationDto>> GetProductConversationsAsync(int productId)
    {
        try
        {
            var conversations = await _conversationRepository.GetByProductIdAsync(productId);
            return conversations.Select(MapToDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des conversations du produit {ProductId}", productId);
            throw;
        }
    }

    public async Task<List<ConversationDto>> GetOrderConversationsAsync(int orderId)
    {
        try
        {
            var conversations = await _conversationRepository.GetByOrderIdAsync(orderId);
            return conversations.Select(MapToDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des conversations de la commande {OrderId}", orderId);
            throw;
        }
    }

    // FAQ et réponses rapides
    public async Task<List<FAQDto>> GetFAQsAsync(string? category = null)
    {
        try
        {
            var faqs = string.IsNullOrEmpty(category)
                ? await _faqRepository.GetActiveAsync()
                : await _faqRepository.GetByCategoryAsync(category);

            return faqs.Select(f => new FAQDto
            {
                Id = f.Id,
                Question = f.Question,
                Answer = f.Answer,
                Category = f.Category,
                IsActive = f.IsActive,
                ViewCount = f.ViewCount,
                HelpfulCount = f.HelpfulCount,
                NotHelpfulCount = f.NotHelpfulCount,
                SortOrder = f.SortOrder,
                Tags = !string.IsNullOrEmpty(f.Tags)
                    ? JsonSerializer.Deserialize<List<string>>(f.Tags) ?? new List<string>()
                    : new List<string>()
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des FAQs");
            throw;
        }
    }

    public async Task<List<FAQDto>> SearchFAQsAsync(string query)
    {
        try
        {
            var faqs = await _faqRepository.SearchAsync(query);
            return faqs.Select(f => new FAQDto
            {
                Id = f.Id,
                Question = f.Question,
                Answer = f.Answer,
                Category = f.Category,
                IsActive = f.IsActive,
                ViewCount = f.ViewCount,
                HelpfulCount = f.HelpfulCount,
                NotHelpfulCount = f.NotHelpfulCount,
                SortOrder = f.SortOrder,
                Tags = !string.IsNullOrEmpty(f.Tags)
                    ? JsonSerializer.Deserialize<List<string>>(f.Tags) ?? new List<string>()
                    : new List<string>()
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche des FAQs");
            throw;
        }
    }

    public async Task<List<QuickReplyDto>> GetQuickRepliesAsync(string? category = null)
    {
        try
        {
            var replies = string.IsNullOrEmpty(category)
                ? await _quickReplyRepository.GetActiveAsync()
                : await _quickReplyRepository.GetByCategoryAsync(category);

            return replies.Select(r => new QuickReplyDto
            {
                Id = r.Id,
                Title = r.Title,
                Content = r.Content,
                Category = r.Category,
                IsActive = r.IsActive,
                SortOrder = r.SortOrder,
                Tags = !string.IsNullOrEmpty(r.Tags)
                    ? JsonSerializer.Deserialize<List<string>>(r.Tags) ?? new List<string>()
                    : new List<string>()
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des réponses rapides");
            throw;
        }
    }

    public async Task<int> CreateQuickReplyAsync(QuickReply quickReply)
    {
        try
        {
            return await _quickReplyRepository.CreateAsync(quickReply);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de la réponse rapide");
            throw;
        }
    }

    public async Task<bool> UpdateQuickReplyAsync(QuickReply quickReply)
    {
        try
        {
            return await _quickReplyRepository.UpdateAsync(quickReply);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour de la réponse rapide {Id}", quickReply.Id);
            throw;
        }
    }

    public async Task<bool> DeleteQuickReplyAsync(int id)
    {
        try
        {
            return await _quickReplyRepository.DeleteAsync(id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de la réponse rapide {Id}", id);
            throw;
        }
    }

    public async Task<bool> RecordFAQFeedbackAsync(int faqId, bool isHelpful)
    {
        try
        {
            return await _faqRepository.RecordFeedbackAsync(faqId, isHelpful);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'enregistrement du feedback FAQ {FaqId}", faqId);
            throw;
        }
    }

    // Sessions et présence
    public async Task<bool> UpdateUserPresenceAsync(string userId, bool isOnline)
    {
        try
        {
            return await _sessionRepository.UpdatePresenceAsync(userId, isOnline);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour de la présence de l'utilisateur {UserId}", userId);
            throw;
        }
    }

    public async Task<List<ChatSessionDto>> GetOnlineUsersAsync()
    {
        try
        {
            var sessions = await _sessionRepository.GetOnlineUsersAsync();
            return sessions.Select(s => new ChatSessionDto
            {
                Id = s.Id,
                SessionId = s.SessionId,
                UserId = s.UserId,
                UserName = s.UserName,
                IsOnline = s.IsOnline,
                LastActivity = s.LastActivity,
                CreatedAt = s.CreatedAt,
                Metadata = !string.IsNullOrEmpty(s.Metadata)
                    ? JsonSerializer.Deserialize<Dictionary<string, object>>(s.Metadata) ?? new Dictionary<string, object>()
                    : new Dictionary<string, object>()
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des utilisateurs en ligne");
            throw;
        }
    }

    public async Task<bool> IsUserOnlineAsync(string userId)
    {
        try
        {
            var session = await _sessionRepository.GetByUserIdAsync(userId);
            return session?.IsOnline ?? false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification du statut en ligne de l'utilisateur {UserId}", userId);
            throw;
        }
    }

    // Statistiques
    public async Task<ConversationStatsDto> GetConversationStatsAsync(string? userId = null, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var filter = new ConversationFilterDto
            {
                CustomerId = userId,
                StartDate = startDate,
                EndDate = endDate
            };

            var totalConversations = await _conversationRepository.CountAsync(filter);
            var conversationsByStatus = await _conversationRepository.GetConversationsByStatusAsync(startDate, endDate);
            var conversationsByType = await _conversationRepository.GetConversationsByTypeAsync(startDate, endDate);

            return new ConversationStatsDto
            {
                TotalConversations = totalConversations,
                OpenConversations = conversationsByStatus.GetValueOrDefault("Open", 0),
                InProgressConversations = conversationsByStatus.GetValueOrDefault("InProgress", 0),
                ResolvedConversations = conversationsByStatus.GetValueOrDefault("Resolved", 0),
                ClosedConversations = conversationsByStatus.GetValueOrDefault("Closed", 0),
                UnreadConversations = userId != null ? await _conversationRepository.CountUnreadAsync(userId) : 0,
                AverageResponseTime = await _conversationRepository.GetAverageResponseTimeAsync(userId, startDate, endDate),
                AverageResolutionTime = await _conversationRepository.GetAverageResolutionTimeAsync(userId, startDate, endDate),
                ConversationsByType = conversationsByType,
                ConversationsByPriority = new Dictionary<string, int>() // À implémenter si nécessaire
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des statistiques");
            throw;
        }
    }

    public async Task<Dictionary<string, int>> GetConversationsByTypeAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            return await _conversationRepository.GetConversationsByTypeAsync(startDate, endDate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des conversations par type");
            throw;
        }
    }

    public async Task<Dictionary<string, int>> GetConversationsByStatusAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            return await _conversationRepository.GetConversationsByStatusAsync(startDate, endDate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des conversations par statut");
            throw;
        }
    }

    // Recherche et filtrage
    public async Task<List<ConversationDto>> SearchConversationsAsync(string query, ConversationFilterDto? filter = null)
    {
        try
        {
            var conversations = await _conversationRepository.SearchAsync(query, filter);
            return conversations.Select(MapToDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche des conversations");
            throw;
        }
    }

    public async Task<List<MessageDto>> SearchMessagesAsync(string query, int? conversationId = null)
    {
        try
        {
            var messages = await _messageRepository.SearchAsync(query, conversationId);
            return messages.Select(MapToDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche des messages");
            throw;
        }
    }

    // Notifications temps réel (SignalR) - Stubs pour l'instant
    public async Task NotifyNewMessageAsync(int conversationId, MessageDto message)
    {
        // TODO: Implémenter SignalR
        await Task.CompletedTask;
    }

    public async Task NotifyTypingAsync(int conversationId, TypingIndicatorDto typing)
    {
        // TODO: Implémenter SignalR
        await Task.CompletedTask;
    }

    public async Task NotifyUserStatusAsync(string userId, bool isOnline)
    {
        // TODO: Implémenter SignalR
        await Task.CompletedTask;
    }

    public async Task NotifyConversationStatusAsync(int conversationId, string status)
    {
        // TODO: Implémenter SignalR
        await Task.CompletedTask;
    }
}
