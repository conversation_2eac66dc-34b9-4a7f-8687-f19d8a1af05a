{
  "Routes": [
    // Identity Service Routes
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "nafaplace-identity-api.onrender.com",
          "Port": 443
        }
      ],
      "UpstreamPathTemplate": "/api/identity/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "identity"
    },

    // Auth Service Routes (alias for Identity)
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "nafaplace-identity-api.onrender.com",
          "Port": 443
        }
      ],
      "UpstreamPathTemplate": "/api/auth/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "auth"
    },

    // Users Service Routes (alias for Identity)
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "nafaplace-identity-api.onrender.com",
          "Port": 443
        }
      ],
      "UpstreamPathTemplate": "/api/users/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "users"
    },

    // Catalog Service Routes
    {
      "DownstreamPathTemplate": "/api/v1/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "nafaplace-catalog-api.onrender.com",
          "Port": 443
        }
      ],
      "UpstreamPathTemplate": "/api/catalog/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "catalog"
    },

    // Cart Service Routes
    {
      "DownstreamPathTemplate": "/api/cart/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "nafaplace-cart-api.onrender.com",
          "Port": 443
        }
      ],
      "UpstreamPathTemplate": "/api/cart/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "cart"
    },

    // Order Service Routes
    {
      "DownstreamPathTemplate": "/api/orders/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "nafaplace-order-api.onrender.com",
          "Port": 443
        }
      ],
      "UpstreamPathTemplate": "/api/orders/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "orders"
    },

    // Payment Service Routes
    {
      "DownstreamPathTemplate": "/api/payments/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "nafaplace-payment-api.onrender.com",
          "Port": 443
        }
      ],
      "UpstreamPathTemplate": "/api/payments/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "payments"
    },

    // Reviews Service Routes
    {
      "DownstreamPathTemplate": "/api/reviews/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "nafaplace-reviews-api.onrender.com",
          "Port": 443
        }
      ],
      "UpstreamPathTemplate": "/api/reviews/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "reviews"
    },

    // Notifications Service Routes
    {
      "DownstreamPathTemplate": "/api/notifications/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "nafaplace-notifications-api.onrender.com",
          "Port": 443
        }
      ],
      "UpstreamPathTemplate": "/api/notifications/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "notifications"
    },

    // Wishlist Service Routes
    {
      "DownstreamPathTemplate": "/api/wishlist/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "nafaplace-wishlist-api.onrender.com",
          "Port": 443
        }
      ],
      "UpstreamPathTemplate": "/api/wishlist/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "wishlist"
    },

    // Inventory Service Routes
    {
      "DownstreamPathTemplate": "/api/v1/inventory/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "nafaplace-inventory-api.onrender.com",
          "Port": 443
        }
      ],
      "UpstreamPathTemplate": "/api/inventory/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "inventory"
    },

    // Coupon Service Routes
    {
      "DownstreamPathTemplate": "/api/coupon/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "nafaplace-coupon-api.onrender.com",
          "Port": 443
        }
      ],
      "UpstreamPathTemplate": "/api/coupon/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "coupon"
    },

    // Delivery Service Routes
    {
      "DownstreamPathTemplate": "/api/delivery/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "nafaplace-delivery-api.onrender.com",
          "Port": 443
        }
      ],
      "UpstreamPathTemplate": "/api/delivery/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "delivery"
    },

    // Analytics Service Routes
    {
      "DownstreamPathTemplate": "/api/analytics/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "nafaplace-analytics-api.onrender.com",
          "Port": 443
        }
      ],
      "UpstreamPathTemplate": "/api/analytics/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "analytics"
    },

    // Localization Service Routes
    {
      "DownstreamPathTemplate": "/api/localization/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "nafaplace-localization-api.onrender.com",
          "Port": 443
        }
      ],
      "UpstreamPathTemplate": "/api/localization/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "localization"
    }
  ],

  "GlobalConfiguration": {
    "BaseUrl": "https://nafaplace-api-gateway.onrender.com",
    "RequestIdKey": "OcRequestId",
    "AdministrationPath": "/administration"
  }
}
