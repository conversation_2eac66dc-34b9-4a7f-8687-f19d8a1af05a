@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Routing
@inject NavigationManager NavigationManager

<style>
    .nav-section-header {
        cursor: pointer;
        padding: 0.5rem 1rem;
        color: #fff;
        font-weight: 500;
        display: flex;
        align-items: center;
        transition: background-color 0.2s;
    }

    .nav-section-header:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .nav-subsection {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-out;
        background-color: rgba(0, 0, 0, 0.1);
    }

    .nav-subsection.show {
        max-height: 500px;
    }

    .nav-subsection .nav-link {
        padding-left: 2rem;
        font-size: 0.9rem;
    }
</style>

<div class="top-row ps-3 navbar navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="">NafaPlace Vendeur</a>
        <button title="Navigation menu" class="navbar-toggler" @onclick="ToggleNavMenu">
            <span class="navbar-toggler-icon"></span>
        </button>
    </div>
</div>

<div class="@NavMenuCssClass nav-scrollable" @onclick="ToggleNavMenu">
    <nav class="nav flex-column">
        <div class="nav-item px-3">
            <a class="nav-link @(GetActive(""))" href="">
                <span class="bi bi-house-door-fill-nav-menu" aria-hidden="true"></span> Accueil
            </a>
        </div>
        <div class="nav-item px-3">
            <a class="nav-link @(GetActive("dashboard"))" href="dashboard">
                <span class="fas fa-tachometer-alt" aria-hidden="true"></span> Dashboard
            </a>
        </div>
        <div class="nav-item px-3">
            <a class="nav-link @(GetActive("products"))" href="products">
                <span class="fas fa-box" aria-hidden="true"></span> Produits
            </a>
        </div>
        <div class="nav-item px-3">
            <a class="nav-link @(GetActive("inventory"))" href="inventory">
                <span class="fas fa-warehouse" aria-hidden="true"></span> Inventaire
            </a>
        </div>
        <div class="nav-item px-3">
            <a class="nav-link @(GetActive("orders"))" href="orders">
                <span class="fas fa-shopping-cart" aria-hidden="true"></span> Commandes
            </a>
        </div>
        <div class="nav-item px-3">
            <a class="nav-link @(GetActive("reviews"))" href="reviews">
                <span class="fas fa-star" aria-hidden="true"></span> Avis
            </a>
        </div>
        <div class="nav-item px-3">
            <a class="nav-link @(GetActive("coupons"))" href="coupons">
                <span class="fas fa-ticket-alt" aria-hidden="true"></span> Coupons
            </a>
        </div>
        <div class="nav-item px-3">
            <a class="nav-link @(GetActive("statistics"))" href="statistics">
                <span class="fas fa-chart-bar" aria-hidden="true"></span> Statistiques
            </a>
        </div>

        <!-- Section Analytics Avancées -->
        <div class="nav-item px-3 mt-2">
            <div class="nav-section-header" @onclick="ToggleAnalyticsSection">
                <span class="fas fa-chart-line me-2" aria-hidden="true"></span>
                <span>Analytics Avancées</span>
                <span class="fas @(showAnalyticsSection ? "fa-chevron-down" : "fa-chevron-right") ms-auto" aria-hidden="true"></span>
            </div>
        </div>
        <div class="nav-subsection @(showAnalyticsSection ? "show" : "")">
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("analytics/dashboard"))" href="analytics/dashboard">
                    <span class="fas fa-tachometer-alt" aria-hidden="true"></span> Dashboard Avancé
                </a>
            </div>
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("analytics/sales"))" href="analytics/sales">
                    <span class="fas fa-chart-area" aria-hidden="true"></span> Analyse des Ventes
                </a>
            </div>
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("analytics/products"))" href="analytics/products">
                    <span class="fas fa-box-open" aria-hidden="true"></span> Performance Produits
                </a>
            </div>
        </div>

        <!-- Section Communication -->
        <div class="nav-item px-3 mt-2">
            <div class="nav-section-header" @onclick="ToggleCommunicationSection">
                <span class="fas fa-comments me-2" aria-hidden="true"></span>
                <span>Communication</span>
                <span class="fas @(showCommunicationSection ? "fa-chevron-down" : "fa-chevron-right") ms-auto" aria-hidden="true"></span>
            </div>
        </div>
        <div class="nav-subsection @(showCommunicationSection ? "show" : "")">
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("chat/support"))" href="chat/support">
                    <span class="fas fa-headset" aria-hidden="true"></span> Support Client
                </a>
            </div>
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("notifications"))" href="notifications">
                    <span class="fas fa-bell" aria-hidden="true"></span> Notifications
                </a>
            </div>
        </div>

        <!-- Section Outils Avancés -->
        <div class="nav-item px-3 mt-2">
            <div class="nav-section-header" @onclick="ToggleToolsSection">
                <span class="fas fa-tools me-2" aria-hidden="true"></span>
                <span>Outils Avancés</span>
                <span class="fas @(showToolsSection ? "fa-chevron-down" : "fa-chevron-right") ms-auto" aria-hidden="true"></span>
            </div>
        </div>
        <div class="nav-subsection @(showToolsSection ? "show" : "")">
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("tracking/orders"))" href="tracking/orders">
                    <span class="fas fa-route" aria-hidden="true"></span> Suivi Commandes
                </a>
            </div>
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("reports"))" href="reports">
                    <span class="fas fa-file-alt" aria-hidden="true"></span> Rapports
                </a>
            </div>
        </div>
    </nav>
</div>

@code {
    private bool collapseNavMenu = true;
    private bool showAnalyticsSection = false;
    private bool showCommunicationSection = false;
    private bool showToolsSection = false;

    private string? NavMenuCssClass => collapseNavMenu ? "collapse" : null;

    private void ToggleNavMenu()
    {
        collapseNavMenu = !collapseNavMenu;
    }

    private void ToggleAnalyticsSection()
    {
        showAnalyticsSection = !showAnalyticsSection;
    }

    private void ToggleCommunicationSection()
    {
        showCommunicationSection = !showCommunicationSection;
    }

    private void ToggleToolsSection()
    {
        showToolsSection = !showToolsSection;
    }
    
    private string GetActive(string href)
    {
        var uri = new Uri(NavigationManager.Uri);
        var path = uri.AbsolutePath;
        
        if (string.IsNullOrEmpty(href) && path == "/")
            return "active";
            
        if (!string.IsNullOrEmpty(href) && path.StartsWith($"/{href}"))
            return "active";
            
        return "";
    }
}
