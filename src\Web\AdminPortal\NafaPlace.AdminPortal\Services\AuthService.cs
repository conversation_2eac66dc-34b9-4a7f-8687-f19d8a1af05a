﻿using System.Net.Http.Json;
using System.Net.Http.Headers;
using System.Text.Json;
using Microsoft.AspNetCore.Components.Authorization;
using Blazored.LocalStorage;
using NafaPlace.AdminPortal.Models.Auth;
using NafaPlace.AdminPortal.Pages.Users;

namespace NafaPlace.AdminPortal.Services;

public class AuthService : IAuthService
{
    private readonly HttpClient _httpClient;
    private readonly ILocalStorageService _localStorage;
    private readonly AuthenticationStateProvider _authStateProvider;

    public event Action? AuthenticationStateChanged;

    public AuthService(HttpClient httpClient, ILocalStorageService localStorage, AuthenticationStateProvider authStateProvider)
    {
        _httpClient = httpClient;
        _localStorage = localStorage;
        _authStateProvider = authStateProvider;
    }

    public async Task<AuthResponse> LoginAsync(string email, string password)
    {
        try
        {
            var loginRequest = new { Email = email, Password = password };
            var response = await _httpClient.PostAsJsonAsync("api/auth/login", loginRequest);
            
            if (response.IsSuccessStatusCode)
            {
                var authResponse = await response.Content.ReadFromJsonAsync<AuthResponse>();
                if (authResponse != null && authResponse.Success && !string.IsNullOrEmpty(authResponse.Token))
                {
                    await _localStorage.SetItemAsync("authToken", authResponse.Token);
                    await _localStorage.SetItemAsync("userEmail", email);
                    
                    AuthenticationStateChanged?.Invoke();
                    
                    return authResponse;
                }
            }
            
            return new AuthResponse { Success = false, Message = "Login failed" };
        }
        catch (Exception ex)
        {
            Console.WriteLine($"LoginAsync Error: {ex.Message}");
            return new AuthResponse { Success = false, Message = ex.Message };
        }
    }

    public async Task LogoutAsync()
    {
        try
        {
            await _localStorage.RemoveItemAsync("authToken");
            await _localStorage.RemoveItemAsync("userEmail");
            
            AuthenticationStateChanged?.Invoke();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"LogoutAsync Error: {ex.Message}");
        }
    }

    public async Task<bool> IsAuthenticatedAsync()
    {
        try
        {
            var token = await _localStorage.GetItemAsync<string>("authToken");
            return !string.IsNullOrEmpty(token);
        }
        catch
        {
            return false;
        }
    }

    public async Task<string?> GetTokenAsync()
    {
        try
        {
            return await _localStorage.GetItemAsync<string>("authToken");
        }
        catch
        {
            return null;
        }
    }

    // Méthodes simplifiées pour la compilation
    public async Task<UserListResponse> GetUsersAsync(string searchTerm = "", int page = 1, int pageSize = 10)
    {
        return new UserListResponse { Users = new List<UserDto>(), TotalCount = 0 };
    }

    public async Task<List<RoleDto>> GetRolesAsync()
    {
        return new List<RoleDto>();
    }

    public async Task<bool> CreateUserAsync(object request)
    {
        return false;
    }

    public async Task<bool> UpdateUserAsync(int userId, object request)
    {
        return false;
    }

    public async Task<bool> DeleteUserAsync(int userId)
    {
        return false;
    }

    public async Task<bool> CreateRoleAsync(object request)
    {
        return false;
    }

    public async Task<bool> UpdateRoleAsync(int roleId, object request)
    {
        return false;
    }

    public async Task<bool> DeleteRoleAsync(int roleId)
    {
        return false;
    }

    public async Task<bool> AssignRoleToUserAsync(int userId, int roleId)
    {
        return false;
    }

    public async Task<bool> RemoveRoleFromUserAsync(int userId, int roleId)
    {
        return false;
    }

    public async Task<UserDto> GetCurrentUserAsync()
    {
        return new UserDto();
    }

    public async Task<UserDto> GetUserByIdAsync(int userId)
    {
        try
        {
            Console.WriteLine($"[AuthService] Récupération des infos pour userId: {userId}");

            // Récupérer le token d'authentification
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                Console.WriteLine("[AuthService] ❌ Token non trouvé");
                return new UserDto();
            }

            // Créer un HttpClient temporaire avec le token
            using var request = new HttpRequestMessage(HttpMethod.Get, $"/api/users/internal/{userId}");
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await _httpClient.SendAsync(request);
            Console.WriteLine($"[AuthService] Appel API: /api/users/internal/{userId} - Status: {response.StatusCode}");

            if (response.IsSuccessStatusCode)
            {
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                var userDto = await response.Content.ReadFromJsonAsync<UserDto>(jsonOptions);
                if (userDto != null)
                {
                    Console.WriteLine($"[AuthService] ✅ Info utilisateur récupérée: {userId} -> {userDto.FirstName} {userDto.LastName}");
                    return userDto;
                }
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"[AuthService] ❌ Erreur API: {errorContent}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[AuthService] ❌ Exception: {ex.Message}");
        }

        return new UserDto();
    }

    public async Task<bool> RegisterUserAsync(object userModel)
    {
        return false;
    }

    public async Task<bool> UpdateUserAsync(object userModel)
    {
        return false;
    }

    public async Task<bool> UpdateUserRolesAsync(int userId, List<string> roles)
    {
        return false;
    }

    public async Task<bool> AssignRoleToUserAsync(int userId, string roleName)
    {
        return false;
    }

    public async Task<RoleDto> CreateRoleAsync(RoleDto role)
    {
        return new RoleDto();
    }

    public async Task<RoleDto> UpdateRoleAsync(RoleDto role)
    {
        return new RoleDto();
    }
}
