using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Microsoft.JSInterop;

namespace NafaPlace.SellerPortal.Services;

public class ChatEcommerceService : IChatEcommerceService
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ILogger<ChatEcommerceService> _logger;
    private readonly IJSRuntime _jsRuntime;

    public ChatEcommerceService(
        IHttpClientFactory httpClientFactory,
        ILogger<ChatEcommerceService> logger,
        IJSRuntime jsRuntime)
    {
        _httpClientFactory = httpClientFactory;
        _logger = logger;
        _jsRuntime = jsRuntime;
    }

    private async Task SetAuthorizationHeaderAsync(HttpClient httpClient)
    {
        try
        {
            var token = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", "authToken");
            Console.WriteLine($"[ChatEcommerceService] Token récupéré: {(string.IsNullOrEmpty(token) ? "VIDE" : $"longueur={token.Length}")}");

            if (!string.IsNullOrEmpty(token))
            {
                // Nettoyer le token des guillemets potentiels
                var cleanToken = token.Trim('"');
                Console.WriteLine($"[ChatEcommerceService] Token nettoyé: longueur={cleanToken.Length}");

                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", cleanToken);
                Console.WriteLine($"[ChatEcommerceService] ✅ En-tête Authorization ajouté avec token nettoyé");
            }
            else
            {
                Console.WriteLine($"[ChatEcommerceService] ❌ Token vide, pas d'en-tête Authorization");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[ChatEcommerceService] ❌ Erreur lors de la récupération du token: {ex.Message}");
            _logger.LogError(ex, "Erreur lors de la récupération du token");
        }
    }

    public async Task<bool> IsHealthyAsync()
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient("ChatEcommerceApi");
            var response = await httpClient.GetAsync("health");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification de santé du service Chat");
            return false;
        }
    }

    // FAQ Methods
    public async Task<List<FAQDto>> GetFAQsAsync(string? category = null)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient("ChatEcommerceApi");

            var url = "support/faqs";
            if (!string.IsNullOrEmpty(category))
            {
                url += $"?category={Uri.EscapeDataString(category)}";
            }

            var response = await httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadAsStringAsync();
            var faqs = JsonSerializer.Deserialize<List<FAQDto>>(json, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return faqs ?? new List<FAQDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des FAQs");
            return new List<FAQDto>();
        }
    }

    public async Task<List<string>> GetFAQCategoriesAsync()
    {
        try
        {
            // Récupérer toutes les FAQs et extraire les catégories uniques
            var faqs = await GetFAQsAsync();
            var categories = faqs
                .Where(f => !string.IsNullOrEmpty(f.Category))
                .Select(f => f.Category)
                .Distinct()
                .OrderBy(c => c)
                .ToList();

            return categories;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des catégories FAQ");
            return new List<string> { "Commandes", "Paiement", "Livraison", "Support", "Retours" };
        }
    }

    // Quick Reply Methods (requires authentication)
    public async Task<List<QuickReplyDto>> GetQuickRepliesAsync(string? category = null)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient("ChatEcommerceApi");
            await SetAuthorizationHeaderAsync(httpClient);

            var url = "support/quick-replies";
            if (!string.IsNullOrEmpty(category))
            {
                url += $"?category={Uri.EscapeDataString(category)}";
            }

            var response = await httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadAsStringAsync();
            var quickReplies = JsonSerializer.Deserialize<List<QuickReplyDto>>(json, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return quickReplies ?? new List<QuickReplyDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des réponses rapides");
            return new List<QuickReplyDto>();
        }
    }

    public async Task<List<QuickReplyDto>> GetQuickRepliesAsync(int sellerId)
    {
        // Pour l'instant, on retourne toutes les réponses rapides
        // Dans une version future, on pourrait filtrer par sellerId
        return await GetQuickRepliesAsync(null);
    }

    public async Task<int> CreateQuickReplyAsync(CreateQuickReplyDto quickReply)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient("ChatEcommerceApi");
            await SetAuthorizationHeaderAsync(httpClient);

            // Convertir la liste de tags en chaîne JSON pour l'API
            var apiDto = new
            {
                title = quickReply.Title,
                content = quickReply.Content,
                category = quickReply.Category,
                tags = quickReply.Tags != null && quickReply.Tags.Any()
                    ? JsonSerializer.Serialize(quickReply.Tags)
                    : null
            };

            var json = JsonSerializer.Serialize(apiDto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await httpClient.PostAsync("support/quick-replies", content);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                if (int.TryParse(responseContent, out int id))
                {
                    _logger.LogInformation("Réponse rapide créée avec succès: {Id}", id);
                    Console.WriteLine($"[ChatEcommerceService] ✅ Réponse rapide créée: ID={id}");
                    return id;
                }
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("Erreur HTTP {StatusCode} lors de la création de la réponse rapide: {Error}",
                    response.StatusCode, errorContent);
                Console.WriteLine($"[ChatEcommerceService] ❌ Erreur {response.StatusCode}: {errorContent}");
            }

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de la réponse rapide");
            Console.WriteLine($"[ChatEcommerceService] ❌ Exception: {ex.Message}");
            return 0;
        }
    }

    public async Task<bool> UpdateQuickReplyAsync(int id, CreateQuickReplyDto quickReply)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient("ChatEcommerceApi");
            await SetAuthorizationHeaderAsync(httpClient);

            // Convertir la liste de tags en chaîne JSON pour l'API
            var apiDto = new
            {
                title = quickReply.Title,
                content = quickReply.Content,
                category = quickReply.Category,
                tags = quickReply.Tags != null && quickReply.Tags.Any()
                    ? JsonSerializer.Serialize(quickReply.Tags)
                    : null,
                isActive = true,
                sortOrder = 0
            };

            var json = JsonSerializer.Serialize(apiDto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await httpClient.PutAsync($"support/quick-replies/{id}", content);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Réponse rapide modifiée avec succès: {Id}", id);
                Console.WriteLine($"[ChatEcommerceService] ✅ Réponse rapide modifiée: ID={id}");
                return true;
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("Erreur HTTP {StatusCode} lors de la modification de la réponse rapide: {Error}",
                    response.StatusCode, errorContent);
                Console.WriteLine($"[ChatEcommerceService] ❌ Erreur {response.StatusCode}: {errorContent}");
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la modification de la réponse rapide");
            Console.WriteLine($"[ChatEcommerceService] ❌ Exception: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> DeleteQuickReplyAsync(int id)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient("ChatEcommerceApi");
            await SetAuthorizationHeaderAsync(httpClient);

            var response = await httpClient.DeleteAsync($"support/quick-replies/{id}");

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Réponse rapide supprimée avec succès: {Id}", id);
                Console.WriteLine($"[ChatEcommerceService] ✅ Réponse rapide supprimée: ID={id}");
                return true;
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("Erreur HTTP {StatusCode} lors de la suppression de la réponse rapide: {Error}",
                    response.StatusCode, errorContent);
                Console.WriteLine($"[ChatEcommerceService] ❌ Erreur {response.StatusCode}: {errorContent}");
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de la réponse rapide");
            Console.WriteLine($"[ChatEcommerceService] ❌ Exception: {ex.Message}");
            return false;
        }
    }

    // Conversation Methods
    public async Task<List<ConversationDto>> GetConversationsAsync(string sellerId)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient("ChatEcommerceApi");
            await SetAuthorizationHeaderAsync(httpClient);

            var response = await httpClient.GetAsync($"conversations/seller/{sellerId}");
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadAsStringAsync();
            var conversations = JsonSerializer.Deserialize<List<ConversationDto>>(json, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return conversations ?? new List<ConversationDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des conversations");
            return new List<ConversationDto>();
        }
    }

    public async Task<ConversationDto?> GetConversationAsync(int conversationId)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient("ChatEcommerceApi");
            await SetAuthorizationHeaderAsync(httpClient);

            var response = await httpClient.GetAsync($"conversations/{conversationId}");
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadAsStringAsync();
            var conversation = JsonSerializer.Deserialize<ConversationDto>(json, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return conversation;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de la conversation {ConversationId}", conversationId);
            return null;
        }
    }

    public async Task<List<MessageDto>> GetMessagesAsync(int conversationId)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient("ChatEcommerceApi");
            await SetAuthorizationHeaderAsync(httpClient);

            var response = await httpClient.GetAsync($"messages/conversation/{conversationId}");
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadAsStringAsync();
            var messages = JsonSerializer.Deserialize<List<MessageDto>>(json, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return messages ?? new List<MessageDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des messages");
            return new List<MessageDto>();
        }
    }

    public async Task<bool> SendMessageAsync(int conversationId, SendMessageDto message)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient("ChatEcommerceApi");
            await SetAuthorizationHeaderAsync(httpClient);

            var json = JsonSerializer.Serialize(message);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await httpClient.PostAsync("messages", content);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi du message");
            return false;
        }
    }

    public async Task<ConversationDto?> GetConversationByIdAsync(int conversationId)
    {
        return await GetConversationAsync(conversationId);
    }

    public async Task<bool> SendMessageAsync(SendMessageDto message)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient("ChatEcommerceApi");
            await SetAuthorizationHeaderAsync(httpClient);

            var json = JsonSerializer.Serialize(message);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await httpClient.PostAsync("messages", content);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi du message");
            return false;
        }
    }

    public async Task<bool> MarkMessagesAsReadAsync(int conversationId)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient("ChatEcommerceApi");
            await SetAuthorizationHeaderAsync(httpClient);

            // Récupérer l'ID de l'utilisateur depuis le token
            var userId = await GetUserIdFromTokenAsync();
            var dto = new { UserId = userId };
            var json = JsonSerializer.Serialize(dto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await httpClient.PutAsync($"conversations/{conversationId}/read", content);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du marquage des messages comme lus");
            return false;
        }
    }

    public async Task<bool> UpdateConversationStatusAsync(int conversationId, string status)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient("ChatEcommerceApi");
            await SetAuthorizationHeaderAsync(httpClient);

            var dto = new { Status = status };
            var json = JsonSerializer.Serialize(dto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await httpClient.PutAsync($"conversations/{conversationId}/status", content);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour du statut de la conversation");
            return false;
        }
    }

    private async Task<string> GetUserIdFromTokenAsync()
    {
        try
        {
            var token = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", "authToken");
            if (string.IsNullOrEmpty(token))
                return string.Empty;

            // Décoder le JWT pour extraire l'ID utilisateur
            var handler = new System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler();
            var jwtToken = handler.ReadJwtToken(token);
            var userId = jwtToken.Claims.FirstOrDefault(c => c.Type == "sub" || c.Type == "nameid")?.Value;
            return userId ?? string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de l'ID utilisateur depuis le token");
            return string.Empty;
        }
    }
}

// DTOs
public class FAQDto
{
    public int Id { get; set; }
    public string Question { get; set; } = string.Empty;
    public string Answer { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public int ViewCount { get; set; }
    public int HelpfulCount { get; set; }
    public int NotHelpfulCount { get; set; }
    public int SortOrder { get; set; }
    public List<string> Tags { get; set; } = new();
}

public class QuickReplyDto
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public int SortOrder { get; set; }
    public List<string> Tags { get; set; } = new();
}

public class CreateQuickReplyDto
{
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public List<string> Tags { get; set; } = new();
}

public class ConversationDto
{
    public int Id { get; set; }
    public string CustomerId { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public string? CustomerEmail { get; set; }
    public string? SellerId { get; set; }
    public string? SellerName { get; set; }
    public int? ProductId { get; set; }
    public string? ProductName { get; set; }
    public int? OrderId { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public DateTime? LastMessageAt { get; set; }
    public bool IsCustomerOnline { get; set; }
    public bool IsSellerOnline { get; set; }
    public int UnreadCount { get; set; }
    public List<string> Tags { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class MessageDto
{
    public int Id { get; set; }
    public int ConversationId { get; set; }
    public string SenderId { get; set; } = string.Empty;
    public string SenderName { get; set; } = string.Empty;
    public string SenderType { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string MessageType { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public bool IsRead { get; set; }
    public DateTime? ReadAt { get; set; }
    public bool IsEdited { get; set; }
    public DateTime? EditedAt { get; set; }
    public string? AttachmentUrl { get; set; }
    public string? AttachmentType { get; set; }
    public long? AttachmentSize { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class MessageAttachmentDto
{
    public string FileName { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public string FileType { get; set; } = string.Empty;
    public long FileSize { get; set; }
}

public class SendMessageDto
{
    public int ConversationId { get; set; }
    public string SenderId { get; set; } = string.Empty;
    public string SenderName { get; set; } = string.Empty;
    public string SenderType { get; set; } = "Seller";
    public string Content { get; set; } = string.Empty;
    public string MessageType { get; set; } = "Text";
    public string? AttachmentUrl { get; set; }
    public string? AttachmentType { get; set; }
    public long? AttachmentSize { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}
