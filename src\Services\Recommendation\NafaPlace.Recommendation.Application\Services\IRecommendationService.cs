using NafaPlace.Recommendation.Application.DTOs;

namespace NafaPlace.Recommendation.Application.Services;

public interface IRecommendationService
{
    // Recommandations principales
    Task<RecommendationResponseDto> GetPersonalizedRecommendationsAsync(RecommendationRequestDto request);
    Task<RecommendationResponseDto> GetSimilarProductsAsync(int productId, string? userId = null, int limit = 10);
    Task<RecommendationResponseDto> GetFrequentlyBoughtTogetherAsync(int productId, string? userId = null, int limit = 5);
    Task<RecommendationResponseDto> GetTrendingProductsAsync(string? userId = null, int limit = 10);
    Task<RecommendationResponseDto> GetBestSellersAsync(int? categoryId = null, int limit = 10);
    Task<RecommendationResponseDto> GetNewArrivalsAsync(int? categoryId = null, int limit = 10);
    Task<RecommendationResponseDto> GetRecentlyViewedAsync(string userId, int limit = 10);
    
    // Recommandations contextuelles
    Task<RecommendationResponseDto> GetCategoryBasedRecommendationsAsync(int categoryId, string? userId = null, int limit = 10);
    Task<RecommendationResponseDto> GetBrandBasedRecommendationsAsync(string brand, string? userId = null, int limit = 10);
    Task<RecommendationResponseDto> GetSeasonalRecommendationsAsync(string? userId = null, int limit = 10);
    Task<RecommendationResponseDto> GetPriceDropRecommendationsAsync(string? userId = null, int limit = 10);
    Task<RecommendationResponseDto> GetBackInStockRecommendationsAsync(string userId, int limit = 10);
    Task<RecommendationResponseDto> GetAbandonedCartRecommendationsAsync(string userId, int limit = 5);
    Task<RecommendationResponseDto> GetWishlistBasedRecommendationsAsync(string userId, int limit = 10);
    
    // Cross-sell et Up-sell
    Task<RecommendationResponseDto> GetCrossSellRecommendationsAsync(List<int> productIds, string? userId = null, int limit = 5);
    Task<RecommendationResponseDto> GetUpSellRecommendationsAsync(int productId, string? userId = null, int limit = 3);
    Task<RecommendationResponseDto> GetComplementaryProductsAsync(int productId, string? userId = null, int limit = 5);
    Task<RecommendationResponseDto> GetAlternativeProductsAsync(int productId, string? userId = null, int limit = 5);
    
    // Gestion des interactions utilisateur
    Task<bool> RecordUserInteractionAsync(UserInteractionDto interaction);
    Task<bool> RecordBulkInteractionsAsync(List<UserInteractionDto> interactions);
    Task<List<UserInteractionDto>> GetUserInteractionsAsync(string userId, InteractionType? type = null, int limit = 100);
    Task<bool> UpdateInteractionWeightAsync(int interactionId, double newWeight);
    Task<int> CleanupOldInteractionsAsync(int daysToKeep = 90);
    
    // Préférences utilisateur
    Task<UserPreferenceDto> GetUserPreferencesAsync(string userId);
    Task<bool> UpdateUserPreferencesAsync(UserPreferenceDto preferences);
    Task<bool> RecalculateUserPreferencesAsync(string userId);
    Task<List<SimilarProductDto>> GetSimilarUsersAsync(string userId, int limit = 10);
    Task<PersonalizationProfileDto> GetPersonalizationProfileAsync(string userId);
    Task<bool> UpdatePersonalizationProfileAsync(PersonalizationProfileDto profile);
    
    // Modèles de recommandation
    Task<int> CreateRecommendationModelAsync(RecommendationModelDto model);
    Task<List<RecommendationModelDto>> GetRecommendationModelsAsync();
    Task<RecommendationModelDto?> GetRecommendationModelAsync(int modelId);
    Task<bool> UpdateRecommendationModelAsync(RecommendationModelDto model);
    Task<bool> DeleteRecommendationModelAsync(int modelId);
    Task<bool> TrainModelAsync(int modelId, Dictionary<string, object>? parameters = null);
    Task<bool> DeployModelAsync(int modelId);
    Task<Dictionary<string, object>> EvaluateModelAsync(int modelId);
    
    // Campagnes de recommandation
    Task<int> CreateRecommendationCampaignAsync(RecommendationCampaignDto campaign);
    Task<List<RecommendationCampaignDto>> GetRecommendationCampaignsAsync(bool? isActive = null);
    Task<RecommendationCampaignDto?> GetRecommendationCampaignAsync(int campaignId);
    Task<bool> UpdateRecommendationCampaignAsync(RecommendationCampaignDto campaign);
    Task<bool> DeleteRecommendationCampaignAsync(int campaignId);
    Task<bool> ActivateCampaignAsync(int campaignId);
    Task<bool> DeactivateCampaignAsync(int campaignId);
    Task<RecommendationCampaignStats> GetCampaignStatsAsync(int campaignId);
    
    // A/B Testing
    Task<int> CreateABTestAsync(ABTestRecommendationDto abTest);
    Task<List<ABTestRecommendationDto>> GetABTestsAsync(ABTestStatus? status = null);
    Task<ABTestRecommendationDto?> GetABTestAsync(int abTestId);
    Task<bool> UpdateABTestAsync(ABTestRecommendationDto abTest);
    Task<bool> StartABTestAsync(int abTestId);
    Task<bool> StopABTestAsync(int abTestId);
    Task<ABTestResultsDto> GetABTestResultsAsync(int abTestId);
    Task<string?> GetWinningVariantAsync(int abTestId);
    
    // Analytics et métriques
    Task<RecommendationAnalyticsDto> GetRecommendationAnalyticsAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<Dictionary<string, object>> GetAlgorithmPerformanceAsync(string algorithm, DateTime? startDate = null, DateTime? endDate = null);
    Task<Dictionary<string, double>> GetRecommendationAccuracyAsync(RecommendationType type, DateTime? startDate = null, DateTime? endDate = null);
    Task<List<TopPerformingProductDto>> GetTopPerformingProductsAsync(int limit = 20, DateTime? startDate = null, DateTime? endDate = null);
    Task<Dictionary<string, object>> GetUserEngagementMetricsAsync(string userId);
    Task<Dictionary<string, object>> GetRecommendationCoverageAsync();
    Task<Dictionary<string, object>> GetDiversityMetricsAsync();
    
    // Recherche et filtrage
    Task<List<ProductRecommendationDto>> SearchRecommendationsAsync(string query, string? userId = null, int limit = 20);
    Task<RecommendationResponseDto> GetFilteredRecommendationsAsync(RecommendationRequestDto request, Dictionary<string, object> filters);
    Task<List<ProductRecommendationDto>> GetRecommendationsByTagsAsync(List<string> tags, string? userId = null, int limit = 10);
    Task<RecommendationResponseDto> GetLocationBasedRecommendationsAsync(string userId, string location, int limit = 10);
    Task<RecommendationResponseDto> GetTimeBasedRecommendationsAsync(string userId, DateTime targetTime, int limit = 10);
    
    // Optimisation et machine learning
    Task<bool> OptimizeRecommendationParametersAsync(RecommendationType type);
    Task<Dictionary<string, object>> GetFeatureImportanceAsync(int modelId);
    Task<bool> UpdateModelWeightsAsync(int modelId, Dictionary<string, double> weights);
    Task<List<string>> GetRecommendationExplanationsAsync(string userId, int productId);
    Task<double> CalculateRecommendationConfidenceAsync(string userId, int productId);
    Task<bool> FeedbackLearningAsync(string userId, int productId, bool wasRelevant);
    
    // Intégrations et synchronisation
    Task<bool> SyncUserDataAsync(string userId);
    Task<bool> SyncProductDataAsync(int productId);
    Task<bool> ImportUserInteractionsAsync(List<UserInteractionDto> interactions);
    Task<bool> ExportRecommendationDataAsync(DateTime startDate, DateTime endDate, string format = "json");
    Task<bool> SyncWithExternalSystemAsync(string systemId, Dictionary<string, object> config);
    
    // Configuration et maintenance
    Task<Dictionary<string, object>> GetRecommendationConfigAsync();
    Task<bool> UpdateRecommendationConfigAsync(Dictionary<string, object> config);
    Task<bool> RefreshRecommendationCacheAsync(string? userId = null);
    Task<bool> RebuildRecommendationIndexAsync();
    Task<Dictionary<string, bool>> GetServiceHealthAsync();
    Task<bool> TestRecommendationServiceAsync();
    
    // Recommandations en temps réel
    Task<RecommendationResponseDto> GetRealTimeRecommendationsAsync(string userId, string context, Dictionary<string, object>? sessionData = null);
    Task<bool> UpdateRealTimeContextAsync(string userId, Dictionary<string, object> context);
    Task<List<ProductRecommendationDto>> GetInstantRecommendationsAsync(string userId, List<int> currentProductIds, int limit = 5);
    Task<RecommendationResponseDto> GetStreamingRecommendationsAsync(string userId, string streamId);
    
    // Recommandations sociales
    Task<RecommendationResponseDto> GetSocialRecommendationsAsync(string userId, int limit = 10);
    Task<RecommendationResponseDto> GetFriendsRecommendationsAsync(string userId, List<string> friendIds, int limit = 10);
    Task<RecommendationResponseDto> GetInfluencerRecommendationsAsync(string userId, List<string> influencerIds, int limit = 10);
    Task<List<ProductRecommendationDto>> GetTrendingInNetworkAsync(string userId, int limit = 10);
    
    // Recommandations par contenu
    Task<RecommendationResponseDto> GetContentBasedRecommendationsAsync(int productId, string? userId = null, int limit = 10);
    Task<List<SimilarProductDto>> GetProductSimilarityAsync(int productId, SimilarityType similarityType, int limit = 10);
    Task<double> CalculateProductSimilarityAsync(int productId1, int productId2, SimilarityType similarityType);
    Task<bool> UpdateProductFeaturesAsync(int productId, Dictionary<string, object> features);
    Task<Dictionary<string, double>> GetProductFeatureVectorAsync(int productId);
    
    // Recommandations collaboratives
    Task<RecommendationResponseDto> GetCollaborativeFilteringRecommendationsAsync(string userId, int limit = 10);
    Task<List<string>> GetSimilarUsersAsync(string userId, double similarityThreshold = 0.5, int limit = 20);
    Task<double> CalculateUserSimilarityAsync(string userId1, string userId2);
    Task<Dictionary<int, double>> GetUserProductRatingsAsync(string userId);
    Task<bool> UpdateUserRatingAsync(string userId, int productId, double rating);
    
    // Recommandations hybrides
    Task<RecommendationResponseDto> GetHybridRecommendationsAsync(string userId, Dictionary<string, double> algorithmWeights, int limit = 10);
    Task<Dictionary<string, double>> OptimizeHybridWeightsAsync(string userId);
    Task<RecommendationResponseDto> GetEnsembleRecommendationsAsync(string userId, List<string> algorithms, int limit = 10);
    Task<bool> CalibrateRecommendationScoresAsync(RecommendationType type);
    
    // Explications et transparence
    Task<List<string>> GetRecommendationReasonsAsync(string userId, int productId);
    Task<Dictionary<string, object>> GetRecommendationMetadataAsync(string requestId);
    Task<bool> LogRecommendationDecisionAsync(string userId, int productId, Dictionary<string, object> decisionFactors);
    Task<List<string>> GetAlgorithmExplanationAsync(string algorithm, Dictionary<string, object> parameters);
    
    // Gestion des biais et équité
    Task<Dictionary<string, object>> DetectRecommendationBiasAsync(string userId);
    Task<bool> ApplyFairnessConstraintsAsync(RecommendationType type, Dictionary<string, object> constraints);
    Task<RecommendationResponseDto> GetDiversifiedRecommendationsAsync(string userId, double diversityWeight = 0.3, int limit = 10);
    Task<Dictionary<string, double>> CalculateRecommendationDiversityAsync(List<ProductRecommendationDto> recommendations);
    
    // Recommandations contextuelles avancées
    Task<RecommendationResponseDto> GetWeatherBasedRecommendationsAsync(string userId, string weatherCondition, int limit = 10);
    Task<RecommendationResponseDto> GetEventBasedRecommendationsAsync(string userId, string eventType, int limit = 10);
    Task<RecommendationResponseDto> GetMoodBasedRecommendationsAsync(string userId, string mood, int limit = 10);
    Task<RecommendationResponseDto> GetOccasionBasedRecommendationsAsync(string userId, string occasion, int limit = 10);
}
