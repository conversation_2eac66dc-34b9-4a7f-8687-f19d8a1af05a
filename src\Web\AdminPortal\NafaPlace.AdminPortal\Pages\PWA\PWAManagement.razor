@page "/pwa/management"
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.JSInterop
@inject IJSRuntime JSRuntime
@inject ILogger<PWAManagement> Logger
@attribute [Authorize(Roles = "Admin")]

<PageTitle>Gestion PWA - Admin</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-mobile text-primary me-2"></i>
                        Gestion Progressive Web App (PWA)
                    </h1>
                    <p class="text-muted mb-0">Configuration et statistiques de l'application web progressive</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" @onclick="TestPWAFeatures">
                        <i class="fas fa-flask me-2"></i>Tester PWA
                    </button>
                    <button class="btn btn-primary" @onclick="UpdatePWAConfig">
                        <i class="fas fa-sync-alt me-2"></i>Mettre à jour
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques PWA -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                                <i class="fas fa-download text-primary fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Installations</h6>
                            <h4 class="mb-0">@totalInstalls.ToString("N0")</h4>
                            <small class="text-success">
                                <i class="fas fa-arrow-up me-1"></i>+@installGrowth.ToString("F1")%
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-opacity-10 rounded-3 p-3">
                                <i class="fas fa-users text-success fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Utilisateurs Actifs</h6>
                            <h4 class="mb-0">@activeUsers.ToString("N0")</h4>
                            <small class="text-success">
                                <i class="fas fa-arrow-up me-1"></i>+@userGrowth.ToString("F1")%
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-opacity-10 rounded-3 p-3">
                                <i class="fas fa-bell text-info fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Notifications Autorisées</h6>
                            <h4 class="mb-0">@notificationPermissions.ToString("N0")</h4>
                            <small class="text-muted">
                                @Math.Round((decimal)notificationPermissions / totalInstalls * 100, 1)% du total
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                                <i class="fas fa-clock text-warning fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Temps Moyen Session</h6>
                            <h4 class="mb-0">@avgSessionTime</h4>
                            <small class="text-success">
                                <i class="fas fa-arrow-up me-1"></i>+@sessionGrowth.ToString("F1")%
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <!-- Configuration PWA -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>Configuration PWA
                    </h5>
                </div>
                <div class="card-body">
                    <EditForm Model="pwaConfig" OnValidSubmit="SavePWAConfig">
                        <div class="mb-3">
                            <label class="form-label">Nom de l'application</label>
                            <InputText @bind-Value="pwaConfig.Name" class="form-control" />
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Nom court</label>
                            <InputText @bind-Value="pwaConfig.ShortName" class="form-control" />
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <InputTextArea @bind-Value="pwaConfig.Description" class="form-control" rows="3" />
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Couleur de thème</label>
                            <InputText @bind-Value="pwaConfig.ThemeColor" class="form-control" type="color" />
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Couleur d'arrière-plan</label>
                            <InputText @bind-Value="pwaConfig.BackgroundColor" class="form-control" type="color" />
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Mode d'affichage</label>
                            <InputSelect @bind-Value="pwaConfig.Display" class="form-select">
                                <option value="standalone">Standalone</option>
                                <option value="fullscreen">Plein écran</option>
                                <option value="minimal-ui">Interface minimale</option>
                                <option value="browser">Navigateur</option>
                            </InputSelect>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Orientation</label>
                            <InputSelect @bind-Value="pwaConfig.Orientation" class="form-select">
                                <option value="any">Toute orientation</option>
                                <option value="portrait">Portrait</option>
                                <option value="landscape">Paysage</option>
                            </InputSelect>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Sauvegarder
                            </button>
                            <button type="button" class="btn btn-outline-secondary" @onclick="ResetConfig">
                                <i class="fas fa-undo me-2"></i>Réinitialiser
                            </button>
                        </div>
                    </EditForm>
                </div>
            </div>
        </div>

        <!-- Fonctionnalités PWA -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list-check me-2"></i>Fonctionnalités PWA
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        @foreach (var feature in pwaFeatures)
                        {
                            <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <div class="d-flex align-items-center">
                                    <i class="fas @feature.Icon text-@(feature.IsEnabled ? "success" : "muted") me-3"></i>
                                    <div>
                                        <h6 class="mb-1">@feature.Name</h6>
                                        <small class="text-muted">@feature.Description</small>
                                    </div>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" @bind="feature.IsEnabled" @bind:after="() => ToggleFeature(feature)">
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Graphiques et Analytics -->
    <div class="row g-4 mt-4">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>Installations PWA
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="installChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-mobile-alt me-2"></i>Plateformes
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="platformChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Logs et Événements -->
    <div class="row g-4 mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>Événements PWA Récents
                        </h5>
                        <button class="btn btn-outline-primary btn-sm" @onclick="RefreshEvents">
                            <i class="fas fa-sync-alt me-2"></i>Actualiser
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    @if (pwaEvents?.Any() == true)
                    {
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Événement</th>
                                        <th>Utilisateur</th>
                                        <th>Plateforme</th>
                                        <th>Date</th>
                                        <th>Détails</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var evt in pwaEvents.Take(10))
                                    {
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="fas @GetEventIcon(evt.Type) text-@GetEventColor(evt.Type) me-2"></i>
                                                    <span>@evt.Type</span>
                                                </div>
                                            </td>
                                            <td>@evt.UserId</td>
                                            <td>
                                                <span class="badge bg-light text-dark">@evt.Platform</span>
                                            </td>
                                            <td>@evt.Timestamp.ToString("dd/MM/yyyy HH:mm")</td>
                                            <td>
                                                <small class="text-muted">@evt.Details</small>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fs-1 text-muted mb-3"></i>
                            <p class="text-muted">Aucun événement récent</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    // Statistiques
    private int totalInstalls = 1_234;
    private int activeUsers = 856;
    private int notificationPermissions = 678;
    private string avgSessionTime = "8m 32s";
    private decimal installGrowth = 23.5m;
    private decimal userGrowth = 18.2m;
    private decimal sessionGrowth = 12.7m;

    // Configuration
    private PWAConfigDto pwaConfig = new();
    private List<PWAFeatureDto> pwaFeatures = new();
    private List<PWAEventDto> pwaEvents = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadPWAData();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await InitializeCharts();
        }
    }

    private async Task LoadPWAData()
    {
        try
        {
            // Configuration PWA par défaut
            pwaConfig = new PWAConfigDto
            {
                Name = "NafaPlace - Marketplace Guinéen",
                ShortName = "NafaPlace",
                Description = "La première marketplace de Guinée",
                ThemeColor = "#003366",
                BackgroundColor = "#ffffff",
                Display = "standalone",
                Orientation = "any"
            };

            // Fonctionnalités PWA
            pwaFeatures = new List<PWAFeatureDto>
            {
                new() { Name = "Installation", Description = "Permettre l'installation de l'app", Icon = "fa-download", IsEnabled = true },
                new() { Name = "Notifications Push", Description = "Notifications en temps réel", Icon = "fa-bell", IsEnabled = true },
                new() { Name = "Mode Hors-ligne", Description = "Fonctionnement sans connexion", Icon = "fa-wifi", IsEnabled = true },
                new() { Name = "Synchronisation", Description = "Sync automatique des données", Icon = "fa-sync", IsEnabled = true },
                new() { Name = "Géolocalisation", Description = "Services basés sur la position", Icon = "fa-map-marker-alt", IsEnabled = false },
                new() { Name = "Caméra", Description = "Accès à l'appareil photo", Icon = "fa-camera", IsEnabled = true },
                new() { Name = "Partage", Description = "Partage natif du contenu", Icon = "fa-share-alt", IsEnabled = true }
            };

            // Événements récents
            pwaEvents = new List<PWAEventDto>
            {
                new() { Type = "Installation", UserId = "user123", Platform = "Android", Timestamp = DateTime.Now.AddMinutes(-15), Details = "Chrome 120" },
                new() { Type = "Notification", UserId = "user456", Platform = "iOS", Timestamp = DateTime.Now.AddMinutes(-32), Details = "Permission accordée" },
                new() { Type = "Offline", UserId = "user789", Platform = "Windows", Timestamp = DateTime.Now.AddHours(-1), Details = "Mode hors-ligne activé" },
                new() { Type = "Update", UserId = "user321", Platform = "Android", Timestamp = DateTime.Now.AddHours(-2), Details = "Mise à jour v2.1.0" }
            };
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du chargement des données PWA");
        }
    }

    private async Task SavePWAConfig()
    {
        try
        {
            // Sauvegarder la configuration
            await JSRuntime.InvokeVoidAsync("alert", "Configuration PWA sauvegardée avec succès!");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de la sauvegarde");
            await JSRuntime.InvokeVoidAsync("alert", "Erreur lors de la sauvegarde");
        }
    }

    private void ResetConfig()
    {
        pwaConfig = new PWAConfigDto
        {
            Name = "NafaPlace - Marketplace Guinéen",
            ShortName = "NafaPlace",
            Description = "La première marketplace de Guinée",
            ThemeColor = "#003366",
            BackgroundColor = "#ffffff",
            Display = "standalone",
            Orientation = "any"
        };
        StateHasChanged();
    }

    private async Task ToggleFeature(PWAFeatureDto feature)
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Fonctionnalité '{feature.Name}' " + (feature.IsEnabled ? "activée" : "désactivée"));
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du toggle de la fonctionnalité");
        }
    }

    private async Task TestPWAFeatures()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Test des fonctionnalités PWA en cours...");
    }

    private async Task UpdatePWAConfig()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Mise à jour de la configuration PWA...");
        await LoadPWAData();
    }

    private async Task RefreshEvents()
    {
        await LoadPWAData();
        StateHasChanged();
    }

    private async Task InitializeCharts()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("initializePWACharts");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'initialisation des graphiques");
        }
    }

    private string GetEventIcon(string type)
    {
        return type switch
        {
            "Installation" => "fa-download",
            "Notification" => "fa-bell",
            "Offline" => "fa-wifi",
            "Update" => "fa-sync-alt",
            _ => "fa-info-circle"
        };
    }

    private string GetEventColor(string type)
    {
        return type switch
        {
            "Installation" => "success",
            "Notification" => "info",
            "Offline" => "warning",
            "Update" => "primary",
            _ => "secondary"
        };
    }

    // Classes DTO
    public class PWAConfigDto
    {
        public string Name { get; set; } = "";
        public string ShortName { get; set; } = "";
        public string Description { get; set; } = "";
        public string ThemeColor { get; set; } = "#000000";
        public string BackgroundColor { get; set; } = "#ffffff";
        public string Display { get; set; } = "standalone";
        public string Orientation { get; set; } = "any";
    }

    public class PWAFeatureDto
    {
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public string Icon { get; set; } = "";
        public bool IsEnabled { get; set; }
    }

    public class PWAEventDto
    {
        public string Type { get; set; } = "";
        public string UserId { get; set; } = "";
        public string Platform { get; set; } = "";
        public DateTime Timestamp { get; set; }
        public string Details { get; set; } = "";
    }
}
