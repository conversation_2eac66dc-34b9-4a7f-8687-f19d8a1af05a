@page "/catalog"
@using NafaPlace.Web.Services
@using NafaPlace.Web.Models.Catalog
@using NafaPlace.Web.Components.Reviews
@inject IProductService ProductService
@inject ICategoryService CategoryService
@inject ICartService CartService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@using System.Security.Claims
@inject AuthenticationStateProvider AuthenticationStateProvider

<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Accueil</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Catalogue</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <!-- Filtres -->
        <div class="col-lg-3 mb-4">
            <!-- Bouton pour afficher les filtres sur mobile -->
            <div class="d-lg-none mb-3">
                <button class="btn btn-outline-primary w-100" type="button" data-bs-toggle="collapse" data-bs-target="#filtersCollapse" aria-expanded="false" aria-controls="filtersCollapse">
                    <i class="bi bi-funnel me-2"></i>Filtres
                </button>
            </div>

            <div class="collapse d-lg-block" id="filtersCollapse">
                <div class="card">
                    <div class="card-header bg-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Filtres</h5>
                        <button class="btn btn-sm btn-outline-secondary" @onclick="ResetFilters">
                            <i class="bi bi-arrow-clockwise me-1"></i>Reset
                        </button>
                    </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Catégories</h6>
                        @if (isLoading)
                        {
                            <div class="d-flex justify-content-center">
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">Chargement...</span>
                                </div>
                            </div>
                        }
                        else if (categories != null && categories.Any())
                        {
                            @foreach (var category in categories)
                            {
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" value="@category.Id"
                                           id="<EMAIL>" checked="@selectedCategories.Contains(category.Id)"
                                           @onchange="@((ChangeEventArgs e) => CategoryFilterChanged(category.Id, e.Value))">
                                    <label class="form-check-label" for="<EMAIL>">
                                        @category.Name
                                    </label>
                                </div>
                            }
                        }
                        else
                        {
                            <p>Aucune catégorie disponible</p>
                        }
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Prix</h6>
                        <div class="row g-2">
                            <div class="col-6">
                                <input type="number" class="form-control" placeholder="Min" @bind="minPrice">
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control" placeholder="Max" @bind="maxPrice">
                            </div>
                        </div>
                        <button class="btn btn-outline-primary w-100 mt-2" @onclick="ApplyPriceFilter">Appliquer</button>
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Disponibilité</h6>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" value="" id="inStock"
                                   @bind="inStockOnly" @bind:after="OnInStockFilterChanged">
                            <label class="form-check-label" for="inStock">
                                En stock uniquement
                            </label>
                        </div>
                    </div>

                    <button class="btn btn-primary w-100" @onclick="ApplyFilters">
                        <i class="bi bi-funnel-fill me-1"></i> Filtrer
                    </button>
                </div>
            </div>
            </div>
        </div>

        <!-- Produits -->
        <div class="col-lg-9">
            <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-3 mb-md-4">
                <h2 class="mb-2 mb-md-0">Catalogue de Produits</h2>
                <div class="d-flex align-items-center w-100 w-md-auto">
                    <label class="me-2 d-none d-md-block">Trier par:</label>
                    <select class="form-select form-select-sm" @bind="sortOrder" @bind:after="OnSortChanged">
                        <option value="createdat_desc">Plus récents</option>
                        <option value="price_asc">Prix croissant</option>
                        <option value="price_desc">Prix décroissant</option>
                        <option value="name_asc">Nom (A-Z)</option>
                        <option value="name_desc">Nom (Z-A)</option>
                    </select>
                </div>
            </div>

            @if (isLoading)
            {
                <div class="d-flex justify-content-center my-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            }
            else if (searchResponse?.Products != null && searchResponse.Products.Any())
            {
                <div class="row row-cols-1 row-cols-sm-2 row-cols-md-4 g-3 g-md-4">
                    @foreach (var product in GetProductsToShow(searchResponse.Products, 12))
                    {
                        <div class="col">
                            <div class="card h-100 product-card-mobile @(product.Stock <= 0 ? "product-out-of-stock" : "")">
                                <div class="position-relative">
                                    @if (product.Images.Any())
                                    {
                                        <a href="/catalog/products/@product.Id">
                                            <img src="@ProductService.GetImageUrl(product.Images.First())" class="card-img-top" alt="@product.Name">
                                        </a>
                                    }
                                    else
                                    {
                                        <a href="/catalog/products/@product.Id">
                                            <img src="/images/no-image.png" class="card-img-top" alt="Image non disponible">
                                        </a>
                                    }

                                    @if (product.Stock <= 0)
                                    {
                                        <div class="product-out-of-stock-overlay">
                                            <div class="product-out-of-stock-text">
                                                RUPTURE DE STOCK
                                            </div>
                                        </div>
                                    }
                                </div>
                                <div class="card-body">
                                    <h5 class="card-title"><a href="/catalog/products/@product.Id" class="text-decoration-none text-dark">@product.Name</a></h5>
                                    <p class="card-text text-muted small">@product.Category?.Name</p>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="text-primary fw-bold product-price-mobile">@product.Price.ToString("N0") @product.Currency</span>
                                        <div class="ratings ratings-mobile">
                                            <StarRating Rating="product.Rating" ShowRatingText="false" CssClass="small" />
                                            <span class="text-muted ms-1" style="font-size: 0.8rem;">(@product.ReviewCount)</span>
                                        </div>
                                    </div>
                                    <div class="d-grid gap-2 product-actions-mobile">
                                        <a href="/catalog/products/@product.Id" class="btn btn-outline-primary btn-sm">Voir détails</a>
                                        <button class="btn btn-primary btn-sm" @onclick="() => AddToCart(product)" disabled="@(product.Stock <= 0)">
                                            <i class="bi bi-cart-plus"></i> Ajouter au panier
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>

                <!-- Pagination -->
                @if (searchResponse.TotalPages > 1)
                {
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center pagination-mobile">
                            <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                                <a class="page-link" href="javascript:void(0);" @onclick="() => ChangePage(currentPage - 1)" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            
                            @for (int i = 1; i <= searchResponse.TotalPages; i++)
                            {
                                var pageNumber = i;
                                <li class="page-item @(currentPage == pageNumber ? "active" : "")">
                                    <a class="page-link" href="javascript:void(0);" @onclick="() => ChangePage(pageNumber)">@pageNumber</a>
                                </li>
                            }
                            
                            <li class="page-item @(currentPage == searchResponse.TotalPages ? "disabled" : "")">
                                <a class="page-link" href="javascript:void(0);" @onclick="() => ChangePage(currentPage + 1)" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                }
            }
            else if (searchResponse == null || searchResponse.Products == null || !searchResponse.Products.Any())
            {
                <p class="text-center">Chargement des produits...</p>
            }
            else
            {
                <div class="alert alert-info" role="alert">
                    Aucun produit ne correspond à vos critères de recherche.
                </div>
            }
        </div>
    </div>
</div>

@code {
    private IEnumerable<CategoryDto> categories = Array.Empty<CategoryDto>();
    private ProductSearchResponse searchResponse = new ProductSearchResponse
    {
        Products = Array.Empty<ProductDto>(),
        TotalItems = 0,
        PageSize = 1
    };
    private List<int> selectedCategories = new List<int>();
    private decimal? minPrice;
    private decimal? maxPrice;
    private bool inStockOnly;
    private string sortOrder = "createdat_desc";
    private int currentPage = 1;
    private int pageSize = 9;
    private bool isLoading = true;

    private string _userId = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        if (user.Identity?.IsAuthenticated == true)
        {
            _userId = user.FindFirst(c => c.Type == ClaimTypes.NameIdentifier)?.Value ?? string.Empty;
            Console.WriteLine($"🔍 DEBUG Index: UserId récupéré = '{_userId}'");

            // Debug de toutes les claims
            Console.WriteLine("🔍 DEBUG Index: Toutes les claims:");
            foreach (var claim in user.Claims)
            {
                Console.WriteLine($"  - {claim.Type}: {claim.Value}");
            }
        }
        else
        {
            Console.WriteLine("❌ DEBUG Index: Utilisateur non authentifié");
        }

        // Récupérer le paramètre categoryId depuis l'URL
        var uri = NavigationManager.ToAbsoluteUri(NavigationManager.Uri);
        var query = uri.Query;
        if (!string.IsNullOrEmpty(query))
        {
            var categoryIdMatch = System.Text.RegularExpressions.Regex.Match(query, @"categoryId=(\d+)");
            if (categoryIdMatch.Success && int.TryParse(categoryIdMatch.Groups[1].Value, out var categoryId))
            {
                selectedCategories.Add(categoryId);
            }
        }

        await LoadCategories();
        await SearchProducts();
    }

    private async Task LoadCategories()
    {
        try
        {
            categories = await CategoryService.GetAllCategoriesAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des catégories: {ex.Message}");
            // Gérer l'erreur
        }
    }

    private async Task SearchProducts()
    {
        try
        {
            isLoading = true;

            var request = new ProductSearchRequest
            {
                CategoryIds = selectedCategories.Any() ? selectedCategories : null,
                MinPrice = minPrice,
                MaxPrice = maxPrice,
                InStockOnly = inStockOnly,
                Page = currentPage,
                PageSize = pageSize
            };

            Console.WriteLine($"🔍 DEBUG SearchProducts: CategoryIds = {(request.CategoryIds != null ? $"[{string.Join(", ", request.CategoryIds)}]" : "null")}");
            Console.WriteLine($"🔍 DEBUG SearchProducts: MinPrice = {request.MinPrice}, MaxPrice = {request.MaxPrice}, InStockOnly = {request.InStockOnly}");

            switch (sortOrder)
            {
                case "price_asc":
                    request.SortBy = "price";
                    request.SortDescending = false;
                    break;
                case "price_desc":
                    request.SortBy = "price";
                    request.SortDescending = true;
                    break;
                case "name_asc":
                    request.SortBy = "name";
                    request.SortDescending = false;
                    break;
                case "name_desc":
                    request.SortBy = "name";
                    request.SortDescending = true;
                    break;
                case "createdat_desc":
                default:
                    request.SortBy = "createdat";
                    request.SortDescending = true;
                    break;
            }

            searchResponse = await ProductService.SearchProductsAsync(request);
            Console.WriteLine($"✅ DEBUG SearchProducts: Résultats = {searchResponse?.Products?.Count() ?? 0} produits");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Erreur lors de la recherche de produits: {ex.Message}");
            Console.WriteLine($"❌ Stack trace: {ex.StackTrace}");
            // Gérer l'erreur
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task CategoryFilterChanged(int categoryId, object isChecked)
    {
        Console.WriteLine($"🔍 DEBUG CategoryFilterChanged: categoryId={categoryId}, isChecked={isChecked}, type={isChecked?.GetType().Name}");

        bool isCheckedBool = false;
        if (isChecked is bool boolValue)
        {
            isCheckedBool = boolValue;
        }
        else if (isChecked != null)
        {
            bool.TryParse(isChecked.ToString(), out isCheckedBool);
        }

        Console.WriteLine($"🔍 DEBUG CategoryFilterChanged: isCheckedBool={isCheckedBool}");

        if (isCheckedBool)
        {
            if (!selectedCategories.Contains(categoryId))
            {
                selectedCategories.Add(categoryId);
                Console.WriteLine($"✅ DEBUG: Catégorie {categoryId} ajoutée. Total: {selectedCategories.Count}");
            }
        }
        else
        {
            selectedCategories.Remove(categoryId);
            Console.WriteLine($"❌ DEBUG: Catégorie {categoryId} retirée. Total: {selectedCategories.Count}");
        }

        Console.WriteLine($"🔍 DEBUG: selectedCategories = [{string.Join(", ", selectedCategories)}]");

        // Déclencher la recherche immédiatement après le changement de catégorie
        currentPage = 1;
        await SearchProducts();
    }

    private async Task ApplyPriceFilter()
    {
        currentPage = 1;
        await SearchProducts();
    }

    private async Task OnInStockFilterChanged()
    {
        currentPage = 1;
        await SearchProducts();
    }

    private async Task ApplyFilters()
    {
        currentPage = 1;
        await SearchProducts();
    }

    private async Task OnSortChanged()
    {
        currentPage = 1;
        await SearchProducts();
    }

    private async Task ResetFilters()
    {
        selectedCategories.Clear();
        minPrice = null;
        maxPrice = null;
        inStockOnly = false;
        sortOrder = "createdat_desc";
        currentPage = 1;
        await SearchProducts();
    }

    private async Task ChangePage(int page)
    {
        if (page < 1 || (searchResponse != null && page > searchResponse.TotalPages))
            return;
            
        currentPage = page;
        await SearchProducts();
    }

    private async Task AddToCart(ProductDto product)
    {
        Console.WriteLine($"🔍 DEBUG: Tentative d'ajout au panier - UserId: {_userId}, ProductId: {product.Id}");

        string userId;
        if (string.IsNullOrEmpty(_userId))
        {
            Console.WriteLine("🔍 DEBUG: Utilisateur non connecté, utilisation d'un ID invité");
            userId = await GetOrCreateGuestUserId();
        }
        else
        {
            userId = _userId;
        }

        if (product.Stock > 0)
        {
            try
            {
                Console.WriteLine($"🛒 DEBUG: Création de l'item panier - ProductId: {product.Id}, Quantity: 1");
                var cartItem = new CartItemCreateDto { ProductId = product.Id, Quantity = 1 };

                Console.WriteLine($"📡 DEBUG: Appel API AddItemToCartAsync...");
                var result = await CartService.AddItemToCartAsync(userId, cartItem);

                Console.WriteLine($"✅ DEBUG: Produit ajouté avec succès - ItemCount: {result?.ItemCount ?? 0}");

                // Notification de succès
                await JSRuntime.InvokeVoidAsync("showToast", $"✅ {product.Name} ajouté au panier !", "success");
                Console.WriteLine($"Produit {product.Name} ajouté au panier.");
            }
            catch (Exception ex)
            {
                // Notification d'erreur
                Console.WriteLine($"❌ DEBUG: Erreur complète: {ex}");
                await JSRuntime.InvokeVoidAsync("showToast", "❌ Erreur lors de l'ajout au panier", "danger");
                Console.WriteLine($"Erreur lors de l'ajout au panier: {ex.Message}");
            }
        }
        else
        {
            Console.WriteLine($"⚠️ DEBUG: Produit en rupture de stock - Stock: {product.Stock}");
            await JSRuntime.InvokeVoidAsync("showToast", "⚠️ Produit en rupture de stock", "warning");
        }
    }

    private async Task<string> GetOrCreateGuestUserId()
    {
        // Utiliser le localStorage pour stocker l'ID de session invité
        var guestId = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "guestUserId");

        if (string.IsNullOrEmpty(guestId))
        {
            guestId = $"guest_{Random.Shared.Next(1, int.MaxValue)}";
            await JSRuntime.InvokeVoidAsync("localStorage.setItem", "guestUserId", guestId);
        }

        return guestId;
    }

    private IEnumerable<ProductDto> GetProductsToShow(IEnumerable<ProductDto> products, int maxCount)
    {
        var allProducts = products.ToList();

        // Si on a 4 produits ou moins, les retourner tous
        if (allProducts.Count <= 4)
        {
            return allProducts;
        }

        // Prendre jusqu'à maxCount produits
        var productsToShow = allProducts.Take(maxCount).ToList();

        // Calculer combien de lignes complètes on peut faire
        var completeRows = productsToShow.Count / 4;
        var targetCount = completeRows * 4;

        // S'assurer qu'on a au moins 4 produits (1 ligne complète)
        if (targetCount < 4 && allProducts.Count >= 4)
        {
            targetCount = 4;
        }

        return allProducts.Take(targetCount);
    }
}
