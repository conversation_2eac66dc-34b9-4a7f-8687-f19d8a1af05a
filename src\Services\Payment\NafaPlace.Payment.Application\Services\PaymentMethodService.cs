using Microsoft.Extensions.Logging;
using NafaPlace.Payment.Domain.Enums;

namespace NafaPlace.Payment.Application.Services;

public class PaymentMethodService : IPaymentMethodService
{
    private readonly ILogger<PaymentMethodService> _logger;

    public PaymentMethodService(ILogger<PaymentMethodService> logger)
    {
        _logger = logger;
    }

    public async Task<List<PaymentMethodDto>> GetPaymentMethodsAsync(string userId)
    {
        _logger.LogInformation("Getting payment methods for user {UserId}", userId);
        return await Task.FromResult(new List<PaymentMethodDto>());
    }

    public async Task<PaymentMethodDto> AddPaymentMethodAsync(string userId, AddPaymentMethodDto request)
    {
        _logger.LogInformation("Adding payment method for user {UserId}", userId);

        return await Task.FromResult(new PaymentMethodDto
        {
            Id = Guid.NewGuid().ToString(),
            UserId = userId,
            Type = request.Type,
            DisplayName = request.DisplayName ?? $"{request.Provider} - {request.PhoneNumber}",
            PhoneNumber = request.PhoneNumber,
            Provider = request.Provider,
            IsDefault = request.SetAsDefault,
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        });
    }

    public async Task<bool> RemovePaymentMethodAsync(string userId, string paymentMethodId)
    {
        _logger.LogInformation("Removing payment method {PaymentMethodId} for user {UserId}", paymentMethodId, userId);
        return await Task.FromResult(true);
    }

    public async Task<PaymentMethodDto> UpdatePaymentMethodAsync(string userId, string paymentMethodId, UpdatePaymentMethodDto request)
    {
        _logger.LogInformation("Updating payment method {PaymentMethodId} for user {UserId}", paymentMethodId, userId);

        return await Task.FromResult(new PaymentMethodDto
        {
            Id = paymentMethodId,
            UserId = userId,
            Type = "Mobile",
            DisplayName = request.DisplayName ?? "Updated Method",
            PhoneNumber = request.PhoneNumber ?? "",
            Provider = MobilePaymentProvider.OrangeMoney,
            IsDefault = false,
            IsActive = request.IsActive ?? true,
            CreatedAt = DateTime.UtcNow.AddDays(-1)
        });
    }

    public async Task<bool> SetDefaultPaymentMethodAsync(string userId, string paymentMethodId)
    {
        _logger.LogInformation("Setting default payment method {PaymentMethodId} for user {UserId}", paymentMethodId, userId);
        return await Task.FromResult(true);
    }

    public async Task<PaymentMethodDto> SaveMobilePaymentMethodAsync(string userId, MobilePaymentMethodDto request)
    {
        _logger.LogInformation("Saving mobile payment method for user {UserId}", userId);

        return await Task.FromResult(new PaymentMethodDto
        {
            Id = Guid.NewGuid().ToString(),
            UserId = userId,
            Type = "Mobile",
            DisplayName = request.DisplayName ?? $"{request.Provider} - {request.PhoneNumber}",
            PhoneNumber = request.PhoneNumber,
            Provider = request.Provider,
            IsDefault = request.SetAsDefault,
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        });
    }

    public async Task<List<PaymentMethodDto>> GetUserPaymentMethodsAsync(string userId)
    {
        return await GetPaymentMethodsAsync(userId);
    }

    public async Task<bool> DeletePaymentMethodAsync(string userId, string paymentMethodId)
    {
        return await RemovePaymentMethodAsync(userId, paymentMethodId);
    }
}
