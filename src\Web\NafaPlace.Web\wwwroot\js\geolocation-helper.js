// Helper de géolocalisation pour l'interopérabilité Blazor/JavaScript
// Gère la communication entre le service C# GeolocationService et l'API JavaScript Geolocation

window.geolocationHelper = {
    dotNetHelper: null,
    watchId: null,
    isWatching: false,

    // Initialiser avec la référence .NET
    initialize: function(dotNetHelper) {
        this.dotNetHelper = dotNetHelper;
        console.log('GeolocationHelper initialisé 📍');
    },

    // Vérifier si la géolocalisation est supportée
    isSupported: function() {
        return 'geolocation' in navigator;
    },

    // Obtenir la position actuelle
    getCurrentPosition: function(timeoutMs = 10000) {
        if (!this.isSupported()) {
            this.handleError('Géolocalisation non supportée par ce navigateur');
            return;
        }

        const options = {
            enableHighAccuracy: true,
            timeout: timeoutMs,
            maximumAge: 60000 // 1 minute
        };

        navigator.geolocation.getCurrentPosition(
            (position) => this.handlePosition(position),
            (error) => this.handlePositionError(error),
            options
        );
    },

    // Démarrer la surveillance de position
    startWatching: function(timeoutMs = 15000) {
        if (!this.isSupported()) {
            this.handleError('Géolocalisation non supportée par ce navigateur');
            return;
        }

        if (this.isWatching) {
            this.stopWatching();
        }

        const options = {
            enableHighAccuracy: true,
            timeout: timeoutMs,
            maximumAge: 30000 // 30 secondes
        };

        this.watchId = navigator.geolocation.watchPosition(
            (position) => this.handlePosition(position),
            (error) => this.handlePositionError(error),
            options
        );

        this.isWatching = true;
        console.log('Surveillance de géolocalisation démarrée');
    },

    // Arrêter la surveillance
    stopWatching: function() {
        if (this.watchId !== null) {
            navigator.geolocation.clearWatch(this.watchId);
            this.watchId = null;
            this.isWatching = false;
            console.log('Surveillance de géolocalisation arrêtée');
        }
    },

    // Gérer une position reçue
    handlePosition: function(position) {
        if (this.dotNetHelper) {
            const coords = position.coords;

            this.dotNetHelper.invokeMethodAsync('OnPositionReceived',
                coords.latitude,
                coords.longitude,
                coords.accuracy || 0,
                coords.speed,
                coords.heading,
                position.timestamp
            ).catch(error => {
                console.error('Erreur lors de l\'envoi de position à .NET:', error);
            });
        }
    },

    // Gérer les erreurs de position
    handlePositionError: function(error) {
        let errorMessage = 'Erreur de géolocalisation inconnue';

        switch (error.code) {
            case error.PERMISSION_DENIED:
                errorMessage = 'Accès à la géolocalisation refusé par l\'utilisateur';
                break;
            case error.POSITION_UNAVAILABLE:
                errorMessage = 'Position non disponible';
                break;
            case error.TIMEOUT:
                errorMessage = 'Timeout lors de l\'obtention de la position';
                break;
        }

        console.warn('Erreur de géolocalisation:', errorMessage);
        this.handleError(errorMessage);
    },

    // Envoyer une erreur à .NET
    handleError: function(errorMessage) {
        if (this.dotNetHelper) {
            this.dotNetHelper.invokeMethodAsync('OnPositionError', errorMessage)
                .catch(error => {
                    console.error('Erreur lors de l\'envoi d\'erreur à .NET:', error);
                });
        }
    },

    // Demander la permission de géolocalisation explicitement
    requestPermission: async function() {
        if (!this.isSupported()) {
            return { success: false, error: 'Géolocalisation non supportée' };
        }

        try {
            // Tenter d'obtenir la position pour déclencher la demande de permission
            const position = await new Promise((resolve, reject) => {
                navigator.geolocation.getCurrentPosition(resolve, reject, {
                    enableHighAccuracy: false,
                    timeout: 5000,
                    maximumAge: 300000 // 5 minutes
                });
            });

            return { success: true, position: position };
        } catch (error) {
            let errorMessage = 'Permission refusée';

            if (error.code === 1) {
                errorMessage = 'Permission de géolocalisation refusée';
            } else if (error.code === 2) {
                errorMessage = 'Position non disponible';
            } else if (error.code === 3) {
                errorMessage = 'Timeout de géolocalisation';
            }

            return { success: false, error: errorMessage };
        }
    },

    // Vérifier le statut des permissions
    checkPermissionStatus: async function() {
        if ('permissions' in navigator) {
            try {
                const permission = await navigator.permissions.query({ name: 'geolocation' });
                return {
                    state: permission.state, // 'granted', 'denied', 'prompt'
                    supported: true
                };
            } catch (error) {
                console.warn('Impossible de vérifier les permissions:', error);
                return { state: 'unknown', supported: false };
            }
        }

        return { state: 'unknown', supported: false };
    },

    // Options de géolocalisation optimisées pour différents cas d'usage
    getOptions: function(type = 'default') {
        const options = {
            default: {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 60000
            },

            // Pour le suivi en temps réel (plus fréquent, moins de cache)
            tracking: {
                enableHighAccuracy: true,
                timeout: 15000,
                maximumAge: 30000
            },

            // Pour l'économie de batterie (moins précis mais plus efficace)
            battery_saving: {
                enableHighAccuracy: false,
                timeout: 20000,
                maximumAge: 300000 // 5 minutes
            },

            // Pour la navigation (très précis)
            navigation: {
                enableHighAccuracy: true,
                timeout: 5000,
                maximumAge: 10000
            }
        };

        return options[type] || options.default;
    },

    // Calculer la distance entre deux points (client-side)
    calculateDistance: function(lat1, lon1, lat2, lon2) {
        const R = 6371; // Rayon de la Terre en km
        const dLat = this.toRadians(lat2 - lat1);
        const dLon = this.toRadians(lon2 - lon1);

        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                  Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
                  Math.sin(dLon / 2) * Math.sin(dLon / 2);

        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return R * c;
    },

    // Convertir degrés en radians
    toRadians: function(degrees) {
        return degrees * (Math.PI / 180);
    },

    // Formater une position pour l'affichage
    formatPosition: function(lat, lon, accuracy) {
        const latStr = lat.toFixed(6);
        const lonStr = lon.toFixed(6);
        const accStr = accuracy ? ` (±${Math.round(accuracy)}m)` : '';

        return `${latStr}, ${lonStr}${accStr}`;
    },

    // Obtenir des informations sur la précision
    getAccuracyLevel: function(accuracy) {
        if (!accuracy) return 'inconnue';

        if (accuracy <= 5) return 'excellente';
        if (accuracy <= 10) return 'très bonne';
        if (accuracy <= 20) return 'bonne';
        if (accuracy <= 50) return 'moyenne';
        if (accuracy <= 100) return 'faible';
        return 'très faible';
    },

    // Test de performance de géolocalisation
    performanceTest: async function() {
        const results = {
            supported: this.isSupported(),
            permissionStatus: await this.checkPermissionStatus(),
            tests: []
        };

        if (!results.supported) {
            return results;
        }

        // Test de position rapide (basse précision)
        try {
            const start = performance.now();
            await new Promise((resolve, reject) => {
                navigator.geolocation.getCurrentPosition(resolve, reject, {
                    enableHighAccuracy: false,
                    timeout: 5000,
                    maximumAge: 0
                });
            });
            const end = performance.now();

            results.tests.push({
                type: 'Position rapide',
                duration: Math.round(end - start),
                success: true
            });
        } catch (error) {
            results.tests.push({
                type: 'Position rapide',
                duration: 0,
                success: false,
                error: error.message
            });
        }

        // Test de position précise
        try {
            const start = performance.now();
            await new Promise((resolve, reject) => {
                navigator.geolocation.getCurrentPosition(resolve, reject, {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 0
                });
            });
            const end = performance.now();

            results.tests.push({
                type: 'Position précise',
                duration: Math.round(end - start),
                success: true
            });
        } catch (error) {
            results.tests.push({
                type: 'Position précise',
                duration: 0,
                success: false,
                error: error.message
            });
        }

        return results;
    },

    // Nettoyer les ressources
    cleanup: function() {
        this.stopWatching();
        this.dotNetHelper = null;
        console.log('GeolocationHelper nettoyé');
    }
};

// Ajouter des méthodes globales pour l'interopérabilité
window.getCurrentPosition = () => window.geolocationHelper.getCurrentPosition();
window.startWatchingPosition = (timeoutMs) => window.geolocationHelper.startWatching(timeoutMs);
window.stopWatchingPosition = () => window.geolocationHelper.stopWatching();
window.checkGeolocationSupport = () => window.geolocationHelper.isSupported();
window.requestGeolocationPermission = () => window.geolocationHelper.requestPermission();

// Intégration avec le service de suivi
if (window.trackingService) {
    // Connecter les services
    window.geolocationHelper.trackingService = window.trackingService;
    window.trackingService.geolocationHelper = window.geolocationHelper;
}

// Nettoyage automatique lors du déchargement de la page
window.addEventListener('beforeunload', () => {
    window.geolocationHelper.cleanup();
});

console.log('GeolocationHelper chargé et prêt 🌍');