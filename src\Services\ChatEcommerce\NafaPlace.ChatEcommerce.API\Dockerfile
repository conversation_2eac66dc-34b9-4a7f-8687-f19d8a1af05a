# Utiliser l'image de base ASP.NET Core
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 80

# Utiliser l'image SDK pour la construction
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copier les fichiers de projet et restaurer les dépendances
COPY ["src/Services/ChatEcommerce/NafaPlace.ChatEcommerce.API/NafaPlace.ChatEcommerce.API.csproj", "src/Services/ChatEcommerce/NafaPlace.ChatEcommerce.API/"]
COPY ["src/Services/ChatEcommerce/NafaPlace.ChatEcommerce.Application/NafaPlace.ChatEcommerce.Application.csproj", "src/Services/ChatEcommerce/NafaPlace.ChatEcommerce.Application/"]
COPY ["src/Services/ChatEcommerce/NafaPlace.ChatEcommerce.Domain/NafaPlace.ChatEcommerce.Domain.csproj", "src/Services/ChatEcommerce/NafaPlace.ChatEcommerce.Domain/"]
COPY ["src/Services/ChatEcommerce/NafaPlace.ChatEcommerce.Infrastructure/NafaPlace.ChatEcommerce.Infrastructure.csproj", "src/Services/ChatEcommerce/NafaPlace.ChatEcommerce.Infrastructure/"]

RUN dotnet restore "src/Services/ChatEcommerce/NafaPlace.ChatEcommerce.API/NafaPlace.ChatEcommerce.API.csproj"

# Copier tout le code source
COPY . .
WORKDIR "/src/src/Services/ChatEcommerce/NafaPlace.ChatEcommerce.API"

# Construire l'application
RUN dotnet build "NafaPlace.ChatEcommerce.API.csproj" -c Release -o /app/build

# Publier l'application
FROM build AS publish
RUN dotnet publish "NafaPlace.ChatEcommerce.API.csproj" -c Release -o /app/publish

# Image finale
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "NafaPlace.ChatEcommerce.API.dll"]
