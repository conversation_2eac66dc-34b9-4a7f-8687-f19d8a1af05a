using NafaPlace.Analytics.Domain.DTOs;

namespace NafaPlace.Analytics.Application.Services;

/// <summary>
/// Interface pour le service d'analytics avancé
/// Fournit des analyses métier approfondies et des insights automatisés
/// </summary>
public interface IAdvancedAnalyticsService
{
    /// <summary>
    /// Génère un tableau de bord analytics complet avec KPIs et métriques
    /// </summary>
    Task<ComprehensiveDashboardDto> GetComprehensiveDashboardAsync(DateTime fromDate, DateTime toDate);

    /// <summary>
    /// Analyse les tendances en temps réel
    /// </summary>
    Task<RealTimeTrendsDto> GetRealTimeTrendsAsync();

    /// <summary>
    /// Analyse de cohort pour comprendre la rétention client
    /// </summary>
    Task<CohortAnalysisDto> GetCohortAnalysisAsync(DateTime startDate, int periodMonths);

    /// <summary>
    /// Détecte les anomalies dans les données de vente
    /// </summary>
    Task<AnomalyDetectionDto> DetectAnomaliesAsync(DateTime fromDate, DateTime toDate);

    /// <summary>
    /// Analyse d'attribution marketing et ROI des canaux
    /// </summary>
    Task<MarketingAttributionDto> GetMarketingAttributionAsync(DateTime fromDate, DateTime toDate);

    /// <summary>
    /// Analyse de performance des vendeurs
    /// </summary>
    Task<SellerPerformanceAnalysisDto> GetSellerPerformanceAnalysisAsync(DateTime fromDate, DateTime toDate);

    /// <summary>
    /// Génère un rapport personnalisé basé sur les critères fournis
    /// </summary>
    Task<object> GenerateCustomReportAsync(CustomReportRequestDto request);

    /// <summary>
    /// Génère des insights automatiques grâce à l'IA
    /// </summary>
    Task<List<AIInsightDto>> GenerateAIInsightsAsync();

    /// <summary>
    /// Configure des alertes automatiques pour l'utilisateur
    /// </summary>
    Task<bool> ConfigureAlertsAsync(AlertConfigurationDto configuration);

    /// <summary>
    /// Récupère les alertes actives pour un utilisateur
    /// </summary>
    Task<List<AlertDto>> GetActiveAlertsAsync(string userId);

    /// <summary>
    /// Exporte les données d'analytics dans différents formats
    /// </summary>
    Task<ExportResultDto> ExportDataAsync(ExportRequestDto request);

    /// <summary>
    /// Calcule les métriques de performance en temps réel
    /// </summary>
    Task<RealTimeMetricsDto> GetRealTimeMetricsAsync();

    /// <summary>
    /// Analyse la performance géographique (spécifique à la Guinée)
    /// </summary>
    Task<List<GeographicPerformanceDto>> GetGeographicPerformanceAsync(DateTime fromDate, DateTime toDate);

    /// <summary>
    /// Analyse les patterns de comportement saisonniers
    /// </summary>
    Task<SeasonalAnalysisDto> GetSeasonalAnalysisAsync(int years = 2);

    /// <summary>
    /// Calcule l'impact des promotions et campagnes
    /// </summary>
    Task<PromotionImpactAnalysisDto> GetPromotionImpactAnalysisAsync(List<int> promotionIds);

    /// <summary>
    /// Analyse la santé globale de l'entreprise
    /// </summary>
    Task<BusinessHealthScoreDto> GetBusinessHealthScoreAsync();

    /// <summary>
    /// Recommandations d'optimisation métier
    /// </summary>
    Task<List<BusinessOptimizationDto>> GetBusinessOptimizationRecommendationsAsync();
}

/// <summary>
/// Interface pour le service d'analytics prédictif
/// Utilise l'IA pour faire des prédictions métier
/// </summary>
public interface IPredictiveAnalyticsService
{
    /// <summary>
    /// Prédit les ventes futures basées sur les données historiques
    /// </summary>
    Task<List<SalesPredictionDto>> PredictSalesAsync(int daysAhead);

    /// <summary>
    /// Prédit la demande pour des produits spécifiques
    /// </summary>
    Task<List<DemandPredictionDto>> PredictDemandAsync(List<int> productIds, int daysAhead);

    /// <summary>
    /// Prédit le taux de désabonnement des clients
    /// </summary>
    Task<CustomerChurnAnalysisDto> PredictCustomerChurnAsync();

    /// <summary>
    /// Optimise l'inventaire basé sur les prédictions de demande
    /// </summary>
    Task<InventoryOptimizationDto> OptimizeInventoryAsync(int warehouseId);

    /// <summary>
    /// Prédit les tendances de marché émergentes
    /// </summary>
    Task<List<MarketTrendPredictionDto>> PredictMarketTrendsAsync(int weeksAhead = 4);

    /// <summary>
    /// Prédit la valeur à vie des clients (CLV)
    /// </summary>
    Task<List<CustomerValuePredictionDto>> PredictCustomerLifetimeValueAsync(List<string> customerIds);

    /// <summary>
    /// Entraîne les modèles ML avec de nouvelles données
    /// </summary>
    Task<ModelTrainingResultDto> TrainModelsAsync(TrainModelsRequestDto request, string userId);

    /// <summary>
    /// Obtient les métriques de performance des modèles ML
    /// </summary>
    Task<List<ModelPerformanceDto>> GetModelPerformanceMetricsAsync();

    /// <summary>
    /// Prédit l'impact de changements de prix
    /// </summary>
    Task<PriceChangeImpactDto> PredictPriceChangeImpactAsync(int productId, decimal newPrice);

    /// <summary>
    /// Prédit le succès de nouveaux produits
    /// </summary>
    Task<ProductSuccessPredictionDto> PredictNewProductSuccessAsync(NewProductDto productInfo);

    /// <summary>
    /// Analyse prédictive de la concurrence
    /// </summary>
    Task<CompetitiveAnalysisPredictionDto> PredictCompetitiveMovesAsync();

    /// <summary>
    /// Optimise les stratégies marketing avec l'IA
    /// </summary>
    Task<MarketingOptimizationDto> OptimizeMarketingStrategiesAsync();
}

/// <summary>
/// Interface pour le service d'insights client
/// Analyse comportementale et segmentation avancée
/// </summary>
public interface ICustomerInsightsService
{
    /// <summary>
    /// Analyse avancée du comportement client
    /// </summary>
    Task<AdvancedCustomerInsightsDto> GetAdvancedCustomerInsightsAsync(string? segment = null);

    /// <summary>
    /// Segmentation automatique des clients par IA
    /// </summary>
    Task<AICustomerSegmentationDto> GetAICustomerSegmentationAsync();

    /// <summary>
    /// Génère des recommandations personnalisées pour un client
    /// </summary>
    Task<List<PersonalizedRecommendationDto>> GetPersonalizedRecommendationsAsync(string customerId, int count = 10);

    /// <summary>
    /// Analyse RFM (Récence, Fréquence, Montant) des clients
    /// </summary>
    Task<RFMAnalysisDto> GetRFMAnalysisAsync();

    /// <summary>
    /// Analyse du parcours client (customer journey)
    /// </summary>
    Task<CustomerJourneyAnalysisDto> GetCustomerJourneyAnalysisAsync();

    /// <summary>
    /// Identifie les clients à forte valeur potentielle
    /// </summary>
    Task<List<HighValueCustomerDto>> IdentifyHighValueCustomersAsync();

    /// <summary>
    /// Analyse les préférences et affinités produit par segment
    /// </summary>
    Task<List<ProductAffinityAnalysisDto>> GetProductAffinityAnalysisAsync();

    /// <summary>
    /// Analyse de satisfaction client basée sur les interactions
    /// </summary>
    Task<CustomerSatisfactionAnalysisDto> GetCustomerSatisfactionAnalysisAsync();

    /// <summary>
    /// Détecte les changements de comportement clients
    /// </summary>
    Task<List<BehaviorChangeDto>> DetectCustomerBehaviorChangesAsync();

    /// <summary>
    /// Analyse de la réactivité aux campagnes marketing
    /// </summary>
    Task<MarketingResponsivenessDto> GetMarketingResponsivenessAnalysisAsync();

    /// <summary>
    /// Prédit le prochain achat probable d'un client
    /// </summary>
    Task<NextPurchasePredictionDto> PredictNextPurchaseAsync(string customerId);

    /// <summary>
    /// Analyse la fidélité et l'engagement client
    /// </summary>
    Task<CustomerLoyaltyAnalysisDto> GetCustomerLoyaltyAnalysisAsync();
}

/// <summary>
/// Interface pour le service d'analyse de marché
/// Analyse concurrentielle et positionnement
/// </summary>
public interface IMarketAnalysisService
{
    /// <summary>
    /// Analyse de positionnement marché et concurrence
    /// </summary>
    Task<MarketPositionAnalysisDto> GetMarketPositionAnalysisAsync(string? category = null);

    /// <summary>
    /// Optimisation des prix basée sur l'IA et l'analyse concurrentielle
    /// </summary>
    Task<AIPricingOptimizationDto> GetAIPricingOptimizationAsync(List<int> productIds);

    /// <summary>
    /// Analyse des tendances de marché en Guinée
    /// </summary>
    Task<GuineaMarketTrendsDto> GetGuineaMarketTrendsAsync();

    /// <summary>
    /// Analyse de l'élasticité prix de la demande
    /// </summary>
    Task<List<PriceElasticityDto>> GetPriceElasticityAnalysisAsync(List<int> productIds);

    /// <summary>
    /// Identifie les opportunités de marché émergentes
    /// </summary>
    Task<List<MarketOpportunityDto>> IdentifyMarketOpportunitiesAsync();

    /// <summary>
    /// Analyse de la part de marché par catégorie
    /// </summary>
    Task<List<MarketShareAnalysisDto>> GetMarketShareAnalysisAsync();

    /// <summary>
    /// Surveillance concurrentielle automatisée
    /// </summary>
    Task<CompetitorMonitoringDto> GetCompetitorMonitoringAsync();

    /// <summary>
    /// Analyse des barrières à l'entrée du marché
    /// </summary>
    Task<MarketBarriersAnalysisDto> GetMarketBarriersAnalysisAsync();

    /// <summary>
    /// Recommandations de stratégie concurrentielle
    /// </summary>
    Task<CompetitiveStrategyDto> GetCompetitiveStrategyRecommendationsAsync();

    /// <summary>
    /// Analyse du potentiel de marché pour nouveaux produits
    /// </summary>
    Task<MarketPotentialDto> AssessMarketPotentialAsync(NewProductDto product);

    /// <summary>
    /// Analyse géopolitique et impact sur le marché guinéen
    /// </summary>
    Task<GeopoliticalImpactDto> GetGeopoliticalImpactAnalysisAsync();

    /// <summary>
    /// Recommandations d'expansion géographique
    /// </summary>
    Task<GeographicExpansionDto> GetGeographicExpansionRecommendationsAsync();
}

// DTOs complémentaires pour les services

public class AlertDto
{
    public string Id { get; set; } = "";
    public string Title { get; set; } = "";
    public string Message { get; set; } = "";
    public string Type { get; set; } = "";
    public string Severity { get; set; } = "";
    public DateTime CreatedAt { get; set; }
    public bool IsRead { get; set; }
    public object? Data { get; set; }
}

public class ExportResultDto
{
    public byte[] Data { get; set; } = Array.Empty<byte>();
    public string FileName { get; set; } = "";
    public string ContentType { get; set; } = "";
    public long Size { get; set; }
}

public class RealTimeMetricsDto
{
    public decimal CurrentHourRevenue { get; set; }
    public int CurrentHourOrders { get; set; }
    public int ActiveUsers { get; set; }
    public double ConversionRate { get; set; }
    public List<TopProductDto> TopProducts { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

public class SeasonalAnalysisDto
{
    public List<SeasonalTrendDto> Trends { get; set; } = new();
    public List<SeasonalInsightDto> Insights { get; set; } = new();
    public SeasonalForecastDto Forecast { get; set; } = new();
}

public class PromotionImpactAnalysisDto
{
    public List<PromotionMetricsDto> PromotionMetrics { get; set; } = new();
    public decimal TotalImpact { get; set; }
    public double ROI { get; set; }
    public List<string> Insights { get; set; } = new();
}

public class BusinessHealthScoreDto
{
    public double OverallScore { get; set; } // 0-100
    public List<HealthMetricDto> Metrics { get; set; } = new();
    public List<string> HealthWarnings { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

public class BusinessOptimizationDto
{
    public string Area { get; set; } = "";
    public string Recommendation { get; set; } = "";
    public decimal EstimatedImpact { get; set; }
    public string Priority { get; set; } = "";
    public List<string> Actions { get; set; } = new();
}

public class MarketTrendPredictionDto
{
    public string TrendName { get; set; } = "";
    public string Category { get; set; } = "";
    public double Probability { get; set; }
    public decimal EstimatedImpact { get; set; }
    public List<string> InfluencingFactors { get; set; } = new();
}

public class CustomerValuePredictionDto
{
    public string CustomerId { get; set; } = "";
    public decimal PredictedLTV { get; set; }
    public double Confidence { get; set; }
    public List<string> ValueDrivers { get; set; } = new();
}

public class ModelTrainingResultDto
{
    public bool Success { get; set; }
    public List<string> TrainedModels { get; set; } = new();
    public Dictionary<string, double> ModelAccuracies { get; set; } = new();
    public string TrainingId { get; set; } = "";
    public DateTime CompletedAt { get; set; }
}

public class ModelPerformanceDto
{
    public string ModelName { get; set; } = "";
    public string ModelType { get; set; } = "";
    public double Accuracy { get; set; }
    public double Precision { get; set; }
    public double Recall { get; set; }
    public DateTime LastTrained { get; set; }
    public string Status { get; set; } = "";
}

public class PriceChangeImpactDto
{
    public int ProductId { get; set; }
    public decimal CurrentPrice { get; set; }
    public decimal NewPrice { get; set; }
    public double PredictedDemandChange { get; set; }
    public decimal PredictedRevenueChange { get; set; }
    public double Confidence { get; set; }
}

public class NewProductDto
{
    public string Name { get; set; } = "";
    public string Category { get; set; } = "";
    public decimal Price { get; set; }
    public List<string> Features { get; set; } = new();
    public string Description { get; set; } = "";
}

public class ProductSuccessPredictionDto
{
    public double SuccessProbability { get; set; }
    public decimal PredictedRevenue { get; set; }
    public List<string> SuccessFactors { get; set; } = new();
    public List<string> RiskFactors { get; set; } = new();
}