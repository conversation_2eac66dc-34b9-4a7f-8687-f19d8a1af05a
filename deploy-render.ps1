# Script de déploiement automatisé pour Render
# Ce script préserve votre environnement local

param(
    [switch]$SkipBuild,
    [switch]$OnlyServices,
    [switch]$OnlyPortals,
    [string]$ServiceName = ""
)

Write-Host "🚀 Déploiement NafaPlace sur Render" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

# Vérifier que nous sommes dans le bon répertoire
if (-not (Test-Path "render.yaml")) {
    Write-Host "❌ Fichier render.yaml non trouvé. Assurez-vous d'être dans le répertoire racine du projet." -ForegroundColor Red
    exit 1
}

# Vérifier l'état Git
Write-Host "`n📋 Vérification de l'état Git..." -ForegroundColor Cyan
$gitStatus = git status --porcelain
if ($gitStatus) {
    Write-Host "⚠️ Il y a des modifications non commitées:" -ForegroundColor Yellow
    git status --short
    $continue = Read-Host "Voulez-vous continuer le déploiement ? (y/N)"
    if ($continue -ne "y" -and $continue -ne "Y") {
        Write-Host "❌ Déploiement annulé." -ForegroundColor Red
        exit 1
    }
}

# Sauvegarder la configuration locale
Write-Host "`n💾 Sauvegarde de la configuration locale..." -ForegroundColor Cyan
if (Test-Path "docker-compose.local.backup.yml") {
    Remove-Item "docker-compose.local.backup.yml"
}
Copy-Item "docker-compose.yml" "docker-compose.local.backup.yml"
Write-Host "✅ Configuration locale sauvegardée dans docker-compose.local.backup.yml" -ForegroundColor Green

# Fonction pour déployer un service spécifique
function Deploy-Service {
    param($ServiceName)
    
    Write-Host "`n🔧 Déploiement du service: $ServiceName" -ForegroundColor Cyan
    
    # Ici vous pouvez ajouter la logique spécifique pour déployer sur Render
    # Par exemple, utiliser l'API Render ou des webhooks
    
    Write-Host "✅ Service $ServiceName déployé avec succès" -ForegroundColor Green
}

# Déploiement des services
if (-not $OnlyPortals) {
    Write-Host "`n🔧 Déploiement des services API..." -ForegroundColor Cyan
    
    $services = @(
        "nafaplace-identity-api",
        "nafaplace-catalog-api", 
        "nafaplace-cart-api",
        "nafaplace-order-api",
        "nafaplace-payment-api",
        "nafaplace-reviews-api",
        "nafaplace-notifications-api",
        "nafaplace-wishlist-api",
        "nafaplace-inventory-api",
        "nafaplace-coupon-api",
        "nafaplace-delivery-api",
        "nafaplace-analytics-api",
        "nafaplace-api-gateway"
    )
    
    if ($ServiceName) {
        $services = @($ServiceName)
    }
    
    foreach ($service in $services) {
        Deploy-Service $service
        Start-Sleep -Seconds 2
    }
}

# Déploiement des portails web
if (-not $OnlyServices) {
    Write-Host "`n🌐 Déploiement des portails web..." -ForegroundColor Cyan
    
    $portals = @(
        "nafaplace-web",
        "nafaplace-admin-portal", 
        "nafaplace-seller-portal"
    )
    
    foreach ($portal in $portals) {
        Deploy-Service $portal
        Start-Sleep -Seconds 2
    }
}

# Vérification post-déploiement
Write-Host "`n🔍 Vérification des services déployés..." -ForegroundColor Cyan

$endpoints = @{
    "API Gateway" = "https://nafaplace-api-gateway.onrender.com/health"
    "Identity API" = "https://nafaplace-identity-api.onrender.com/health"
    "Catalog API" = "https://nafaplace-catalog-api.onrender.com/health"
    "Web Portal" = "https://nafaplace-web.onrender.com"
    "Admin Portal" = "https://nafaplace-admin-portal.onrender.com"
    "Seller Portal" = "https://nafaplace-seller-portal.onrender.com"
}

foreach ($endpoint in $endpoints.GetEnumerator()) {
    try {
        Write-Host "Vérification de $($endpoint.Key)..." -NoNewline
        $response = Invoke-WebRequest -Uri $endpoint.Value -Method GET -TimeoutSec 10 -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-Host " ✅" -ForegroundColor Green
        } else {
            Write-Host " ⚠️ (Status: $($response.StatusCode))" -ForegroundColor Yellow
        }
    } catch {
        Write-Host " ❌ (Erreur: $($_.Exception.Message))" -ForegroundColor Red
    }
}

# Restaurer la configuration locale
Write-Host "`n🔄 Restauration de la configuration locale..." -ForegroundColor Cyan
if (Test-Path "docker-compose.local.backup.yml") {
    Copy-Item "docker-compose.local.backup.yml" "docker-compose.yml" -Force
    Remove-Item "docker-compose.local.backup.yml"
    Write-Host "✅ Configuration locale restaurée" -ForegroundColor Green
}

Write-Host "`n🎉 Déploiement terminé !" -ForegroundColor Green
Write-Host "📋 URLs de production:" -ForegroundColor Cyan
Write-Host "   🌐 Site Web: https://nafaplace-web.onrender.com" -ForegroundColor White
Write-Host "   👨‍💼 Admin: https://nafaplace-admin-portal.onrender.com" -ForegroundColor White
Write-Host "   🏪 Vendeur: https://nafaplace-seller-portal.onrender.com" -ForegroundColor White
Write-Host "   🔗 API: https://nafaplace-api-gateway.onrender.com" -ForegroundColor White

Write-Host "`n💡 Votre environnement local n'a pas été modifié et fonctionne toujours avec:" -ForegroundColor Yellow
Write-Host "   docker-compose up --build -d" -ForegroundColor White
