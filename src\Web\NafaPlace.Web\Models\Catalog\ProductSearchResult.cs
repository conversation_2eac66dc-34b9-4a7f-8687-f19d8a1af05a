namespace NafaPlace.Web.Models.Catalog;

public class ProductSearchResult
{
    public PagedResult<ProductDto> Products { get; set; } = new();
    public SearchMetadata Metadata { get; set; } = new();
    public List<SearchSuggestion> Suggestions { get; set; } = new();
    public List<SearchFacet> Facets { get; set; } = new();
    public List<ProductDto> SimilarProducts { get; set; } = new();
}

public class PagedResult<T>
{
    public List<T> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int Page => PageNumber; // Alias pour compatibilité
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;
}



public class SearchMetadata
{
    public string? OriginalQuery { get; set; }
    public string? CorrectedQuery { get; set; }
    public int TotalResults { get; set; }
    public double SearchTimeMs { get; set; }
    public bool HasSpellingSuggestions { get; set; }
    public List<string> AppliedFilters { get; set; } = new();
    public Dictionary<string, object> SearchStats { get; set; } = new();
}

public class SearchSuggestion
{
    public string Text { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // product, category, brand, etc.
    public int ResultCount { get; set; }
    public double Relevance { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }
}

public class SearchFacet
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // range, list, boolean
    public List<SearchFacetValue> Values { get; set; } = new();
}

public class SearchFacetValue
{
    public string Value { get; set; } = string.Empty;
    public string DisplayValue { get; set; } = string.Empty;
    public int Count { get; set; }
    public bool IsSelected { get; set; }
}

public class ImageSearchRequest
{
    public string ImageUrl { get; set; } = string.Empty;
    public int MaxResults { get; set; } = 10;
}
