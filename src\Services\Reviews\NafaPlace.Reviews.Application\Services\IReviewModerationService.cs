using NafaPlace.Reviews.DTOs;

namespace NafaPlace.Reviews.Application.Services;

public interface IReviewModerationService
{
    Task<bool> ReportReviewAsync(int reviewId, ReportReviewDto reportDto, string userId);
    Task<bool> ApproveReviewAsync(int reviewId, string moderatorId);
    Task<bool> RejectReviewAsync(int reviewId, string moderatorId, string? reason = null);
    Task<bool> DeleteReviewAsync(int reviewId, string moderatorId);
    Task<List<ReviewDto>> GetReportedReviewsAsync(int page = 1, int pageSize = 10);
    Task<List<ReviewDto>> GetPendingReviewsAsync(int page = 1, int pageSize = 10);
    Task<bool> ModerateReviewAsync(int reviewId);
    Task<bool> BulkModerateReviewsAsync(List<int> reviewIds, string action, string? reason = null);
}
