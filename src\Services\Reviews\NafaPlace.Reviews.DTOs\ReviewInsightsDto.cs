namespace NafaPlace.Reviews.DTOs;

public class ReviewInsightsDto
{
    public int ProductId { get; set; }
    public double AverageRating { get; set; }
    public int TotalReviews { get; set; }
    public Dictionary<int, int> RatingDistribution { get; set; } = new();
    public List<string> CommonKeywords { get; set; } = new();
    public double SentimentScore { get; set; }
    public int PositiveReviews { get; set; }
    public int NegativeReviews { get; set; }
    public int NeutralReviews { get; set; }
}
