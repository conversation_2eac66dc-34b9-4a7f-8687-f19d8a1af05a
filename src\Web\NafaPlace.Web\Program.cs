using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using Blazored.LocalStorage;
using NafaPlace.Web;
using NafaPlace.Web.Services;
using NafaPlace.Common.Services;

var builder = WebAssemblyHostBuilder.CreateDefault(args);
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

// Configuration des HttpClient pour chaque API
var baseApiUrl = builder.Configuration["ApiEndpoints:Gateway"] ?? "http://localhost:5000"; // API Gateway
var identityApiUrl = builder.Configuration["ApiEndpoints:IdentityApi"] ?? "http://localhost:5155";
var catalogApiUrl = builder.Configuration["ApiEndpoints:CatalogApi"] ?? "http://localhost:5243";
var cartApiUrl = builder.Configuration["ApiEndpoints:CartApi"] ?? "http://localhost:5003"; // Port for the new Cart API
var orderApiUrl = builder.Configuration["ApiEndpoints:OrderApi"] ?? "http://localhost:5004"; // Port for the new Order API
var reviewApiUrl = builder.Configuration["ApiEndpoints:ReviewApi"] ?? "http://localhost:5006"; // Port for the Reviews API
var deliveryApiUrl = builder.Configuration["ApiEndpoints:DeliveryApi"] ?? "http://localhost:5010"; // Port for the Delivery API
var chatApiUrl = builder.Configuration["ApiEndpoints:ChatApi"] ?? "http://localhost:5007"; // Port for the Chat API

// Services
builder.Services.AddBlazoredLocalStorage();

// Configuration du service Gateway centralisé
builder.Services.AddScoped<HttpClient>(sp => new HttpClient());
builder.Services.AddScoped<IGatewayHttpClientService, BlazorWebAssemblyGatewayHttpClientService>();

// Service d'authentification centralisé pour HttpClient
builder.Services.AddScoped<IAuthenticatedHttpClientService, AuthenticatedHttpClientService>();

// Services utilisant la Gateway (via HttpClient pointant vers Gateway)
builder.Services.AddScoped<ICategoryService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) };
    return new CategoryService(httpClient);
});

builder.Services.AddScoped<IProductService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) };
    var reviewService = sp.GetRequiredService<IReviewService>();
    return new ProductService(httpClient, reviewService);
});

builder.Services.AddScoped<IAdvancedSearchService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(baseApiUrl) };
    var logger = sp.GetRequiredService<ILogger<AdvancedSearchService>>();
    return new AdvancedSearchService(httpClient, logger);
});

builder.Services.AddAuthorizationCore();

// Enregistrer CustomAuthStateProvider en premier
builder.Services.AddScoped<CustomAuthStateProvider>();
builder.Services.AddScoped<AuthenticationStateProvider>(provider =>
    provider.GetRequiredService<CustomAuthStateProvider>());

// Puis enregistrer les services utilisant la Gateway (via HttpClient)
builder.Services.AddScoped<IAuthService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) };
    var localStorage = sp.GetRequiredService<ILocalStorageService>();
    var authStateProvider = sp.GetRequiredService<CustomAuthStateProvider>();
    var guestCartMergeService = sp.GetRequiredService<IGuestCartMergeService>();
    return new AuthService(httpClient, localStorage, authStateProvider, guestCartMergeService);
});

builder.Services.AddSingleton<TokenExpirationService>();
builder.Services.AddSingleton<CartNotificationService>();

builder.Services.AddScoped<ICartService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) };
    var notificationService = sp.GetRequiredService<CartNotificationService>();
    return new CartService(httpClient, notificationService);
});

builder.Services.AddScoped<IGuestCartMergeService, GuestCartMergeService>();

// Service de cache pour les reviews
builder.Services.AddSingleton<IReviewCacheService, ReviewCacheService>();

builder.Services.AddScoped<IOrderService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) };
    return new OrderService(httpClient);
});

builder.Services.AddScoped<IReviewService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) };
    var authStateProvider = sp.GetRequiredService<AuthenticationStateProvider>();
    var localStorage = sp.GetRequiredService<ILocalStorageService>();
    var jsRuntime = sp.GetRequiredService<IJSRuntime>();
    var cacheService = sp.GetRequiredService<IReviewCacheService>();
    return new ReviewService(httpClient, authStateProvider, localStorage, jsRuntime, cacheService);
});

// Service Wishlist avec authentification centralisée
builder.Services.AddScoped<IWishlistService>(sp =>
{
    var authenticatedHttpClient = sp.GetRequiredService<IAuthenticatedHttpClientService>();
    var logger = sp.GetRequiredService<ILogger<WishlistService>>();
    var authStateProvider = sp.GetRequiredService<AuthenticationStateProvider>();
    return new WishlistService(authenticatedHttpClient, logger, authStateProvider);
});

// Service de coupons
builder.Services.AddScoped<ICouponService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) };
    var configuration = sp.GetRequiredService<IConfiguration>();
    var logger = sp.GetRequiredService<ILogger<CouponService>>();
    var jsRuntime = sp.GetRequiredService<IJSRuntime>();
    return new CouponService(httpClient, configuration, logger, jsRuntime);
});

// Service de suivi de livraison
builder.Services.AddScoped<IDeliveryTrackingService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) };
    var logger = sp.GetRequiredService<ILogger<DeliveryTrackingService>>();
    return new DeliveryTrackingService(httpClient, logger);
});

// Service JSRuntime sécurisé
builder.Services.AddScoped<ISafeJSRuntimeService, SafeJSRuntimeService>();

// Service de chat
builder.Services.AddScoped<IChatService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) };
    var authStateProvider = sp.GetRequiredService<AuthenticationStateProvider>();
    var localStorage = sp.GetRequiredService<ILocalStorageService>();
    var jsRuntime = sp.GetRequiredService<IJSRuntime>();
    var logger = sp.GetRequiredService<ILogger<ChatService>>();
    return new ChatService(httpClient, authStateProvider, localStorage, jsRuntime, logger);
});

// Service de stockage de chat
builder.Services.AddScoped<IChatStorageService, ChatStorageService>();

// Service pour le chat e-commerce (conversations client-vendeur)
builder.Services.AddScoped<IChatEcommerceService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri("http://localhost:5000/api/chat-ecommerce/") };
    var localStorage = sp.GetRequiredService<ILocalStorageService>();
    return new ChatEcommerceService(httpClient, localStorage);
});

// Service de notifications
builder.Services.AddScoped<INotificationService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) };
    var logger = sp.GetRequiredService<ILogger<NotificationService>>();
    return new NotificationService(httpClient, logger);
});

// Service d'analytics
builder.Services.AddScoped<IAnalyticsService>(sp =>
{
    var httpClient = new HttpClient { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) };
    var logger = sp.GetRequiredService<ILogger<AnalyticsService>>();
    return new AnalyticsService(httpClient, logger);
});

var app = builder.Build();

// Démarrer le service de vérification d'expiration des tokens
var tokenExpirationService = app.Services.GetRequiredService<TokenExpirationService>();
tokenExpirationService.StartTokenExpirationCheck();

await app.RunAsync();
