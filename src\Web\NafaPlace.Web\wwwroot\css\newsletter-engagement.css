/* Newsletter and Engagement Section */
.newsletter-engagement-section {
    background: linear-gradient(135deg, #003366 0%, #004488 50%, #0066AA 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.newsletter-engagement-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.03)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.04)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

/* Newsletter Container */
.newsletter-container {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 30px;
    padding: 3rem;
    margin-bottom: 4rem;
    position: relative;
    z-index: 2;
}

/* Newsletter Content */
.newsletter-content {
    position: relative;
    z-index: 2;
}

.newsletter-badge {
    display: inline-flex;
    align-items: center;
    background: rgba(249, 99, 2, 0.2);
    border: 1px solid rgba(249, 99, 2, 0.3);
    color: #FFD700;
    padding: 8px 20px;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    backdrop-filter: blur(10px);
}

.newsletter-title {
    font-size: 2.5rem;
    font-weight: 900;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.newsletter-description {
    font-size: 1.2rem;
    line-height: 1.6;
    opacity: 0.9;
    margin-bottom: 2rem;
}

/* Benefits List */
.benefits-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2.5rem;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1rem;
    opacity: 0.95;
}

.benefit-item i {
    color: #4CAF50;
    font-size: 1.2rem;
    flex-shrink: 0;
}

/* Newsletter Form */
.newsletter-form {
    margin-bottom: 2rem;
}

.form-group {
    display: flex;
    gap: 0;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    padding: 8px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.form-group:focus-within {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(249, 99, 2, 0.5);
    box-shadow: 0 0 0 3px rgba(249, 99, 2, 0.2);
}

.newsletter-input {
    flex: 1;
    background: transparent;
    border: none;
    color: white;
    font-size: 1rem;
    padding: 15px 20px;
    outline: none;
}

.newsletter-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.newsletter-btn {
    background: linear-gradient(135deg, #F96302, #E73C30);
    border: none;
    color: white;
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 700;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.newsletter-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(249, 99, 2, 0.4);
}

.newsletter-btn:active {
    transform: translateY(0);
}

.btn-text {
    transition: transform 0.3s ease;
}

.btn-icon {
    transition: transform 0.3s ease;
}

.newsletter-btn:hover .btn-text {
    transform: translateX(-5px);
}

.newsletter-btn:hover .btn-icon {
    transform: translateX(5px);
}

.btn-ripple {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    transform: scale(0);
    transition: transform 0.6s ease;
    border-radius: 50px;
}

.newsletter-btn:active .btn-ripple {
    transform: scale(1);
}

.newsletter-privacy {
    font-size: 0.85rem;
    opacity: 0.8;
    margin-top: 1rem;
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Social Proof Mini */
.social-proof-mini {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.subscriber-avatars {
    display: flex;
    align-items: center;
}

.subscriber-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.3);
    margin-left: -10px;
    transition: all 0.3s ease;
}

.subscriber-avatar:first-child {
    margin-left: 0;
}

.subscriber-avatar:hover {
    transform: scale(1.1);
    z-index: 2;
}

.subscriber-count {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #F96302, #E73C30);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 700;
    margin-left: -10px;
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.social-proof-text {
    font-size: 0.9rem;
    opacity: 0.9;
    margin: 0;
}

/* Newsletter Visual */
.newsletter-visual {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.newsletter-mockup {
    position: relative;
}

.mockup-phone {
    width: 280px;
    height: 500px;
    background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
    border-radius: 40px;
    padding: 20px;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
    position: relative;
}

.phone-screen {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-radius: 30px;
    padding: 20px;
    overflow: hidden;
}

.notification-preview {
    background: white;
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.notification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.notification-logo {
    width: 30px;
    height: 30px;
}

.notification-time {
    font-size: 0.8rem;
    color: #6c757d;
}

.notification-content h4 {
    color: #003366;
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.notification-content p {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.notification-image {
    width: 100%;
    height: 120px;
    border-radius: 15px;
    overflow: hidden;
}

.notification-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Floating Elements */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-discount,
.floating-heart,
.floating-star {
    position: absolute;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    animation: float 6s ease-in-out infinite;
}

.floating-discount {
    width: 60px;
    height: 60px;
    top: 20%;
    right: -30px;
    background: linear-gradient(135deg, #E73C30, #F96302);
    color: white;
    font-size: 0.9rem;
    animation-delay: 0s;
}

.floating-heart {
    width: 50px;
    height: 50px;
    top: 60%;
    left: -25px;
    color: #E73C30;
    font-size: 1.2rem;
    animation-delay: 2s;
}

.floating-star {
    width: 45px;
    height: 45px;
    top: 40%;
    right: -20px;
    color: #FFD700;
    font-size: 1.1rem;
    animation-delay: 4s;
}

/* AI Recommendations Section */
.ai-recommendations-section {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 30px;
    padding: 3rem;
    position: relative;
    z-index: 2;
}

.section-header-ai {
    text-align: center;
    margin-bottom: 3rem;
}

.ai-badge {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #9C27B0, #673AB7);
    color: white;
    padding: 8px 20px;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1rem;
    box-shadow: 0 4px 15px rgba(156, 39, 176, 0.3);
}

.ai-title {
    font-size: 2rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
}

.ai-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* AI Products Grid */
.ai-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.ai-product-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.ai-product-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.ai-badge-product {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(135deg, #9C27B0, #673AB7);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.product-image-ai {
    width: 100%;
    height: 150px;
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.product-image-ai img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.ai-product-card:hover .product-image-ai img {
    transform: scale(1.1);
}

.product-content-ai {
    text-align: center;
}

.product-title-ai {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: white;
}

.product-rating-ai {
    display: flex;
    justify-content: center;
    gap: 2px;
    margin-bottom: 0.75rem;
}

.product-rating-ai i {
    color: #FFD700;
    font-size: 0.9rem;
}

.product-price-ai {
    font-size: 1.2rem;
    font-weight: 700;
    color: #FFD700;
    margin-bottom: 1rem;
}

.btn-add-ai {
    background: linear-gradient(135deg, #F96302, #E73C30);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.btn-add-ai:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(249, 99, 2, 0.4);
}

/* AI Product Skeleton */
.ai-product-skeleton {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 1.5rem;
}

.skeleton-image-ai {
    width: 100%;
    height: 150px;
    background: linear-gradient(90deg, rgba(255,255,255,0.1) 25%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.1) 75%);
    background-size: 200% 100%;
    border-radius: 15px;
    margin-bottom: 1rem;
    animation: shimmer 1.5s infinite;
}

.skeleton-content-ai {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.skeleton-line {
    height: 16px;
    background: linear-gradient(90deg, rgba(255,255,255,0.1) 25%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.1) 75%);
    background-size: 200% 100%;
    border-radius: 8px;
    animation: shimmer 1.5s infinite;
}

.skeleton-line.short {
    width: 60%;
    margin: 0 auto;
}

.skeleton-button {
    height: 40px;
    background: linear-gradient(90deg, rgba(255,255,255,0.1) 25%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.1) 75%);
    background-size: 200% 100%;
    border-radius: 25px;
    animation: shimmer 1.5s infinite;
}

.btn-view-ai {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 15px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-view-ai:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
    color: white;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Animations */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Responsive Design */
@media (max-width: 992px) {
    .newsletter-container {
        padding: 2rem;
    }
    
    .newsletter-title {
        font-size: 2rem;
    }
    
    .mockup-phone {
        width: 240px;
        height: 420px;
    }
    
    .ai-recommendations-section {
        padding: 2rem;
    }
    
    .ai-title {
        font-size: 1.8rem;
    }
}

@media (max-width: 768px) {
    .newsletter-container,
    .ai-recommendations-section {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .newsletter-title {
        font-size: 1.8rem;
    }
    
    .newsletter-description {
        font-size: 1.1rem;
    }
    
    .form-group {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
        border-radius: 20px;
    }
    
    .newsletter-input {
        padding: 15px;
        border-radius: 15px;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .newsletter-btn {
        width: 100%;
        justify-content: center;
    }
    
    .social-proof-mini {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
    
    .mockup-phone {
        width: 200px;
        height: 350px;
    }
    
    .ai-products-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

@media (max-width: 576px) {
    .newsletter-engagement-section {
        padding: 3rem 0;
    }
    
    .newsletter-title {
        font-size: 1.6rem;
    }
    
    .benefits-list {
        gap: 0.75rem;
    }
    
    .benefit-item {
        font-size: 0.9rem;
    }
    
    .subscriber-avatar {
        width: 35px;
        height: 35px;
    }
    
    .subscriber-count {
        width: 35px;
        height: 35px;
        font-size: 0.7rem;
    }
    
    .ai-title {
        font-size: 1.6rem;
    }
    
    .ai-subtitle {
        font-size: 1rem;
    }
}
