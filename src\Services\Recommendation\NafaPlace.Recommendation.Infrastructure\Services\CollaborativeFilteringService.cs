using Microsoft.Extensions.Logging;
using NafaPlace.Recommendation.Application.DTOs;
using NafaPlace.Recommendation.Application.Interfaces;

namespace NafaPlace.Recommendation.Infrastructure.Services;

public class CollaborativeFilteringService : ICollaborativeFilteringService
{
    private readonly ILogger<CollaborativeFilteringService> _logger;

    public CollaborativeFilteringService(ILogger<CollaborativeFilteringService> logger)
    {
        _logger = logger;
    }

    public async Task<List<SimilarProductDto>> GetRecommendationsAsync(string userId, int limit)
    {
        try
        {
            _logger.LogInformation("Génération de recommandations par filtrage collaboratif pour {UserId}", userId);

            // Simuler des recommandations basées sur le filtrage collaboratif
            var recommendations = new List<SimilarProductDto>();
            
            for (int i = 1; i <= limit; i++)
            {
                recommendations.Add(new SimilarProductDto
                {
                    ProductId = i + 1000,
                    Name = $"Produit Collaboratif {i}",
                    Price = 35000 + (i * 5000),
                    ImageUrl = $"/images/collaborative-{i}.jpg",
                    SimilarityScore = 0.9 - (i * 0.05),
                    Reason = "Aimé par des utilisateurs similaires",
                    IsInStock = true,
                    Rating = 4.3 + (i * 0.1),
                    ReviewCount = 80 + (i * 10)
                });
            }

            return recommendations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la génération des recommandations collaboratives");
            return new List<SimilarProductDto>();
        }
    }

    public async Task<List<SimilarProductDto>> GetSimilarProductsAsync(int productId, string userId, int limit)
    {
        try
        {
            _logger.LogInformation("Recherche de produits similaires par filtrage collaboratif pour {ProductId}", productId);

            var similarProducts = new List<SimilarProductDto>();
            
            for (int i = 1; i <= limit; i++)
            {
                similarProducts.Add(new SimilarProductDto
                {
                    ProductId = productId + (i * 10),
                    Name = $"Produit Similaire CF {i}",
                    Price = 28000 + (i * 4000),
                    ImageUrl = $"/images/similar-cf-{productId}-{i}.jpg",
                    SimilarityScore = 0.85 - (i * 0.03),
                    Reason = "Apprécié par des utilisateurs aux goûts similaires",
                    IsInStock = true,
                    Rating = 4.2,
                    ReviewCount = 65 + (i * 8)
                });
            }

            return similarProducts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche de produits similaires par filtrage collaboratif");
            return new List<SimilarProductDto>();
        }
    }
}


