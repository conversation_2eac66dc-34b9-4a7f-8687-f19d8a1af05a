namespace NafaPlace.Loyalty.Domain.Entities;

public enum LoyaltyTier
{
    Bronze = 1,
    Silver = 2,
    Gold = 3,
    Platinum = 4,
    Diamond = 5
}

public enum PointTransactionType
{
    Earned = 1,
    Redeemed = 2,
    Expired = 3,
    Adjusted = 4,
    Bonus = 5
}

public enum TransactionStatus
{
    Pending = 1,
    Completed = 2,
    Failed = 3,
    Cancelled = 4
}

public enum RewardType
{
    Discount = 1,
    FreeShipping = 2,
    Product = 3,
    Cashback = 4,
    Experience = 5
}

public enum RewardCategory
{
    Shopping = 1,
    Shipping = 2,
    Experience = 3,
    Exclusive = 4
}

public enum RedemptionStatus
{
    Active = 1,
    Used = 2,
    Expired = 3,
    Cancelled = 4
}

public enum BadgeCategory
{
    Purchase = 1,
    Social = 2,
    Engagement = 3,
    Special = 4,
    Seasonal = 5
}

public enum BadgeRarity
{
    Common = 1,
    Uncommon = 2,
    Rare = 3,
    Epic = 4,
    Legendary = 5
}

public enum ChallengeType
{
    Purchase = 1,
    Social = 2,
    Engagement = 3,
    Seasonal = 4,
    Special = 5
}

public enum ChallengeDifficulty
{
    Easy = 1,
    Medium = 2,
    Hard = 3,
    Expert = 4
}

public enum ReferralStatus
{
    Pending = 1,
    Completed = 2,
    Cancelled = 3
}

public enum LoyaltyEventType
{
    DoublePoints = 1,
    BonusPoints = 2,
    SpecialRewards = 3,
    TierBonus = 4
}

public enum PointsEarnRuleType
{
    Purchase = 1,
    Registration = 2,
    Review = 3,
    Referral = 4,
    Social = 5,
    Birthday = 6
}

public enum LoyaltyNotificationType
{
    PointsEarned = 1,
    PointsExpiring = 2,
    RewardAvailable = 3,
    TierUpgrade = 4,
    BadgeEarned = 5,
    ChallengeCompleted = 6
}

public class LoyaltyAccount
{
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public int TotalPoints { get; set; }
    public int AvailablePoints { get; set; }
    public int PendingPoints { get; set; }
    public int LifetimePoints { get; set; }
    public LoyaltyTier CurrentTier { get; set; } = LoyaltyTier.Bronze;
    public DateTime? TierExpiryDate { get; set; }
    public decimal TotalSpent { get; set; }
    public int TotalOrders { get; set; }
    public DateTime JoinedAt { get; set; }
    public DateTime LastActivity { get; set; }
    public bool IsActive { get; set; } = true;
    public string Metadata { get; set; } = "{}";
}

public class PointTransaction
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public PointTransactionType Type { get; set; }
    public int Points { get; set; }
    public string Description { get; set; } = string.Empty;
    public string? ReferenceId { get; set; }
    public string? ReferenceType { get; set; }
    public TransactionStatus Status { get; set; }
    public string? ReasonCode { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
}

public class Reward
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? ImageUrl { get; set; }
    public RewardType Type { get; set; }
    public RewardCategory Category { get; set; }
    public int PointsCost { get; set; }
    public decimal? MonetaryValue { get; set; }
    public int? DiscountPercentage { get; set; }
    public decimal? DiscountAmount { get; set; }
    public int? FreeShippingThreshold { get; set; }
    public string EligibleTiers { get; set; } = "[]";
    public int StockQuantity { get; set; } = -1;
    public int MaxRedemptionsPerUser { get; set; } = -1;
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public bool IsActive { get; set; } = true;
    public bool IsFeatured { get; set; }
    public int Priority { get; set; } = 1;
    public string Terms { get; set; } = "[]";
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class RewardRedemption
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public int RewardId { get; set; }
    public string RewardName { get; set; } = string.Empty;
    public int PointsUsed { get; set; }
    public RedemptionStatus Status { get; set; }
    public string? CouponCode { get; set; }
    public string? QRCode { get; set; }
    public int? OrderId { get; set; }
    public DateTime RedeemedAt { get; set; }
    public DateTime? UsedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public string Metadata { get; set; } = "{}";
}

public class Badge
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? ImageUrl { get; set; }
    public BadgeCategory Category { get; set; }
    public BadgeRarity Rarity { get; set; }
    public int PointsReward { get; set; }
    public string Requirements { get; set; } = "[]";
    public bool IsActive { get; set; } = true;
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class UserBadge
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public int BadgeId { get; set; }
    public DateTime EarnedAt { get; set; }
    public string? Reason { get; set; }
}

public class Challenge
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? ImageUrl { get; set; }
    public ChallengeType Type { get; set; }
    public ChallengeDifficulty Difficulty { get; set; }
    public int PointsReward { get; set; }
    public string Requirements { get; set; } = "[]";
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int MaxParticipants { get; set; } = -1;
    public int CurrentParticipants { get; set; }
    public bool IsActive { get; set; } = true;
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class ChallengeProgress
{
    public int Id { get; set; }
    public int ChallengeId { get; set; }
    public string UserId { get; set; } = string.Empty;
    public int Progress { get; set; }
    public bool IsCompleted { get; set; }
    public string RequirementProgress { get; set; } = "{}";
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
}

public class ReferralProgram
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int ReferrerPoints { get; set; }
    public int RefereePoints { get; set; }
    public decimal? ReferrerCashback { get; set; }
    public decimal? RefereeCashback { get; set; }
    public int MaxReferrals { get; set; } = -1;
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public bool IsActive { get; set; } = true;
    public string Terms { get; set; } = "[]";
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class Referral
{
    public int Id { get; set; }
    public string ReferrerId { get; set; } = string.Empty;
    public string RefereeId { get; set; } = string.Empty;
    public string ReferralCode { get; set; } = string.Empty;
    public ReferralStatus Status { get; set; }
    public int PointsEarned { get; set; }
    public decimal CashbackEarned { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string Metadata { get; set; } = "{}";
}

public class LoyaltyEvent
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public LoyaltyEventType Type { get; set; }
    public double PointsMultiplier { get; set; } = 1.0;
    public int BonusPoints { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public string EligibleCategories { get; set; } = "[]";
    public string EligibleTiers { get; set; } = "[]";
    public bool IsActive { get; set; } = true;
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class PointsEarnRule
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public PointsEarnRuleType Type { get; set; }
    public int PointsPerUnit { get; set; }
    public decimal? MinimumAmount { get; set; }
    public decimal? MaximumAmount { get; set; }
    public string EligibleCategories { get; set; } = "[]";
    public string EligibleTiers { get; set; } = "[]";
    public int MaxPointsPerDay { get; set; } = -1;
    public int MaxPointsPerMonth { get; set; } = -1;
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public bool IsActive { get; set; } = true;
    public int Priority { get; set; } = 1;
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class LoyaltyNotification
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public LoyaltyNotificationType Type { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Data { get; set; } = "{}";
    public bool IsRead { get; set; }
    public DateTime? ReadAt { get; set; }
    public DateTime CreatedAt { get; set; }
}
