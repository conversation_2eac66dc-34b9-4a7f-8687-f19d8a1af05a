namespace NafaPlace.Recommendation.Domain.Entities;

public enum InteractionType
{
    View = 1,
    Click = 2,
    AddToCart = 3,
    Purchase = 4,
    Like = 5,
    Share = 6,
    Review = 7,
    Wishlist = 8
}

public class UserInteraction
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public int ProductId { get; set; }
    public InteractionType Type { get; set; }
    public double Weight { get; set; } = 1.0;
    public string? SessionId { get; set; }
    public string Context { get; set; } = "{}";
    public DateTime Timestamp { get; set; }
}

public class ProductSimilarity
{
    public int Id { get; set; }
    public int ProductId1 { get; set; }
    public int ProductId2 { get; set; }
    public decimal SimilarityScore { get; set; }
    public string Algorithm { get; set; } = string.Empty;
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class UserPreference
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string PreferenceType { get; set; } = string.Empty; // category, brand, price_range, etc.
    public string PreferenceValue { get; set; } = string.Empty;
    public decimal Score { get; set; }
    public decimal Confidence { get; set; }
    public string? Source { get; set; }
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class RecommendationModel
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Algorithm { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Parameters { get; set; } = "{}";
    public string ModelData { get; set; } = "{}";
    public bool IsActive { get; set; } = true;
    public decimal? Accuracy { get; set; }
    public decimal? Precision { get; set; }
    public decimal? Recall { get; set; }
    public decimal? F1Score { get; set; }
    public DateTime? LastTrainedAt { get; set; }
    public string TrainingData { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class RecommendationResult
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string RecommendedProducts { get; set; } = "[]"; // JSON array of product IDs
    public string Algorithm { get; set; } = string.Empty;
    public int? ModelId { get; set; }
    public string Context { get; set; } = "{}";
    public string Scores { get; set; } = "{}"; // JSON object with scores for each product
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
}

public class ABTestGroup
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Algorithm { get; set; } = string.Empty;
    public string Parameters { get; set; } = "{}";
    public decimal TrafficPercentage { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public bool IsActive { get; set; } = true;
    public string Metrics { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class UserABTestAssignment
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public int ABTestGroupId { get; set; }
    public DateTime AssignedAt { get; set; }
}
