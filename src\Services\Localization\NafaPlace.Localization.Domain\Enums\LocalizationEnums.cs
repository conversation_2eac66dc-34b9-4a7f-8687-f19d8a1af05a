namespace NafaPlace.Localization.Domain.Enums;

public enum TranslationStatus
{
    Draft,
    PendingTranslation,
    Translated,
    PendingReview,
    Reviewed,
    Approved,
    Published,
    Rejected,
    Outdated
}

public enum TranslationSource
{
    Manual,
    AutoTranslation,
    Import,
    API,
    Bulk,
    Migration
}

public enum TranslationPriority
{
    Low,
    Normal,
    High,
    Critical
}

public enum BatchStatus
{
    Created,
    InProgress,
    Completed,
    Failed,
    Cancelled
}

public enum AutoTranslationProvider
{
    Google,
    Microsoft,
    Amazon,
    DeepL,
    OpenAI,
    Custom
}

public enum QualityScore
{
    Poor = 1,
    Fair = 2,
    Good = 3,
    VeryGood = 4,
    Excellent = 5
}

public enum QualityIssueType
{
    Grammar,
    Spelling,
    Terminology,
    Consistency,
    Fluency,
    Accuracy,
    Formatting,
    Cultural
}

public enum QualitySeverity
{
    Info,
    Warning,
    Error,
    Critical
}

public enum WorkflowStatus
{
    Active,
    Inactive,
    Draft,
    Archived
}

public enum WorkflowStepType
{
    Translation,
    Review,
    Approval,
    QualityAssurance,
    AutoTranslation,
    Validation,
    Publishing
}

public enum ProjectStatus
{
    Planning,
    InProgress,
    Review,
    Completed,
    OnHold,
    Cancelled
}
