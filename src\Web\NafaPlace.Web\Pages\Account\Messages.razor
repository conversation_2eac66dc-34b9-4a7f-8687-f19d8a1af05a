@page "/account/messages"
@using Microsoft.AspNetCore.Authorization
@using NafaPlace.Web.Services
@attribute [Authorize]
@inject IChatEcommerceService ChatService
@inject IJSRuntime JSRuntime
@inject ILogger<Messages> Logger
@inject NavigationManager Navigation

<PageTitle>Mes Messages - NafaPlace</PageTitle>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-comments"></i> Mes Conversations</h2>
                <button class="btn btn-primary" @onclick="RefreshConversations">
                    <i class="fas fa-sync-alt"></i> Actualiser
                </button>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
            <p class="mt-2 text-muted">Chargement de vos conversations...</p>
        </div>
    }
    else if (conversations?.Any() == true)
    {
        <div class="row">
            <!-- Liste des conversations -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0"><i class="fas fa-list"></i> Conversations</h6>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            @foreach (var conversation in conversations)
                            {
                                <div class="list-group-item list-group-item-action @(selectedConversationId == conversation.Id ? "active" : "")" 
                                     @onclick="() => SelectConversation(conversation.Id)"
                                     style="cursor: pointer;">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">@conversation.Subject</h6>
                                        <small>@conversation.CreatedAt.ToString("dd/MM")</small>
                                    </div>
                                    <p class="mb-1 text-truncate">
                                        @if (!string.IsNullOrEmpty(conversation.SellerName))
                                        {
                                            <small><i class="fas fa-store"></i> @conversation.SellerName</small>
                                        }
                                        else
                                        {
                                            <small><i class="fas fa-headset"></i> Support NafaPlace</small>
                                        }
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="badge bg-@GetStatusColor(conversation.Status)">
                                            @GetStatusText(conversation.Status)
                                        </span>
                                        @if (conversation.UnreadCount > 0)
                                        {
                                            <span class="badge bg-danger rounded-pill">@conversation.UnreadCount</span>
                                        }
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- Zone de messages -->
            <div class="col-md-8">
                @if (selectedConversationId.HasValue && selectedConversation != null)
                {
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-0">@selectedConversation.Subject</h6>
                                    <small>
                                        @if (!string.IsNullOrEmpty(selectedConversation.SellerName))
                                        {
                                            <span>Conversation avec @selectedConversation.SellerName</span>
                                        }
                                        else
                                        {
                                            <span>Support NafaPlace</span>
                                        }
                                    </small>
                                </div>
                                <span class="badge bg-light text-dark">
                                    @GetStatusText(selectedConversation.Status)
                                </span>
                            </div>
                        </div>
                        <div class="card-body messages-container" style="height: 500px; overflow-y: auto;">
                            @if (messages?.Any() == true)
                            {
                                @foreach (var message in messages)
                                {
                                    <div class="message @(message.SenderType == "Customer" ? "message-customer" : "message-seller")">
                                        <div class="message-header">
                                            <strong>
                                                @if (message.SenderType == "Customer")
                                                {
                                                    <span>Vous</span>
                                                }
                                                else
                                                {
                                                    <span>@message.SenderName</span>
                                                }
                                            </strong>
                                            <small class="text-muted ms-2">
                                                @message.Timestamp.ToString("dd/MM/yyyy HH:mm")
                                            </small>
                                        </div>
                                        <div class="message-content">
                                            @message.Content
                                        </div>
                                    </div>
                                }
                            }
                            else
                            {
                                <div class="text-center py-5 text-muted">
                                    <i class="fas fa-comments fa-3x mb-3"></i>
                                    <p>Aucun message dans cette conversation</p>
                                </div>
                            }
                        </div>
                        <div class="card-footer">
                            <div class="input-group">
                                <textarea class="form-control" 
                                          placeholder="Tapez votre message..." 
                                          rows="2"
                                          @bind="newMessage"
                                          @onkeydown="HandleKeyDown"
                                          disabled="@isSending"></textarea>
                                <button class="btn btn-primary" 
                                        @onclick="SendMessage" 
                                        disabled="@(string.IsNullOrWhiteSpace(newMessage) || isSending)">
                                    @if (isSending)
                                    {
                                        <span class="spinner-border spinner-border-sm me-1"></span>
                                    }
                                    else
                                    {
                                        <i class="fas fa-paper-plane me-1"></i>
                                    }
                                    Envoyer
                                </button>
                            </div>
                            @if (!string.IsNullOrEmpty(errorMessage))
                            {
                                <div class="alert alert-danger mt-2 mb-0">
                                    @errorMessage
                                </div>
                            }
                        </div>
                    </div>
                }
                else
                {
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                            <h5>Sélectionnez une conversation</h5>
                            <p class="text-muted">Choisissez une conversation dans la liste pour voir les messages</p>
                        </div>
                    </div>
                }
            </div>
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h4>Aucune conversation</h4>
            <p class="text-muted">Vous n'avez pas encore de conversations avec les vendeurs.</p>
            <a href="/" class="btn btn-primary">
                <i class="fas fa-shopping-bag"></i> Découvrir les produits
            </a>
        </div>
    }
</div>

<style>
    .messages-container {
        background: #f8f9fa;
        padding: 20px;
    }

    .message {
        margin-bottom: 20px;
        padding: 12px 16px;
        border-radius: 8px;
        max-width: 70%;
    }

    .message-customer {
        background: #003366;
        color: white;
        margin-left: auto;
    }

    .message-seller {
        background: white;
        border: 1px solid #dee2e6;
        margin-right: auto;
    }

    .message-customer .text-muted {
        color: rgba(255, 255, 255, 0.8) !important;
    }

    .message-header {
        margin-bottom: 8px;
        font-size: 0.9rem;
    }

    .message-content {
        white-space: pre-wrap;
        word-wrap: break-word;
        line-height: 1.5;
    }

    .list-group-item.active {
        background-color: #003366;
        border-color: #003366;
    }
</style>

@code {
    private bool isLoading = true;
    private bool isSending = false;
    private string errorMessage = "";
    private string newMessage = "";

    private List<ConversationDto>? conversations;
    private int? selectedConversationId;
    private ConversationDto? selectedConversation;
    private List<MessageDto>? messages;

    protected override async Task OnInitializedAsync()
    {
        await LoadConversations();
    }

    private async Task LoadConversations()
    {
        try
        {
            isLoading = true;
            conversations = await ChatService.GetCustomerConversationsAsync();
            
            // Sélectionner la première conversation par défaut
            if (conversations?.Any() == true)
            {
                await SelectConversation(conversations.First().Id);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du chargement des conversations");
            errorMessage = "Erreur lors du chargement des conversations.";
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task SelectConversation(int conversationId)
    {
        try
        {
            selectedConversationId = conversationId;
            selectedConversation = conversations?.FirstOrDefault(c => c.Id == conversationId);
            
            if (selectedConversation != null)
            {
                messages = await ChatService.GetMessagesAsync(conversationId);
                await ChatService.MarkMessagesAsReadAsync(conversationId);
                selectedConversation.UnreadCount = 0;
                
                await JSRuntime.InvokeVoidAsync("scrollToBottom");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de la sélection de la conversation");
        }
    }

    private async Task SendMessage()
    {
        if (string.IsNullOrWhiteSpace(newMessage) || selectedConversation == null)
            return;

        isSending = true;
        errorMessage = "";

        try
        {
            var messageDto = new SendMessageDto
            {
                ConversationId = selectedConversation.Id,
                SenderId = selectedConversation.CustomerId,
                SenderName = selectedConversation.CustomerName,
                SenderType = "Customer",
                Content = newMessage,
                MessageType = "Text"
            };

            var success = await ChatService.SendMessageAsync(messageDto);
            
            if (success)
            {
                newMessage = "";
                messages = await ChatService.GetMessagesAsync(selectedConversation.Id);
                await JSRuntime.InvokeVoidAsync("scrollToBottom");
            }
            else
            {
                errorMessage = "Erreur lors de l'envoi du message. Veuillez réessayer.";
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'envoi du message");
            errorMessage = "Erreur lors de l'envoi du message. Veuillez réessayer.";
        }
        finally
        {
            isSending = false;
        }
    }

    private async Task HandleKeyDown(KeyboardEventArgs e)
    {
        if (e.Key == "Enter" && !e.ShiftKey)
        {
            await SendMessage();
        }
    }

    private async Task RefreshConversations()
    {
        await LoadConversations();
    }

    private string GetStatusColor(string status)
    {
        return status switch
        {
            "Open" => "success",
            "InProgress" => "primary",
            "Waiting" => "warning",
            "Closed" => "secondary",
            "Resolved" => "info",
            _ => "secondary"
        };
    }

    private string GetStatusText(string status)
    {
        return status switch
        {
            "Open" => "Ouvert",
            "InProgress" => "En cours",
            "Waiting" => "En attente",
            "Closed" => "Fermé",
            "Resolved" => "Résolu",
            _ => status
        };
    }
}

