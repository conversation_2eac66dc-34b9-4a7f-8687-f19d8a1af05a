@page "/chat/conversation/{ConversationId:int}"
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@inject IChatEcommerceService ChatService
@inject ILogger<ConversationDetails> <PERSON>gger
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>Détails de la conversation - NafaPlace Seller</PageTitle>

<div class="container-fluid py-4">
    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
            <p class="mt-3 text-muted">Chargement de la conversation...</p>
        </div>
    }
    else if (conversation == null)
    {
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Conversation introuvable.
            <a href="/chat/support" class="alert-link ms-2">Retour aux conversations</a>
        </div>
    }
    else
    {
        <!-- En-tête de la conversation -->
        <div class="card mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <button class="btn btn-link text-decoration-none p-0 mb-2" @onclick="GoBack">
                            <i class="fas fa-arrow-left me-2"></i>Retour aux conversations
                        </button>
                        <h4 class="mb-2">@conversation.Subject</h4>
                        <div class="d-flex gap-3 flex-wrap">
                            <span class="text-muted">
                                <i class="fas fa-user me-1"></i>
                                <strong>Client:</strong> @conversation.CustomerName
                            </span>
                            @if (!string.IsNullOrEmpty(conversation.CustomerEmail))
                            {
                                <span class="text-muted">
                                    <i class="fas fa-envelope me-1"></i>
                                    @conversation.CustomerEmail
                                </span>
                            }
                        </div>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-primary">
                            @conversation.Status
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Zone de messages -->
        <div class="card chat-container">
            <div class="card-body p-0">
                <!-- Messages -->
                <div class="messages-container">
                    @if (messages.Any())
                    {
                        @foreach (var message in messages)
                        {
                            <div class="message @(message.SenderType == "Seller" ? "message-seller" : "message-customer")">
                                <div class="message-header">
                                    <strong>
                                        @if (message.SenderType == "Seller")
                                        {
                                            <span>Vous</span>
                                        }
                                        else
                                        {
                                            <span>@conversation.CustomerName</span>
                                        }
                                    </strong>
                                    <small class="text-muted ms-2">
                                        @message.Timestamp.ToString("dd/MM/yyyy HH:mm")
                                    </small>
                                </div>
                                <div class="message-content">
                                    @message.Content
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5 text-muted">
                            <i class="fas fa-comments fa-3x mb-3"></i>
                            <p>Aucun message dans cette conversation</p>
                        </div>
                    }
                </div>

                <!-- Zone de saisie -->
                <div class="message-input-container">
                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger alert-dismissible fade show mb-2" role="alert">
                            @errorMessage
                            <button type="button" class="btn-close" @onclick="@(() => errorMessage = "")"></button>
                        </div>
                    }

                    <div class="d-flex gap-2">
                        <textarea class="form-control"
                                  rows="3"
                                  placeholder="Tapez votre message..."
                                  @bind="newMessage"
                                  disabled="@isSending"></textarea>
                        <button class="btn btn-primary"
                                @onclick="SendMessage"
                                disabled="@(string.IsNullOrWhiteSpace(newMessage) || isSending)">
                            @if (isSending)
                            {
                                <span class="spinner-border spinner-border-sm me-1"></span>
                            }
                            else
                            {
                                <i class="fas fa-paper-plane me-1"></i>
                            }
                            Envoyer
                        </button>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<style>
    .chat-container {
        height: calc(100vh - 350px);
        display: flex;
        flex-direction: column;
    }

    .messages-container {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
        background: #f8f9fa;
    }

    .message {
        margin-bottom: 20px;
        padding: 12px 16px;
        border-radius: 8px;
        max-width: 70%;
    }

    .message-customer {
        background: white;
        border: 1px solid #dee2e6;
        margin-right: auto;
    }

    .message-seller {
        background: #003366;
        color: white;
        margin-left: auto;
    }

        .message-seller .text-muted {
            color: rgba(255, 255, 255, 0.8) !important;
        }

    .message-header {
        margin-bottom: 8px;
        font-size: 0.9rem;
    }

    .message-content {
        white-space: pre-wrap;
        word-wrap: break-word;
        line-height: 1.5;
    }

    .message-input-container {
        padding: 20px;
        background: white;
        border-top: 1px solid #dee2e6;
    }
</style>

@code {
    [Parameter]
    public int ConversationId { get; set; }

    private bool isLoading = true;
    private bool isSending = false;
    private string errorMessage = "";
    private string newMessage = "";

    private ConversationDto? conversation;
    private List<MessageDto> messages = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (ConversationId > 0)
        {
            await LoadData();
        }
    }

    private async Task LoadData()
    {
        isLoading = true;
        try
        {
            var sellerId = await GetCurrentSellerIdAsync();
            conversation = await ChatService.GetConversationByIdAsync(ConversationId);

            if (conversation != null && conversation.SellerId == sellerId.ToString())
            {
                messages = await ChatService.GetMessagesAsync(ConversationId);
                await ChatService.MarkMessagesAsReadAsync(ConversationId);
            }
            else
            {
                conversation = null;
                errorMessage = "Vous n'avez pas accès à cette conversation.";
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, $"Erreur lors du chargement de la conversation {ConversationId}");
            errorMessage = "Erreur lors du chargement de la conversation.";
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task SendMessage()
    {
        if (string.IsNullOrWhiteSpace(newMessage) || conversation == null)
            return;

        isSending = true;
        errorMessage = "";

        try
        {
            var sellerId = await GetCurrentSellerIdAsync();
            var sellerName = await GetCurrentSellerNameAsync();

            var messageDto = new SendMessageDto
            {
                ConversationId = ConversationId,
                SenderId = sellerId.ToString(),
                SenderName = sellerName,
                SenderType = "Seller",
                Content = newMessage,
                MessageType = "Text"
            };

            await ChatService.SendMessageAsync(messageDto);
            messages = await ChatService.GetMessagesAsync(ConversationId);
            newMessage = "";
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'envoi du message");
            errorMessage = "Erreur lors de l'envoi du message. Veuillez réessayer.";
        }
        finally
        {
            isSending = false;
        }
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/chat/support");
    }

    private async Task<int> GetCurrentSellerIdAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        if (user.Identity?.IsAuthenticated == true)
        {
            var sellerIdClaim = user.FindFirst("SellerId");
            if (sellerIdClaim != null && int.TryParse(sellerIdClaim.Value, out int sellerId))
            {
                return sellerId;
            }
        }

        return 0;
    }

    private async Task<string> GetCurrentSellerNameAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        if (user.Identity?.IsAuthenticated == true)
        {
            return user.Identity.Name ?? "Vendeur";
        }

        return "Vendeur";
    }
}
