using System.ComponentModel.DataAnnotations;

namespace NafaPlace.Web.Models.Delivery
{
    public class ActiveDeliveryDto
    {
        public int Id { get; set; }
        public string OrderNumber { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string CustomerPhone { get; set; } = string.Empty;
        public string DeliveryAddress { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime EstimatedDelivery { get; set; }
        public decimal TotalAmount { get; set; }
        public string PaymentMethod { get; set; } = string.Empty;
        public string SpecialInstructions { get; set; } = string.Empty;
        public PositionDto CurrentPosition { get; set; } = new();
        public List<DeliveryItemDto> Items { get; set; } = new();
        public decimal DeliveryLatitude { get; set; }
        public decimal DeliveryLongitude { get; set; }
        public decimal DeliveryFee { get; set; }
        public DateTime ScheduledTime { get; set; }
        public string Priority { get; set; } = string.Empty;
        public bool PaymentRequired { get; set; }
    }

    public class PositionDto
    {
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public DateTime Timestamp { get; set; }
        public string Address { get; set; } = string.Empty;
        public decimal Accuracy { get; set; }
        public double Speed { get; set; }
    }

    public class DeliveryItemDto
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public decimal Price { get; set; }
        public string ImageUrl { get; set; } = string.Empty;
    }

    public class DeliveryPositionDto
    {
        public int DeliveryId { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public DateTime Timestamp { get; set; }
        public string Status { get; set; } = string.Empty;
        public double Speed { get; set; }
        public string Direction { get; set; } = string.Empty;
        public string DeliveryPersonPhoto { get; set; } = string.Empty;
        public string DeliveryPersonName { get; set; } = string.Empty;
        public string DeliveryPersonPhone { get; set; } = string.Empty;
        public string TransportMode { get; set; } = string.Empty;
        public PositionDto CurrentPosition { get; set; } = new();
    }

    public class EstimatedArrivalDto
    {
        public int DeliveryId { get; set; }
        public DateTime EstimatedArrival { get; set; }
        public int EstimatedMinutes { get; set; }
        public double DistanceRemaining { get; set; }
        public string TrafficCondition { get; set; } = string.Empty;
        public List<RouteStepDto> RouteSteps { get; set; } = new();
        public DateTime EstimatedTime { get; set; }
        public int MinutesRemaining { get; set; }
        public string DistanceUnit { get; set; } = "km";
        public double Distance { get; set; }
        public double Confidence { get; set; }
    }

    public class RouteStepDto
    {
        public string Instruction { get; set; } = string.Empty;
        public double Distance { get; set; }
        public int Duration { get; set; }
        public PositionDto StartPosition { get; set; } = new();
        public PositionDto EndPosition { get; set; } = new();
    }

    public class DeliveryTrackingDto
    {
        public int DeliveryId { get; set; }
        public string OrderNumber { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? EstimatedDelivery { get; set; }
        public DateTime? ActualDelivery { get; set; }
        public string DeliveryPersonName { get; set; } = string.Empty;
        public string DeliveryPersonPhone { get; set; } = string.Empty;
        public PositionDto CurrentPosition { get; set; } = new();
        public List<DeliveryEventDto> Events { get; set; } = new();
        public DateTime EventDate { get; set; }
        public string Description { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;
    }

    public class DeliveryEventDto
    {
        public DateTime Timestamp { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public PositionDto? Position { get; set; }
    }

    public class DeliveryStatsDto
    {
        public int TotalDeliveries { get; set; }
        public int CompletedDeliveries { get; set; }
        public int PendingDeliveries { get; set; }
        public int CancelledDeliveries { get; set; }
        public double AverageDeliveryTime { get; set; }
        public double OnTimeDeliveryRate { get; set; }
        public decimal TotalRevenue { get; set; }
    }
}
