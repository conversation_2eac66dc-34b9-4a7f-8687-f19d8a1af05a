using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using NafaPlace.Delivery.Application.Services;
using NafaPlace.Delivery.Domain.DTOs;
using NafaPlace.Delivery.Infrastructure.Hubs;
using System.Security.Claims;

namespace NafaPlace.Delivery.API.Controllers;

/// <summary>
/// Contrôleur pour le suivi GPS en temps réel des livraisons
/// Gère les positions des livreurs et la communication temps réel avec les clients
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
public class TrackingController : ControllerBase
{
    private readonly ITrackingService _trackingService;
    private readonly IHubContext<TrackingHub> _hubContext;
    private readonly ILogger<TrackingController> _logger;

    public TrackingController(
        ITrackingService trackingService,
        IHubContext<TrackingHub> hubContext,
        ILogger<TrackingController> logger)
    {
        _trackingService = trackingService;
        _hubContext = hubContext;
        _logger = logger;
    }

    /// <summary>
    /// Met à jour la position GPS du livreur (pour les livreurs uniquement)
    /// </summary>
    [HttpPost("position")]
    [Authorize(Roles = "DeliveryPerson")]
    public async Task<IActionResult> UpdatePosition([FromBody] UpdatePositionDto positionDto)
    {
        try
        {
            var deliveryPersonId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(deliveryPersonId))
                return Unauthorized();

            positionDto.DeliveryPersonId = deliveryPersonId;
            positionDto.Timestamp = DateTime.UtcNow;

            // Mise à jour de la position dans la base de données
            var result = await _trackingService.UpdateDeliveryPersonPositionAsync(positionDto);

            if (!result.Success)
                return BadRequest(new { Success = false, Message = result.ErrorMessage });

            // Diffusion en temps réel aux clients concernés
            await NotifyClientsOfPositionUpdate(deliveryPersonId, positionDto);

            return Ok(new { Success = true, Message = "Position mise à jour avec succès" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour de position pour le livreur {DeliveryPersonId}",
                User.Identity?.Name);
            return StatusCode(500, new { Success = false, Message = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Récupère la position actuelle d'une livraison (pour les clients)
    /// </summary>
    [HttpGet("delivery/{deliveryId}/position")]
    [Authorize]
    public async Task<IActionResult> GetDeliveryPosition(int deliveryId)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            // Vérification que l'utilisateur peut accéder à cette livraison
            var canAccess = await _trackingService.CanUserAccessDeliveryAsync(userId, deliveryId);
            if (!canAccess)
                return Forbid();

            var position = await _trackingService.GetDeliveryCurrentPositionAsync(deliveryId);
            if (position == null)
                return NotFound(new { Message = "Position non trouvée ou livraison terminée" });

            return Ok(position);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de position pour la livraison {DeliveryId}", deliveryId);
            return StatusCode(500, new { Success = false, Message = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Récupère l'historique complet des positions d'une livraison
    /// </summary>
    [HttpGet("delivery/{deliveryId}/history")]
    [Authorize]
    public async Task<IActionResult> GetDeliveryTrackingHistory(int deliveryId)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            var canAccess = await _trackingService.CanUserAccessDeliveryAsync(userId, deliveryId);
            if (!canAccess)
                return Forbid();

            var history = await _trackingService.GetDeliveryTrackingHistoryAsync(deliveryId);
            return Ok(history);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de l'historique pour la livraison {DeliveryId}", deliveryId);
            return StatusCode(500, new { Success = false, Message = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Démarre le suivi d'une livraison (pour les livreurs)
    /// </summary>
    [HttpPost("delivery/{deliveryId}/start")]
    [Authorize(Roles = "DeliveryPerson")]
    public async Task<IActionResult> StartDeliveryTracking(int deliveryId, [FromBody] StartTrackingDto startDto)
    {
        try
        {
            var deliveryPersonId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(deliveryPersonId))
                return Unauthorized();

            startDto.DeliveryId = deliveryId;
            startDto.DeliveryPersonId = deliveryPersonId;
            startDto.StartTime = DateTime.UtcNow;

            var result = await _trackingService.StartDeliveryTrackingAsync(startDto);

            if (!result.Success)
                return BadRequest(new { Success = false, Message = result.ErrorMessage });

            // Notification aux clients que la livraison a commencé
            await _hubContext.Clients.Group($"delivery_{deliveryId}")
                .SendAsync("DeliveryStarted", new { DeliveryId = deliveryId, StartTime = startDto.StartTime });

            return Ok(new { Success = true, Message = "Suivi de livraison démarré" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du démarrage du suivi pour la livraison {DeliveryId}", deliveryId);
            return StatusCode(500, new { Success = false, Message = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Termine le suivi d'une livraison (pour les livreurs)
    /// </summary>
    [HttpPost("delivery/{deliveryId}/complete")]
    [Authorize(Roles = "DeliveryPerson")]
    public async Task<IActionResult> CompleteDeliveryTracking(int deliveryId, [FromBody] CompleteTrackingDto completeDto)
    {
        try
        {
            var deliveryPersonId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(deliveryPersonId))
                return Unauthorized();

            completeDto.DeliveryId = deliveryId;
            completeDto.DeliveryPersonId = deliveryPersonId;
            completeDto.CompletionTime = DateTime.UtcNow;

            var result = await _trackingService.CompleteDeliveryTrackingAsync(completeDto);

            if (!result.Success)
                return BadRequest(new { Success = false, Message = result.ErrorMessage });

            // Notification aux clients que la livraison est terminée
            await _hubContext.Clients.Group($"delivery_{deliveryId}")
                .SendAsync("DeliveryCompleted", new {
                    DeliveryId = deliveryId,
                    CompletionTime = completeDto.CompletionTime,
                    FinalPosition = completeDto.FinalPosition
                });

            return Ok(new { Success = true, Message = "Livraison terminée avec succès" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la finalisation du suivi pour la livraison {DeliveryId}", deliveryId);
            return StatusCode(500, new { Success = false, Message = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Calcule le temps estimé d'arrivée basé sur la position actuelle
    /// </summary>
    [HttpGet("delivery/{deliveryId}/eta")]
    [Authorize]
    public async Task<IActionResult> GetEstimatedArrivalTime(int deliveryId)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            var canAccess = await _trackingService.CanUserAccessDeliveryAsync(userId, deliveryId);
            if (!canAccess)
                return Forbid();

            var eta = await _trackingService.CalculateEstimatedArrivalTimeAsync(deliveryId);
            return Ok(eta);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du calcul de l'ETA pour la livraison {DeliveryId}", deliveryId);
            return StatusCode(500, new { Success = false, Message = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Récupère toutes les livraisons actives pour un livreur
    /// </summary>
    [HttpGet("deliveries/active")]
    [Authorize(Roles = "DeliveryPerson")]
    public async Task<IActionResult> GetActiveDeliveries()
    {
        try
        {
            var deliveryPersonId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(deliveryPersonId))
                return Unauthorized();

            var activeDeliveries = await _trackingService.GetActiveDeliveriesForPersonAsync(deliveryPersonId);
            return Ok(activeDeliveries);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des livraisons actives pour {DeliveryPersonId}",
                User.Identity?.Name);
            return StatusCode(500, new { Success = false, Message = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Met à jour le statut d'une livraison avec géolocalisation
    /// </summary>
    [HttpPut("delivery/{deliveryId}/status")]
    [Authorize(Roles = "DeliveryPerson")]
    public async Task<IActionResult> UpdateDeliveryStatus(int deliveryId, [FromBody] UpdateDeliveryStatusDto statusDto)
    {
        try
        {
            var deliveryPersonId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(deliveryPersonId))
                return Unauthorized();

            statusDto.DeliveryId = deliveryId;
            statusDto.DeliveryPersonId = deliveryPersonId;
            statusDto.UpdateTime = DateTime.UtcNow;

            var result = await _trackingService.UpdateDeliveryStatusWithLocationAsync(statusDto);

            if (!result.Success)
                return BadRequest(new { Success = false, Message = result.ErrorMessage });

            // Diffusion du changement de statut aux clients
            await _hubContext.Clients.Group($"delivery_{deliveryId}")
                .SendAsync("DeliveryStatusUpdated", new {
                    DeliveryId = deliveryId,
                    Status = statusDto.Status,
                    Position = statusDto.CurrentPosition,
                    UpdateTime = statusDto.UpdateTime,
                    Message = statusDto.StatusMessage
                });

            return Ok(new { Success = true, Message = "Statut mis à jour avec succès" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour du statut pour la livraison {DeliveryId}", deliveryId);
            return StatusCode(500, new { Success = false, Message = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Rejoint le groupe de suivi en temps réel pour une livraison
    /// </summary>
    [HttpPost("delivery/{deliveryId}/subscribe")]
    [Authorize]
    public async Task<IActionResult> SubscribeToDeliveryUpdates(int deliveryId)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            var canAccess = await _trackingService.CanUserAccessDeliveryAsync(userId, deliveryId);
            if (!canAccess)
                return Forbid();

            // L'abonnement se fait via SignalR, pas via HTTP
            return Ok(new { Success = true, Message = "Utilisez SignalR pour vous abonner aux mises à jour" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'abonnement aux mises à jour pour la livraison {DeliveryId}", deliveryId);
            return StatusCode(500, new { Success = false, Message = "Erreur interne du serveur" });
        }
    }

    /// <summary>
    /// Méthode privée pour notifier les clients des mises à jour de position
    /// </summary>
    private async Task NotifyClientsOfPositionUpdate(string deliveryPersonId, UpdatePositionDto positionDto)
    {
        try
        {
            // Récupère toutes les livraisons actives pour ce livreur
            var activeDeliveries = await _trackingService.GetActiveDeliveriesForPersonAsync(deliveryPersonId);

            foreach (var delivery in activeDeliveries)
            {
                // Diffuse la nouvelle position à tous les clients suivant cette livraison
                await _hubContext.Clients.Group($"delivery_{delivery.Id}")
                    .SendAsync("PositionUpdated", new {
                        DeliveryId = delivery.Id,
                        Position = new {
                            Latitude = positionDto.Latitude,
                            Longitude = positionDto.Longitude,
                            Timestamp = positionDto.Timestamp,
                            Speed = positionDto.Speed,
                            Heading = positionDto.Heading
                        }
                    });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la notification des clients pour le livreur {DeliveryPersonId}", deliveryPersonId);
        }
    }
}