using NafaPlace.Notification.Application.DTOs;

namespace NafaPlace.Notification.Application.Services;

public interface IEmailService
{
    // Envoi d'emails
    Task<bool> SendAsync(EmailNotificationDto email);
    Task<int> SendBulkAsync(List<EmailNotificationDto> emails);
    Task<bool> SendTemplatedAsync(string to, string templateId, Dictionary<string, object> templateData);
    Task<int> SendBulkTemplatedAsync(List<string> recipients, string templateId, Dictionary<string, object> templateData);
    
    // Gestion des templates
    Task<int> CreateTemplateAsync(EmailTemplateDto template);
    Task<EmailTemplateDto?> GetTemplateAsync(string templateId);
    Task<List<EmailTemplateDto>> GetTemplatesAsync(string? category = null);
    Task<bool> UpdateTemplateAsync(EmailTemplateDto template);
    Task<bool> DeleteTemplateAsync(string templateId);
    Task<string> RenderTemplateAsync(string templateId, Dictionary<string, object> data);
    Task<bool> TestTemplateAsync(string templateId, Dictionary<string, object> testData, string testEmail);
    
    // Emails spécialisés
    Task SendWelcomeEmailAsync(string email, string userName, string activationLink);
    Task SendOrderConfirmationAsync(string email, int orderId, decimal amount, List<OrderItemDto> items);
    Task SendOrderStatusUpdateAsync(string email, int orderId, string status, string? trackingNumber = null);
    Task SendPaymentConfirmationAsync(string email, int orderId, decimal amount, string paymentMethod);
    Task SendPasswordResetAsync(string email, string resetLink, DateTime expiresAt);
    Task SendEmailVerificationAsync(string email, string verificationLink);
    Task SendInvoiceAsync(string email, int orderId, byte[] invoicePdf);
    Task SendPromotionAsync(string email, string title, string content, string? actionUrl = null);
    Task SendNewsletterAsync(List<string> emails, string subject, string content);
    Task SendStockAlertAsync(string email, string productName, int currentStock);
    Task SendReviewRequestAsync(string email, int orderId, List<OrderItemDto> items);
    Task SendAbandonedCartAsync(string email, List<CartItemDto> cartItems, string checkoutUrl);
    
    // Emails transactionnels
    Task SendAccountSuspensionAsync(string email, string reason, DateTime? suspensionEnd = null);
    Task SendAccountReactivationAsync(string email);
    Task SendSecurityAlertAsync(string email, string alertType, string details, DateTime timestamp);
    Task SendDataExportReadyAsync(string email, string downloadUrl, DateTime expiresAt);
    Task SendSubscriptionExpiryAsync(string email, string planName, DateTime expiryDate);
    
    // Gestion des listes et segments
    Task<bool> AddToMailingListAsync(string email, string listName, Dictionary<string, object>? metadata = null);
    Task<bool> RemoveFromMailingListAsync(string email, string listName);
    Task<List<string>> GetMailingListsAsync(string email);
    Task<int> SendToMailingListAsync(string listName, string subject, string content, string? templateId = null);
    Task<int> SendToSegmentAsync(string segmentName, string subject, string content, string? templateId = null);
    
    // Planification et automatisation
    Task<bool> ScheduleEmailAsync(EmailNotificationDto email, DateTime scheduledAt);
    Task<bool> ScheduleBulkEmailAsync(List<EmailNotificationDto> emails, DateTime scheduledAt);
    Task<bool> CancelScheduledEmailAsync(int scheduledEmailId);
    Task ProcessScheduledEmailsAsync();
    Task<List<ScheduledEmailDto>> GetScheduledEmailsAsync(DateTime? scheduledBefore = null);
    
    // Campagnes email
    Task<int> CreateCampaignAsync(EmailCampaignDto campaign);
    Task<EmailCampaignDto?> GetCampaignAsync(int campaignId);
    Task<List<EmailCampaignDto>> GetCampaignsAsync(string? status = null);
    Task<bool> StartCampaignAsync(int campaignId);
    Task<bool> PauseCampaignAsync(int campaignId);
    Task<bool> StopCampaignAsync(int campaignId);
    Task<EmailCampaignStatsDto> GetCampaignStatsAsync(int campaignId);
    
    // Analytics et tracking
    Task<EmailStatsDto> GetEmailStatsAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<List<EmailDeliveryDto>> GetDeliveryHistoryAsync(string? email = null, int limit = 50);
    Task<double> GetDeliveryRateAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<double> GetOpenRateAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<double> GetClickRateAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<Dictionary<string, int>> GetEmailStatsByTemplateAsync();
    Task TrackEmailOpenAsync(string trackingId);
    Task TrackEmailClickAsync(string trackingId, string url);
    Task TrackEmailBounceAsync(string trackingId, string reason);
    Task TrackEmailUnsubscribeAsync(string trackingId, string email);
    
    // Gestion des bounces et suppressions
    Task<List<string>> GetBouncedEmailsAsync(DateTime? since = null);
    Task<List<string>> GetUnsubscribedEmailsAsync(DateTime? since = null);
    Task<bool> AddToSuppressionListAsync(string email, string reason);
    Task<bool> RemoveFromSuppressionListAsync(string email);
    Task<bool> IsEmailSuppressedAsync(string email);
    Task<int> CleanupBouncedEmailsAsync(int daysToKeep = 30);
    
    // Configuration et validation
    Task<bool> ValidateEmailAsync(string email);
    Task<bool> ValidateTemplateAsync(string templateContent);
    Task<Dictionary<string, object>> GetEmailConfigAsync();
    Task<bool> UpdateEmailConfigAsync(Dictionary<string, object> config);
    Task<bool> TestEmailServiceAsync(string testEmail);
    Task<Dictionary<string, bool>> GetServiceHealthAsync();
    
    // Personnalisation et A/B testing
    Task<string> PersonalizeContentAsync(string content, Dictionary<string, object> userData);
    Task<int> CreateABTestAsync(ABTestEmailDto abTest);
    Task<ABTestResultDto> GetABTestResultsAsync(int abTestId);
    Task<string> GetOptimalVariantAsync(int abTestId);
    
    // Rapports et exports
    Task<byte[]> ExportEmailStatsAsync(DateTime startDate, DateTime endDate, string format = "csv");
    Task<byte[]> ExportCampaignReportAsync(int campaignId, string format = "pdf");
    Task<byte[]> ExportSubscriberListAsync(string listName, string format = "csv");
}

public class EmailTemplateDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string HtmlContent { get; set; } = string.Empty;
    public string TextContent { get; set; } = string.Empty;
    public List<string> Variables { get; set; } = new();
    public Dictionary<string, object> DefaultValues { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public string Language { get; set; } = "fr";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class OrderItemDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal TotalPrice { get; set; }
    public string? ImageUrl { get; set; }
}

public class CartItemDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public int Quantity { get; set; }
    public decimal Price { get; set; }
    public string? ImageUrl { get; set; }
}

public class ScheduledEmailDto
{
    public int Id { get; set; }
    public string To { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string? TemplateId { get; set; }
    public Dictionary<string, object> TemplateData { get; set; } = new();
    public DateTime ScheduledAt { get; set; }
    public bool IsProcessed { get; set; }
    public DateTime? ProcessedAt { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
}

public class EmailCampaignDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string? TemplateId { get; set; }
    public List<string> Recipients { get; set; } = new();
    public List<string> MailingLists { get; set; } = new();
    public string Status { get; set; } = "draft"; // draft, scheduled, running, paused, completed, cancelled
    public DateTime? ScheduledAt { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public Dictionary<string, object> Settings { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class EmailCampaignStatsDto
{
    public int CampaignId { get; set; }
    public int TotalSent { get; set; }
    public int TotalDelivered { get; set; }
    public int TotalOpened { get; set; }
    public int TotalClicked { get; set; }
    public int TotalBounced { get; set; }
    public int TotalUnsubscribed { get; set; }
    public double DeliveryRate { get; set; }
    public double OpenRate { get; set; }
    public double ClickRate { get; set; }
    public double BounceRate { get; set; }
    public double UnsubscribeRate { get; set; }
    public Dictionary<string, int> ClicksByUrl { get; set; } = new();
    public DateTime? LastUpdated { get; set; }
}

public class EmailStatsDto
{
    public int TotalSent { get; set; }
    public int TotalDelivered { get; set; }
    public int TotalOpened { get; set; }
    public int TotalClicked { get; set; }
    public int TotalBounced { get; set; }
    public int TotalUnsubscribed { get; set; }
    public double DeliveryRate { get; set; }
    public double OpenRate { get; set; }
    public double ClickRate { get; set; }
    public double BounceRate { get; set; }
    public Dictionary<string, int> ByTemplate { get; set; } = new();
    public Dictionary<string, int> ByDay { get; set; } = new();
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
}

public class EmailDeliveryDto
{
    public int Id { get; set; }
    public string To { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // sent, delivered, opened, clicked, bounced, failed
    public DateTime SentAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public DateTime? OpenedAt { get; set; }
    public DateTime? ClickedAt { get; set; }
    public string? ErrorMessage { get; set; }
    public string? TemplateId { get; set; }
    public int? CampaignId { get; set; }
    public string TrackingId { get; set; } = string.Empty;
}

public class ABTestEmailDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<EmailVariantDto> Variants { get; set; } = new();
    public List<string> Recipients { get; set; } = new();
    public double TestPercentage { get; set; } = 0.1; // 10% par défaut
    public string WinningCriteria { get; set; } = "open_rate"; // open_rate, click_rate, conversion_rate
    public DateTime? StartedAt { get; set; }
    public DateTime? EndedAt { get; set; }
    public string Status { get; set; } = "draft";
    public DateTime CreatedAt { get; set; }
}

public class EmailVariantDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public double TrafficPercentage { get; set; }
}

public class ABTestResultDto
{
    public int TestId { get; set; }
    public List<VariantResultDto> VariantResults { get; set; } = new();
    public string? WinningVariant { get; set; }
    public double ConfidenceLevel { get; set; }
    public bool IsStatisticallySignificant { get; set; }
    public DateTime LastUpdated { get; set; }
}

public class VariantResultDto
{
    public string VariantId { get; set; } = string.Empty;
    public string VariantName { get; set; } = string.Empty;
    public int TotalSent { get; set; }
    public int TotalOpened { get; set; }
    public int TotalClicked { get; set; }
    public double OpenRate { get; set; }
    public double ClickRate { get; set; }
    public double ConversionRate { get; set; }
}
