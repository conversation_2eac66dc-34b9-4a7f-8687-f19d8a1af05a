@page "/analytics/dashboard"
@using NafaPlace.SellerPortal.Models.Statistics
@using NafaPlace.SellerPortal.Services
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.JSInterop
@using System.IdentityModel.Tokens.Jwt
@inject IStatisticsService StatisticsService
@inject IJSRuntime JSRuntime
@inject IAuthService AuthService
@inject ILogger<AdvancedDashboard> Logger
@attribute [Authorize]

<PageTitle>Dashboard Analytics Avancé - Vendeur</PageTitle>

<div class="container-fluid px-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-tachometer-alt text-primary me-2"></i>
                        Dashboard Analytics Avancé
                    </h1>
                    <p class="text-muted mb-0">Analyse approfondie de vos performances de vente</p>
                </div>
                <div class="d-flex gap-2">
                    <div class="dropdown">
                        <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-calendar me-2"></i>@selectedPeriod
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" @onclick='() => ChangePeriod("7 derniers jours")'>7 derniers jours</a></li>
                            <li><a class="dropdown-item" @onclick='() => ChangePeriod("30 derniers jours")'>30 derniers jours</a></li>
                            <li><a class="dropdown-item" @onclick='() => ChangePeriod("3 derniers mois")'>3 derniers mois</a></li>
                            <li><a class="dropdown-item" @onclick='() => ChangePeriod("6 derniers mois")'>6 derniers mois</a></li>
                        </ul>
                    </div>
                    <button class="btn btn-primary" @onclick="RefreshData">
                        <i class="fas fa-sync-alt me-2"></i>Actualiser
                    </button>
                    <button class="btn btn-success" @onclick="ExportReport">
                        <i class="fas fa-download me-2"></i>Exporter
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
            <p class="mt-3 text-muted">Chargement des analytics...</p>
        </div>
    }
    else
    {
        <!-- KPIs principaux -->
        <div class="row g-4 mb-4">
            <div class="col-xl-3 col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                                    <i class="fas fa-coins text-primary fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="text-muted mb-1">Revenus</h6>
                                <h4 class="mb-0">@FormatCurrency(totalRevenue)</h4>
                                <small class="text-success">
                                    <i class="fas fa-arrow-up me-1"></i>+@revenueGrowth.ToString("F1")%
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-success bg-opacity-10 rounded-3 p-3">
                                    <i class="fas fa-shopping-bag text-success fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="text-muted mb-1">Ventes</h6>
                                <h4 class="mb-0">@totalSales.ToString("N0")</h4>
                                <small class="text-success">
                                    <i class="fas fa-arrow-up me-1"></i>+@salesGrowth.ToString("F1")%
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-info bg-opacity-10 rounded-3 p-3">
                                    <i class="fas fa-eye text-info fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="text-muted mb-1">Vues Produits</h6>
                                <h4 class="mb-0">@productViews.ToString("N0")</h4>
                                <small class="text-success">
                                    <i class="fas fa-arrow-up me-1"></i>+@viewsGrowth.ToString("F1")%
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                                    <i class="fas fa-percentage text-warning fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="text-muted mb-1">Taux Conversion</h6>
                                <h4 class="mb-0">@conversionRate.ToString("F1")%</h4>
                                <small class="text-success">
                                    <i class="fas fa-arrow-up me-1"></i>+@conversionGrowth.ToString("F1")%
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Graphiques principaux -->
        <div class="row g-4 mb-4">
            <div class="col-xl-8">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-transparent border-0 pb-0">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-area text-primary me-2"></i>
                            Évolution des Revenus
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="revenueChart" height="300"></canvas>
                    </div>
                </div>
            </div>

            <div class="col-xl-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-transparent border-0 pb-0">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            Recommandations IA
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (aiRecommendations?.Any() == true)
                        {
                            @foreach (var recommendation in aiRecommendations.Take(3))
                            {
                                <div class="alert alert-@GetRecommendationAlertClass(recommendation.Priority) border-0 mb-3">
                                    <div class="d-flex align-items-start">
                                        <i class="fas @GetRecommendationIcon(recommendation.Type) me-2 mt-1"></i>
                                        <div class="flex-grow-1">
                                            <h6 class="alert-heading mb-1">@recommendation.Title</h6>
                                            <p class="mb-0 small">@recommendation.Description</p>
                                            <div class="mt-2">
                                                <span class="badge bg-light text-dark">
                                                    Impact: @recommendation.Impact
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-robot fs-1 mb-3"></i>
                                <p>Génération de recommandations...</p>
                                <button class="btn btn-outline-primary btn-sm" @onclick="GenerateRecommendations">
                                    <i class="fas fa-magic me-2"></i>Générer
                                </button>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance des produits -->
        <div class="row g-4 mb-4">
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-transparent border-0 pb-0">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-trophy text-warning me-2"></i>
                            Top Produits
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (topProducts?.Any() == true)
                        {
                            @foreach (var product in topProducts.Take(5))
                            {
                                <div class="d-flex align-items-center mb-3">
                                    <div class="flex-shrink-0">
                                        <div class="bg-light rounded p-2">
                                            <i class="fas fa-box text-muted"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-1">@product.Name</h6>
                                        <small class="text-muted">@product.Sales ventes</small>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold">@FormatCurrency(product.Revenue)</div>
                                        <small class="text-success">+@product.Growth.ToString("F1")%</small>
                                    </div>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-box fs-1 mb-3"></i>
                                <p>Aucune donnée disponible</p>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-transparent border-0 pb-0">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-pie text-info me-2"></i>
                            Répartition des Ventes
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="salesDistributionChart" height="250"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Métriques avancées -->
        <div class="row g-4">
            <div class="col-lg-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent border-0 pb-0">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-star text-warning me-2"></i>
                            Satisfaction Client
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="display-4 text-warning mb-2">@averageRating.ToString("F1")</div>
                        <div class="text-muted mb-3">
                            @for (int i = 1; i <= 5; i++)
                            {
                                <i class="fas fa-star @(i <= Math.Round(averageRating) ? "text-warning" : "text-muted")"></i>
                            }
                        </div>
                        <p class="text-muted mb-0">Basé sur @totalReviews avis</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent border-0 pb-0">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-shipping-fast text-success me-2"></i>
                            Performance Livraison
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="display-4 text-success mb-2">@onTimeDelivery.ToString("F0")%</div>
                        <p class="text-muted mb-0">Livraisons à temps</p>
                        <small class="text-muted">Temps moyen: @avgDeliveryTime jours</small>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent border-0 pb-0">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-undo text-danger me-2"></i>
                            Taux de Retour
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="display-4 text-danger mb-2">@returnRate.ToString("F1")%</div>
                        <p class="text-muted mb-0">Produits retournés</p>
                        <small class="text-success">
                            <i class="fas fa-arrow-down me-1"></i>-@returnReduction.ToString("F1")% ce mois
                        </small>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private string selectedPeriod = "30 derniers jours";
    private bool isLoading = false;
    private int _sellerId = 0;

    // KPIs
    private decimal totalRevenue = 0;
    private int totalSales = 0;
    private int productViews = 0;
    private decimal conversionRate = 0;
    private decimal revenueGrowth = 0;
    private decimal salesGrowth = 0;
    private decimal viewsGrowth = 0;
    private decimal conversionGrowth = 0;

    // Métriques avancées
    private decimal averageRating = 0;
    private int totalReviews = 0;
    private decimal onTimeDelivery = 0;
    private decimal avgDeliveryTime = 0;
    private decimal returnRate = 0;
    private decimal returnReduction = 0;

    // Données
    private List<AIRecommendationDto>? aiRecommendations;
    private List<TopProductDto>? topProducts;

    protected override async Task OnInitializedAsync()
    {
        await GetCurrentSeller();
        await LoadAnalyticsData();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await InitializeCharts();
        }
    }

    private async Task GetCurrentSeller()
    {
        try
        {
            var user = await AuthService.GetCurrentUserAsync();
            if (user != null)
            {
                _sellerId = user.Id;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de la récupération du vendeur");
        }
    }

    private async Task LoadAnalyticsData()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            // Simuler le chargement des données
            await Task.Delay(1000);

            // Données simulées pour le vendeur
            totalRevenue = 450_000m;
            totalSales = 234;
            productViews = 12_456;
            conversionRate = 4.2m;
            revenueGrowth = 18.5m;
            salesGrowth = 15.3m;
            viewsGrowth = 22.1m;
            conversionGrowth = 3.2m;

            // Métriques avancées
            averageRating = 4.3m;
            totalReviews = 89;
            onTimeDelivery = 92.5m;
            avgDeliveryTime = 2.8m;
            returnRate = 3.2m;
            returnReduction = 1.5m;

            // Recommandations IA
            aiRecommendations = new List<AIRecommendationDto>
            {
                new() { Title = "Optimiser les prix", Description = "Augmentez vos prix de 5-8% sur les produits populaires", Type = "pricing", Priority = "high", Impact = "Élevé" },
                new() { Title = "Stock faible", Description = "Réapprovisionnez vos produits les plus vendus", Type = "inventory", Priority = "medium", Impact = "Moyen" },
                new() { Title = "Nouvelle tendance", Description = "Les accessoires tech sont en forte demande", Type = "trend", Priority = "low", Impact = "Faible" }
            };

            // Top produits
            topProducts = new List<TopProductDto>
            {
                new() { Name = "Smartphone Samsung", Sales = 45, Revenue = 135_000m, Growth = 25.3m },
                new() { Name = "Écouteurs Bluetooth", Sales = 78, Revenue = 89_000m, Growth = 18.7m },
                new() { Name = "Chargeur Rapide", Sales = 123, Revenue = 67_000m, Growth = 32.1m },
                new() { Name = "Coque Protection", Sales = 89, Revenue = 45_000m, Growth = 12.5m },
                new() { Name = "Cable USB-C", Sales = 156, Revenue = 34_000m, Growth = 8.9m }
            };
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du chargement des analytics");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task ChangePeriod(string period)
    {
        selectedPeriod = period;
        await LoadAnalyticsData();
    }

    private async Task RefreshData()
    {
        await LoadAnalyticsData();
    }

    private async Task ExportReport()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Export du rapport en cours...");
    }

    private async Task GenerateRecommendations()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Génération de nouvelles recommandations...");
        await LoadAnalyticsData();
    }

    private async Task InitializeCharts()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("initializeSellerCharts");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'initialisation des graphiques");
        }
    }

    private string FormatCurrency(decimal amount)
    {
        return amount.ToString("N0") + " GNF";
    }

    private string GetRecommendationAlertClass(string priority)
    {
        return priority switch
        {
            "high" => "danger",
            "medium" => "warning",
            "low" => "info",
            _ => "secondary"
        };
    }

    private string GetRecommendationIcon(string type)
    {
        return type switch
        {
            "pricing" => "fa-tag",
            "inventory" => "fa-boxes",
            "trend" => "fa-chart-line",
            "marketing" => "fa-bullhorn",
            _ => "fa-lightbulb"
        };
    }

    // Classes DTO
    public class AIRecommendationDto
    {
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Type { get; set; } = "";
        public string Priority { get; set; } = "";
        public string Impact { get; set; } = "";
    }

    public class TopProductDto
    {
        public string Name { get; set; } = "";
        public int Sales { get; set; }
        public decimal Revenue { get; set; }
        public decimal Growth { get; set; }
    }
}
