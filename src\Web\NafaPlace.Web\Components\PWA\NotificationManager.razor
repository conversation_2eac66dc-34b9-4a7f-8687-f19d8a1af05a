@using Microsoft.JSInterop
@inject IJSRuntime JSRuntime

<div class="notification-permission-banner @(ShowPermissionBanner ? "show" : "hide")">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-auto">
                <i class="fas fa-bell text-primary fa-2x"></i>
            </div>
            <div class="col">
                <h6 class="mb-1">Activez les notifications</h6>
                <p class="mb-0 text-muted small">
                    Recevez des alertes sur vos commandes, offres spéciales et nouveautés
                </p>
            </div>
            <div class="col-auto">
                <button class="btn btn-primary btn-sm me-2" @onclick="RequestNotificationPermission">
                    <i class="fas fa-check me-1"></i>
                    Activer
                </button>
                <button class="btn btn-outline-secondary btn-sm" @onclick="DismissPermissionBanner">
                    Plus tard
                </button>
            </div>
        </div>
    </div>
</div>

<style>
    .notification-permission-banner {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background: white;
        border-bottom: 1px solid #dee2e6;
        padding: 1rem 0;
        z-index: 1040;
        transform: translateY(-100%);
        transition: transform 0.3s ease-in-out;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .notification-permission-banner.show {
        transform: translateY(0);
    }

    .notification-permission-banner.hide {
        transform: translateY(-100%);
    }

    @@media (max-width: 768px) {
        .notification-permission-banner .col {
            text-align: center;
            margin: 0.5rem 0;
        }

        .notification-permission-banner .row {
            flex-direction: column;
        }
    }
</style>

@code {
    [Parameter] public bool ShowPermissionBanner { get; set; }
    [Parameter] public EventCallback<bool> ShowPermissionBannerChanged { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JSRuntime.InvokeVoidAsync("notificationManager.init", DotNetObjectReference.Create(this));
        }
    }

    [JSInvokable]
    public async Task ShowNotificationBanner()
    {
        ShowPermissionBanner = true;
        await ShowPermissionBannerChanged.InvokeAsync(ShowPermissionBanner);
        StateHasChanged();
    }

    [JSInvokable]
    public async Task HideNotificationBanner()
    {
        ShowPermissionBanner = false;
        await ShowPermissionBannerChanged.InvokeAsync(ShowPermissionBanner);
        StateHasChanged();
    }

    private async Task RequestNotificationPermission()
    {
        await JSRuntime.InvokeVoidAsync("notificationManager.requestPermission");
        await DismissPermissionBanner();
    }

    private async Task DismissPermissionBanner()
    {
        ShowPermissionBanner = false;
        await ShowPermissionBannerChanged.InvokeAsync(ShowPermissionBanner);
        await JSRuntime.InvokeVoidAsync("localStorage.setItem", "notification-permission-asked", "true");
        StateHasChanged();
    }

    [JSInvokable]
    public async Task SendNotification(string title, string body, string icon = null, string url = null)
    {
        await JSRuntime.InvokeVoidAsync("notificationManager.sendNotification", title, body, icon, url);
    }
}

<script>
    window.notificationManager = {
        dotNetHelper: null,
        registration: null,

        init: async function (dotNetHelper) {
            this.dotNetHelper = dotNetHelper;

            // Register service worker if not already registered
            if ('serviceWorker' in navigator) {
                try {
                    this.registration = await navigator.serviceWorker.register('/sw.js');
                    console.log('Service Worker registered');
                } catch (error) {
                    console.error('Service Worker registration failed:', error);
                }
            }

            // Check if notification permission already asked
            const permissionAsked = localStorage.getItem('notification-permission-asked');
            if (permissionAsked || Notification.permission === 'granted' || Notification.permission === 'denied') {
                return;
            }

            // Show permission banner after a delay
            setTimeout(() => {
                this.dotNetHelper.invokeMethodAsync('ShowNotificationBanner');
            }, 3000);
        },

        requestPermission: async function () {
            if (!('Notification' in window)) {
                console.log('This browser does not support notifications');
                return false;
            }

            const permission = await Notification.requestPermission();

            if (permission === 'granted') {
                console.log('Notification permission granted');

                // Subscribe to push notifications
                await this.subscribeToPush();

                // Send welcome notification
                this.sendNotification(
                    'Notifications activées !',
                    'Vous recevrez maintenant les alertes importantes de NafaPlace',
                    '/images/logo.svg'
                );

                return true;
            } else {
                console.log('Notification permission denied');
                return false;
            }
        },

        subscribeToPush: async function () {
            if (!this.registration || !('pushManager' in this.registration)) {
                console.log('Push messaging is not supported');
                return;
            }

            try {
                // Check if already subscribed
                let subscription = await this.registration.pushManager.getSubscription();

                if (!subscription) {
                    // Subscribe to push notifications
                    subscription = await this.registration.pushManager.subscribe({
                        userVisibleOnly: true,
                        applicationServerKey: this.urlBase64ToUint8Array('YOUR_VAPID_PUBLIC_KEY') // Replace with actual VAPID key
                    });
                }

                // Send subscription to server
                await fetch('/api/notifications/subscribe', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(subscription)
                });

                console.log('Push subscription successful');
            } catch (error) {
                console.error('Failed to subscribe to push notifications:', error);
            }
        },

        sendNotification: function (title, body, icon = '/images/logo.svg', url = null) {
            if (Notification.permission === 'granted') {
                const options = {
                    body: body,
                    icon: icon,
                    badge: '/images/badge.png',
                    vibrate: [200, 100, 200],
                    data: {
                        url: url,
                        dateOfArrival: Date.now()
                    },
                    actions: [
                        {
                            action: 'view',
                            title: 'Voir',
                            icon: '/images/view-icon.png'
                        },
                        {
                            action: 'close',
                            title: 'Fermer',
                            icon: '/images/close-icon.png'
                        }
                    ]
                };

                const notification = new Notification(title, options);

                notification.onclick = function (event) {
                    event.preventDefault();
                    if (url) {
                        window.open(url, '_blank');
                    } else {
                        window.focus();
                    }
                    notification.close();
                };

                // Auto close after 5 seconds
                setTimeout(() => {
                    notification.close();
                }, 5000);
            }
        },

        urlBase64ToUint8Array: function (base64String) {
            const padding = '='.repeat((4 - base64String.length % 4) % 4);
            const base64 = (base64String + padding)
                .replace(/\-/g, '+')
                .replace(/_/g, '/');

            const rawData = window.atob(base64);
            const outputArray = new Uint8Array(rawData.length);

            for (let i = 0; i < rawData.length; ++i) {
                outputArray[i] = rawData.charCodeAt(i);
            }
            return outputArray;
        }
    };
</script>