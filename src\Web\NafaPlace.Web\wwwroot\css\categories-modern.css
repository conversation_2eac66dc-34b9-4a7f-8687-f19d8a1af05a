/* Categories Modern Section */
.categories-modern-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    position: relative;
    overflow: hidden;
}

/* Section Header */
.section-header {
    position: relative;
    z-index: 2;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #E73C30, #F96302);
    color: white;
    padding: 8px 20px;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1rem;
    box-shadow: 0 4px 15px rgba(231, 60, 48, 0.3);
}

.section-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: #003366;
    margin-bottom: 1rem;
}

.text-gradient {
    background: linear-gradient(135deg, #F96302, #E73C30);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #6c757d;
    max-width: 600px;
    margin: 0 auto;
}

/* Loading Animation */
.loading-animation {
    padding: 3rem 0;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #F96302;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Categories Grid */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

/* Category Card Modern */
.category-card-modern {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.category-card-modern:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.category-card-modern:hover .category-hover-effect {
    opacity: 1;
    transform: scale(1);
}

/* Featured Category */
.featured-category {
    background: linear-gradient(135deg, #003366 0%, #004488 100%);
    color: white;
    grid-column: span 2;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.featured-category .category-title {
    color: white;
    font-size: 1.8rem;
}

.featured-category .category-description {
    color: rgba(255, 255, 255, 0.9);
}

.featured-category .stat-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Category Icon Container */
.category-icon-container {
    position: relative;
    width: 80px;
    height: 80px;
    margin-bottom: 1.5rem;
}

.category-icon-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #F96302, #E73C30);
    border-radius: 50%;
    opacity: 0.1;
    transition: all 0.3s ease;
}

.category-card-modern:hover .category-icon-bg {
    opacity: 0.2;
    transform: scale(1.1);
}

.category-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2rem;
    color: #F96302;
    transition: all 0.3s ease;
}

.category-card-modern:hover .category-icon {
    color: #E73C30;
    transform: translate(-50%, -50%) scale(1.1);
}

/* Category Image Container */
.category-image-container {
    position: relative;
    width: 100%;
    height: 120px;
    margin-bottom: 1.5rem;
    border-radius: 15px;
    overflow: hidden;
}

.category-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.4s ease;
}

.category-card-modern:hover .category-image {
    transform: scale(1.1);
}

.category-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(231, 60, 48, 0.8), rgba(249, 99, 2, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.category-card-modern:hover .category-overlay {
    opacity: 1;
}

.category-arrow {
    color: white;
    font-size: 2rem;
    transform: translateX(-20px);
    transition: all 0.3s ease;
}

.category-card-modern:hover .category-arrow {
    transform: translateX(0);
}

/* Category Content */
.category-content {
    text-align: center;
}

.category-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: #003366;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.category-card-modern:hover .category-title {
    color: #F96302;
}

.category-description {
    font-size: 0.95rem;
    color: #6c757d;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.category-stats {
    margin-top: 1rem;
}

.stat-badge {
    display: inline-block;
    background: #f8f9fa;
    color: #495057;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.category-card-modern:hover .stat-badge {
    background: #F96302;
    color: white;
    border-color: #F96302;
}

/* Hover Effect */
.category-hover-effect {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(249, 99, 2, 0.05), rgba(231, 60, 48, 0.05));
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.4s ease;
    border-radius: 20px;
    pointer-events: none;
}

/* Floating Shapes */
.floating-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.shape {
    position: absolute;
    border-radius: 50%;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, #F96302, #E73C30);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    background: linear-gradient(135deg, #003366, #004488);
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-20px) rotate(120deg);
    }
    66% {
        transform: translateY(10px) rotate(240deg);
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .featured-category {
        grid-column: span 1;
    }
}

@media (max-width: 768px) {
    .categories-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .category-card-modern {
        padding: 1.5rem;
    }
    
    .featured-category {
        min-height: 150px;
        flex-direction: column;
        text-align: center;
    }
    
    .category-icon-container {
        width: 60px;
        height: 60px;
        margin: 0 auto 1rem;
    }
    
    .category-icon {
        font-size: 1.5rem;
    }
    
    .category-image-container {
        height: 100px;
    }
}

@media (max-width: 576px) {
    .categories-modern-section {
        padding: 3rem 0;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
    
    .section-subtitle {
        font-size: 1rem;
    }
    
    .category-card-modern {
        padding: 1.25rem;
    }
    
    .category-title {
        font-size: 1.1rem;
    }
    
    .category-description {
        font-size: 0.9rem;
    }
}
