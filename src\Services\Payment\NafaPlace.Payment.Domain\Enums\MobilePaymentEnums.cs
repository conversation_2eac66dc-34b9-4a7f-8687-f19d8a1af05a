namespace NafaPlace.Payment.Domain.Enums;

/// <summary>
/// Statut des paiements mobiles
/// </summary>
public enum MobilePaymentStatus
{
    Pending = 1,
    Processing = 2,
    Completed = 3,
    Failed = 4,
    Cancelled = 5,
    Expired = 6,
    Refunded = 7
}

/// <summary>
/// Fournisseurs de paiement mobile en Guinée
/// </summary>
public enum MobilePaymentProvider
{
    OrangeMoney = 1,
    MTNMoney = 2,
    MtnMoney = 2,  // Alias pour MTNMoney
    MobicashOrange = 3,
    MoovMoney = 4,
    ExpressPay = 5,
    Other = 99
}

/// <summary>
/// Types de transactions mobiles
/// </summary>
public enum MobileTransactionType
{
    Payment = 1,
    Refund = 2,
    Transfer = 3,
    Withdrawal = 4,
    Deposit = 5
}

/// <summary>
/// Modes de confirmation pour les paiements mobiles
/// </summary>
public enum MobilePaymentConfirmationMode
{
    Auto = 1,      // Confirmation automatique
    Manual = 2,    // Confirmation manuelle par le client
    USSD = 3,      // Confirmation via code USSD
    SMS = 4        // Confirmation par SMS
}