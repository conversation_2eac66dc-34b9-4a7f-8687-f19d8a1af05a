using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using NafaPlace.Loyalty.Infrastructure.Data;

namespace NafaPlace.Loyalty.Infrastructure
{
    public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<LoyaltyDbContext>
    {
        public LoyaltyDbContext CreateDbContext(string[] args)
        {
            var connectionString = "Host=localhost;Port=5432;Database=NafaPlace.Loyalty;Username=postgres;Password=*****************";
            var optionsBuilder = new DbContextOptionsBuilder<LoyaltyDbContext>();
            optionsBuilder.UseNpgsql(connectionString);

            return new LoyaltyDbContext(optionsBuilder.Options);
        }
    }
}
