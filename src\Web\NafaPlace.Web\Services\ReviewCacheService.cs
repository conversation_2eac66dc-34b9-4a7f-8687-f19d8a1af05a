using NafaPlace.Reviews.DTOs;
using System.Collections.Concurrent;

namespace NafaPlace.Web.Services
{
    public interface IReviewCacheService
    {
        Task<ReviewSummaryDto?> GetCachedReviewSummaryAsync(int productId);
        Task SetCachedReviewSummaryAsync(int productId, ReviewSummaryDto summary);
        void ClearCache();
        void ClearProductCache(int productId);
    }

    public class ReviewCacheService : IReviewCacheService
    {
        private readonly ConcurrentDictionary<int, CachedReviewSummary> _cache = new();
        private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(5); // Cache pendant 5 minutes

        public async Task<ReviewSummaryDto?> GetCachedReviewSummaryAsync(int productId)
        {
            if (_cache.TryGetValue(productId, out var cachedItem))
            {
                if (DateTime.UtcNow - cachedItem.CachedAt < _cacheExpiry)
                {
                    return cachedItem.Summary;
                }
                else
                {
                    // Cache expiré, le supprimer
                    _cache.TryRemove(productId, out _);
                }
            }

            return null;
        }

        public async Task SetCachedReviewSummaryAsync(int productId, ReviewSummaryDto summary)
        {
            var cachedItem = new CachedReviewSummary
            {
                Summary = summary,
                CachedAt = DateTime.UtcNow
            };

            _cache.AddOrUpdate(productId, cachedItem, (key, oldValue) => cachedItem);
        }

        public void ClearCache()
        {
            _cache.Clear();
        }

        public void ClearProductCache(int productId)
        {
            _cache.TryRemove(productId, out _);
        }

        private class CachedReviewSummary
        {
            public ReviewSummaryDto Summary { get; set; } = new();
            public DateTime CachedAt { get; set; }
        }
    }
}
