@using Microsoft.AspNetCore.SignalR.Client
@using NafaPlace.Web.Models.Delivery
@implements IAsyncDisposable
@inject IJSRuntime JSRuntime
@inject ILogger<RealTimeTrackingComponent> Logger

<div class="tracking-container">
    <!-- En-tête avec informations de livraison -->
    <div class="tracking-header">
        <div class="delivery-info">
            <h3>Suivi de livraison #@DeliveryId</h3>
            @if (currentPosition != null)
            {
                <div class="status-badge @GetStatusClass(currentPosition.Status)">
                    @GetStatusText(currentPosition.Status)
                </div>
            }
        </div>

        <div class="connection-status">
            @if (IsConnected)
            {
                <span class="connected">🟢 Connecté</span>
            }
            else
            {
                <span class="disconnected">🔴 Déconnecté</span>
            }
        </div>
    </div>

    <!-- Informations du livreur -->
    @if (currentPosition != null)
    {
        <div class="delivery-person-info">
            <div class="person-details">
                @if (!string.IsNullOrEmpty(currentPosition.DeliveryPersonPhoto))
                {
                    <img src="@currentPosition.DeliveryPersonPhoto" alt="Photo livreur" class="person-photo" />
                }
                else
                {
                    <div class="person-photo-placeholder">👤</div>
                }

                <div class="person-text">
                    <h4>@currentPosition.DeliveryPersonName</h4>
                    <p>📱 @currentPosition.DeliveryPersonPhone</p>
                    @if (!string.IsNullOrEmpty(currentPosition.TransportMode))
                    {
                        <p>🚴 @GetTransportModeText(currentPosition.TransportMode)</p>
                    }
                </div>
            </div>

            <div class="contact-actions">
                <button class="btn btn-outline-primary btn-sm" @onclick="CallDeliveryPerson">
                    📞 Appeler
                </button>
                <button class="btn btn-outline-secondary btn-sm" @onclick="SendMessage">
                    💬 Message
                </button>
            </div>
        </div>
    }

    <!-- ETA et informations de progression -->
    @if (estimatedArrival != null)
    {
        <div class="eta-section">
            <div class="eta-main">
                <h4>Arrivée estimée</h4>
                <div class="eta-time">@estimatedArrival.EstimatedTime.ToString("HH:mm")</div>
                <div class="eta-remaining">
                    Dans @estimatedArrival.MinutesRemaining minutes
                    • @Math.Round(estimatedArrival.DistanceRemaining, 1) @estimatedArrival.DistanceUnit
                </div>
            </div>

            <div class="eta-details">
                <div class="traffic-condition">
                    Trafic: @GetTrafficText(estimatedArrival.TrafficCondition)
                </div>
                <div class="confidence-bar">
                    <div class="confidence-fill" style="width: @(estimatedArrival.Confidence * 100)%"></div>
                </div>
                <small>Fiabilité: @Math.Round(estimatedArrival.Confidence * 100)%</small>
            </div>
        </div>
    }

    <!-- Carte de suivi -->
    <div class="map-container">
        <div id="tracking-map-@DeliveryId" class="tracking-map"></div>

        <!-- Contrôles de la carte -->
        <div class="map-controls">
            <button class="btn btn-sm btn-outline-primary" @onclick="CenterOnDeliveryPerson">
                🎯 Centrer sur livreur
            </button>
            <button class="btn btn-sm btn-outline-secondary" @onclick="ShowFullRoute">
                🗺️ Itinéraire complet
            </button>
            <button class="btn btn-sm btn-outline-info" @onclick="ToggleTrafficLayer">
                🚦 Trafic
            </button>
        </div>
    </div>

    <!-- Mises à jour de statut en temps réel -->
    <div class="status-updates">
        <h5>Mises à jour</h5>
        <div class="updates-list">
            @foreach (var update in statusUpdates.OrderByDescending(u => u.Timestamp))
            {
                <div class="update-item">
                    <div class="update-time">@update.Timestamp.ToString("HH:mm")</div>
                    <div class="update-content">
                        <strong>@update.Title</strong>
                        @if (!string.IsNullOrEmpty(update.Message))
                        {
                            <p>@update.Message</p>
                        }
                    </div>
                </div>
            }
        </div>
    </div>

    <!-- Messages avec le livreur -->
    @if (showMessagePanel)
    {
        <div class="message-panel">
            <div class="message-header">
                <h5>Messages avec @(currentPosition?.DeliveryPersonName ?? "le livreur")</h5>
                <button class="btn btn-sm btn-outline-secondary" @onclick="() => showMessagePanel = false">
                    ✕
                </button>
            </div>

            <div class="messages-list">
                @foreach (var message in messages.OrderBy(m => m.Timestamp))
                {
                    <div class="message @(message.FromDeliveryPerson ? "from-delivery" : "from-customer")">
                        <div class="message-content">@message.Content</div>
                        <div class="message-time">@message.Timestamp.ToString("HH:mm")</div>
                    </div>
                }
            </div>

            <div class="message-input">
                <div class="input-group">
                    <input type="text" @bind="newMessage" @onkeypress="HandleMessageKeyPress"
                           class="form-control" placeholder="Tapez votre message..." />
                    <button class="btn btn-primary" @onclick="SendMessageToDeliveryPerson">
                        Envoyer
                    </button>
                </div>
            </div>
        </div>
    }
</div>

@code {
    [Parameter] public int DeliveryId { get; set; }
    [Parameter] public EventCallback<string> OnError { get; set; }
    [Parameter] public EventCallback<DeliveryPositionDto> OnPositionUpdated { get; set; }

    private HubConnection? hubConnection;
    private DeliveryPositionDto? currentPosition;
    private EstimatedArrivalDto? estimatedArrival;
    private List<StatusUpdate> statusUpdates = new();
    private List<ChatMessage> messages = new();
    private bool showMessagePanel = false;
    private string newMessage = "";
    private bool isMapInitialized = false;
    private bool showTrafficLayer = false;

    public bool IsConnected =>
        hubConnection?.State == HubConnectionState.Connected;

    protected override async Task OnInitializedAsync()
    {
        // Initialiser la connexion SignalR
        await InitializeSignalRConnection();

        // Récupérer la position initiale
        await LoadInitialData();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender && !isMapInitialized)
        {
            // Initialiser la carte après le premier rendu
            await InitializeMap();
            isMapInitialized = true;
        }
    }

    private async Task InitializeSignalRConnection()
    {
        try
        {
            hubConnection = new HubConnectionBuilder()
                .WithUrl("/hubs/tracking")
                .WithAutomaticReconnect()
                .Build();

            // Gestionnaires d'événements
            hubConnection.On<object>("PositionUpdated", HandlePositionUpdated);
            hubConnection.On<object>("StatusChanged", OnStatusChanged);
            hubConnection.On<object>("MessageReceived", OnMessageReceived);
            hubConnection.On<object>("ETAUpdated", OnETAUpdated);
            hubConnection.On<object>("DeliveryStarted", OnDeliveryStarted);
            hubConnection.On<object>("DeliveryCompleted", OnDeliveryCompleted);
            hubConnection.On<string>("Error", OnSignalRError);

            await hubConnection.StartAsync();

            // Rejoindre le groupe de cette livraison
            await hubConnection.SendAsync("JoinDeliveryGroup", DeliveryId);

            Logger.LogInformation("Connexion SignalR établie pour la livraison {DeliveryId}", DeliveryId);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'initialisation de SignalR");
            await OnError.InvokeAsync("Erreur de connexion temps réel");
        }
    }

    private async Task LoadInitialData()
    {
        try
        {
            // Ici, vous appelleriez votre service pour récupérer les données initiales
            // Pour l'exemple, nous simulons un appel
            await RequestCurrentPosition();
            await RequestETA();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du chargement des données initiales");
        }
    }

    private async Task InitializeMap()
    {
        try
        {
            // Initialiser la carte avec JavaScript
            await JSRuntime.InvokeVoidAsync("initializeTrackingMap", $"tracking-map-{DeliveryId}");

            // Si nous avons une position, l'afficher
            if (currentPosition != null)
            {
                await UpdateMapPosition();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'initialisation de la carte");
        }
    }

    private async Task HandlePositionUpdated(object positionData)
    {
        try
        {
            // Ici, vous désérialiseriez les données de position
            // Pour l'exemple, nous simulons

            statusUpdates.Add(new StatusUpdate
            {
                Title = "Position mise à jour",
                Message = "Le livreur se déplace vers votre adresse",
                Timestamp = DateTime.Now
            });

            await UpdateMapPosition();
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de la mise à jour de position");
        }
    }

    private async Task OnStatusChanged(object statusData)
    {
        try
        {
            // Traiter le changement de statut
            statusUpdates.Add(new StatusUpdate
            {
                Title = "Statut mis à jour",
                Message = "Nouveau statut de livraison",
                Timestamp = DateTime.Now
            });

            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du traitement du changement de statut");
        }
    }

    private async Task OnMessageReceived(object messageData)
    {
        try
        {
            // Ajouter le nouveau message
            messages.Add(new ChatMessage
            {
                Content = "Message du livreur", // À extraire des données
                FromDeliveryPerson = true,
                Timestamp = DateTime.Now
            });

            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de la réception de message");
        }
    }

    private async Task OnETAUpdated(object etaData)
    {
        try
        {
            // Mettre à jour l'ETA
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de la mise à jour de l'ETA");
        }
    }

    private async Task OnDeliveryStarted(object data)
    {
        statusUpdates.Add(new StatusUpdate
        {
            Title = "Livraison démarrée",
            Message = "Votre livreur est en route!",
            Timestamp = DateTime.Now
        });

        await InvokeAsync(StateHasChanged);
    }

    private async Task OnDeliveryCompleted(object data)
    {
        statusUpdates.Add(new StatusUpdate
        {
            Title = "Livraison terminée",
            Message = "Votre commande a été livrée avec succès!",
            Timestamp = DateTime.Now
        });

        await InvokeAsync(StateHasChanged);
    }

    private async Task OnSignalRError(string error)
    {
        await OnError.InvokeAsync($"Erreur SignalR: {error}");
    }

    private async Task RequestCurrentPosition()
    {
        if (IsConnected)
        {
            await hubConnection!.SendAsync("RequestCurrentPosition", DeliveryId);
        }
    }

    private async Task RequestETA()
    {
        if (IsConnected)
        {
            await hubConnection!.SendAsync("RequestETA", DeliveryId);
        }
    }

    private async Task UpdateMapPosition()
    {
        if (isMapInitialized && currentPosition != null)
        {
            await JSRuntime.InvokeVoidAsync("updateDeliveryPersonPosition",
                $"tracking-map-{DeliveryId}",
                currentPosition.CurrentPosition.Latitude,
                currentPosition.CurrentPosition.Longitude);
        }
    }

    private async Task CenterOnDeliveryPerson()
    {
        if (isMapInitialized && currentPosition != null)
        {
            await JSRuntime.InvokeVoidAsync("centerMapOnPosition",
                $"tracking-map-{DeliveryId}",
                currentPosition.CurrentPosition.Latitude,
                currentPosition.CurrentPosition.Longitude);
        }
    }

    private async Task ShowFullRoute()
    {
        if (isMapInitialized)
        {
            await JSRuntime.InvokeVoidAsync("showFullRoute", $"tracking-map-{DeliveryId}");
        }
    }

    private async Task ToggleTrafficLayer()
    {
        showTrafficLayer = !showTrafficLayer;
        if (isMapInitialized)
        {
            await JSRuntime.InvokeVoidAsync("toggleTrafficLayer", $"tracking-map-{DeliveryId}", showTrafficLayer);
        }
    }

    private async Task CallDeliveryPerson()
    {
        if (currentPosition != null)
        {
            await JSRuntime.InvokeVoidAsync("makePhoneCall", currentPosition.DeliveryPersonPhone);
        }
    }

    private void SendMessage()
    {
        showMessagePanel = true;
    }

    private async Task SendMessageToDeliveryPerson()
    {
        if (!string.IsNullOrWhiteSpace(newMessage) && IsConnected)
        {
            // Ajouter le message localement
            messages.Add(new ChatMessage
            {
                Content = newMessage,
                FromDeliveryPerson = false,
                Timestamp = DateTime.Now
            });

            // Envoyer via SignalR (vous implémenterez cette méthode)
            // await hubConnection!.SendAsync("SendMessageToDeliveryPerson", DeliveryId, newMessage);

            newMessage = "";
            StateHasChanged();
        }
    }

    private async Task HandleMessageKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await SendMessageToDeliveryPerson();
        }
    }

    // Méthodes utilitaires
    private string GetStatusClass(string status) => status.ToLower() switch
    {
        "en_route" => "status-en-route",
        "arrived" => "status-arrived",
        "delivered" => "status-delivered",
        "delayed" => "status-delayed",
        _ => "status-default"
    };

    private string GetStatusText(string status) => status.ToLower() switch
    {
        "en_route" => "En route",
        "arrived" => "Arrivé",
        "delivered" => "Livré",
        "delayed" => "Retard",
        _ => "En cours"
    };

    private string GetTransportModeText(string mode) => mode.ToLower() switch
    {
        "bicycle" => "À vélo",
        "motorcycle" => "En moto",
        "car" => "En voiture",
        "walking" => "À pied",
        _ => mode
    };

    private string GetTrafficText(string condition) => condition.ToLower() switch
    {
        "light" => "Fluide 🟢",
        "moderate" => "Modéré 🟡",
        "heavy" => "Dense 🔴",
        _ => "Normal"
    };

    public async ValueTask DisposeAsync()
    {
        if (hubConnection is not null)
        {
            await hubConnection.DisposeAsync();
        }
    }

    // Classes helper
    private class StatusUpdate
    {
        public string Title { get; set; } = "";
        public string Message { get; set; } = "";
        public DateTime Timestamp { get; set; }
    }

    private class ChatMessage
    {
        public string Content { get; set; } = "";
        public bool FromDeliveryPerson { get; set; }
        public DateTime Timestamp { get; set; }
    }
}

<style>
.tracking-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.tracking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
}

.delivery-info h3 {
    margin: 0;
    font-size: 1.4rem;
}

.status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-top: 5px;
}

.status-en-route { background: #28a745; color: white; }
.status-arrived { background: #ffc107; color: #212529; }
.status-delivered { background: #17a2b8; color: white; }
.status-delayed { background: #dc3545; color: white; }
.status-default { background: #6c757d; color: white; }

.connection-status .connected { color: #28a745; }
.connection-status .disconnected { color: #dc3545; }

.delivery-person-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.person-details {
    display: flex;
    align-items: center;
    gap: 15px;
}

.person-photo, .person-photo-placeholder {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.person-photo-placeholder {
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #6c757d;
}

.person-text h4 {
    margin: 0 0 5px 0;
    color: #2c3e50;
}

.person-text p {
    margin: 2px 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.contact-actions {
    display: flex;
    gap: 10px;
}

.eta-section {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%);
    border-radius: 12px;
    margin-bottom: 20px;
    color: #2c3e50;
}

.eta-main h4 {
    margin: 0 0 10px 0;
    font-size: 1.1rem;
}

.eta-time {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.eta-remaining {
    font-size: 0.9rem;
    opacity: 0.8;
}

.eta-details {
    text-align: right;
}

.confidence-bar {
    width: 100px;
    height: 4px;
    background: rgba(255,255,255,0.3);
    border-radius: 2px;
    margin: 5px 0;
}

.confidence-fill {
    height: 100%;
    background: #28a745;
    border-radius: 2px;
    transition: width 0.3s ease;
}

.map-container {
    position: relative;
    margin-bottom: 20px;
}

.tracking-map {
    width: 100%;
    height: 400px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.map-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.status-updates {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.status-updates h5 {
    margin: 0 0 15px 0;
    color: #2c3e50;
}

.updates-list {
    max-height: 200px;
    overflow-y: auto;
}

.update-item {
    display: flex;
    gap: 15px;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.update-item:last-child {
    border-bottom: none;
}

.update-time {
    font-size: 0.8rem;
    color: #6c757d;
    min-width: 60px;
    font-weight: 600;
}

.update-content strong {
    color: #2c3e50;
    display: block;
    margin-bottom: 2px;
}

.update-content p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.message-panel {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 350px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    z-index: 1000;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.message-header h5 {
    margin: 0;
    color: #2c3e50;
}

.messages-list {
    height: 250px;
    overflow-y: auto;
    padding: 10px;
}

.message {
    margin-bottom: 10px;
    padding: 8px 12px;
    border-radius: 18px;
    max-width: 80%;
}

.message.from-delivery {
    background: #e3f2fd;
    align-self: flex-start;
}

.message.from-customer {
    background: #e8f5e8;
    align-self: flex-end;
    margin-left: auto;
}

.message-content {
    font-size: 0.9rem;
    line-height: 1.4;
}

.message-time {
    font-size: 0.7rem;
    color: #6c757d;
    margin-top: 2px;
}

.message-input {
    padding: 15px;
    border-top: 1px solid #eee;
}

/* Responsive Design */
@@media (max-width: 768px) {
    .tracking-container {
        padding: 10px;
    }

    .delivery-person-info {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .eta-section {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .eta-details {
        text-align: center;
    }

    .message-panel {
        bottom: 0;
        right: 0;
        left: 0;
        width: auto;
        border-radius: 12px 12px 0 0;
    }

    .map-controls {
        flex-direction: row;
        flex-wrap: wrap;
    }
}
</style>