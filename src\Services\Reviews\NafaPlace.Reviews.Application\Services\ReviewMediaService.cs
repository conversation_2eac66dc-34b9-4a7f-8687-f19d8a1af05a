using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NafaPlace.Reviews.Application.Interfaces;
using NafaPlace.Reviews.Application.Repositories;
using NafaPlace.Reviews.Domain.Models;
using NafaPlace.Reviews.DTOs;
using System.Drawing;
using System.Drawing.Imaging;

namespace NafaPlace.Reviews.Application.Services;

public class ReviewMediaService : IReviewMediaService
{
    private readonly IReviewRepository _reviewRepository;
    private readonly IReviewMediaRepository _mediaRepository;
    private readonly IConfiguration _configuration;
    private readonly ILogger<ReviewMediaService> _logger;

    private readonly string[] _allowedImageTypes = { ".jpg", ".jpeg", ".png", ".webp" };
    private readonly string[] _allowedVideoTypes = { ".mp4", ".webm", ".mov" };
    private readonly long _maxImageSize = 10 * 1024 * 1024; // 10MB
    private readonly long _maxVideoSize = 100 * 1024 * 1024; // 100MB
    private readonly int _maxImagesPerReview = 10;
    private readonly int _maxVideosPerReview = 3;

    public ReviewMediaService(
        IReviewRepository reviewRepository,
        IReviewMediaRepository mediaRepository,
        IConfiguration configuration,
        ILogger<ReviewMediaService> logger)
    {
        _reviewRepository = reviewRepository;
        _mediaRepository = mediaRepository;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<MediaProcessingResult> ProcessReviewMediaAsync(
        int reviewId,
        List<IFormFile>? images,
        List<IFormFile>? videos)
    {
        var result = new MediaProcessingResult { Success = true };

        try
        {
            // Get existing media count
            var existingMedia = await _mediaRepository.GetByReviewIdAsync(reviewId);
            var existingImageCount = existingMedia.Count(m => m.MediaType == MediaType.Image);
            var existingVideoCount = existingMedia.Count(m => m.MediaType == MediaType.Video);

            // Process images
            if (images?.Count > 0)
            {
                if (existingImageCount + images.Count > _maxImagesPerReview)
                {
                    result.Errors.Add($"Maximum {_maxImagesPerReview} images allowed per review");
                    result.Success = false;
                    return result;
                }

                foreach (var image in images)
                {
                    var imageResult = await ProcessImageAsync(image, reviewId);
                    if (imageResult.Success)
                    {
                        result.ProcessedMedia.AddRange(imageResult.ProcessedMedia);
                        result.ProcessedCount++;
                    }
                    else
                    {
                        result.Errors.AddRange(imageResult.Errors);
                        result.FailedCount++;
                    }
                }
            }

            // Process videos
            if (videos?.Count > 0)
            {
                if (existingVideoCount + videos.Count > _maxVideosPerReview)
                {
                    result.Errors.Add($"Maximum {_maxVideosPerReview} videos allowed per review");
                    result.Success = false;
                    return result;
                }

                foreach (var video in videos)
                {
                    var videoResult = await ProcessVideoAsync(video, reviewId);
                    if (videoResult.Success)
                    {
                        result.ProcessedMedia.AddRange(videoResult.ProcessedMedia);
                        result.ProcessedCount++;
                    }
                    else
                    {
                        result.Errors.AddRange(videoResult.Errors);
                        result.FailedCount++;
                    }
                }
            }

            // Update result success status
            result.Success = result.FailedCount == 0;

            _logger.LogInformation(
                "Processed {ProcessedCount} media files for review {ReviewId}, {FailedCount} failed",
                result.ProcessedCount, reviewId, result.FailedCount);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing media for review {ReviewId}", reviewId);
            result.Success = false;
            result.ErrorMessage = "An error occurred while processing media files";
            return result;
        }
    }

    public async Task<MediaProcessingResult> ProcessImageAsync(IFormFile image, int reviewId)
    {
        var result = new MediaProcessingResult();

        try
        {
            // Validate file
            if (!await ValidateImageFileAsync(image))
            {
                result.Errors.Add($"Invalid image file: {image.FileName}");
                return result;
            }

            // Generate unique filename
            var extension = Path.GetExtension(image.FileName).ToLowerInvariant();
            var fileName = $"review_{reviewId}_{Guid.NewGuid()}{extension}";
            var uploadPath = GetUploadPath("images");
            var filePath = Path.Combine(uploadPath, fileName);

            // Ensure directory exists
            Directory.CreateDirectory(uploadPath);

            // Process and save image
            using (var imageStream = image.OpenReadStream())
            using (var originalImage = Image.FromStream(imageStream))
            {
                // Resize if too large
                var processedImage = ResizeImageIfNeeded(originalImage);

                // Save optimized image
                var imageCodecInfo = GetEncoderInfo("image/jpeg");
                var encoderParameters = new EncoderParameters(1);
                encoderParameters.Param[0] = new EncoderParameter(Encoder.Quality, 85L);

                processedImage.Save(filePath, imageCodecInfo, encoderParameters);
                processedImage.Dispose();
            }

            // Create media entity
            var media = new ReviewMedia
            {
                ReviewId = reviewId,
                MediaType = MediaType.Image,
                FileName = fileName,
                FilePath = filePath,
                FileSize = new FileInfo(filePath).Length,
                MimeType = image.ContentType,
                UploadedAt = DateTime.UtcNow,
                IsProcessed = true
            };

            // Save to database
            await _mediaRepository.AddAsync(media);

            // Create DTO
            var mediaDto = new ReviewMediaDto
            {
                Id = media.Id,
                MediaType = MediaType.Image.ToString(),
                FileName = fileName,
                Url = GetMediaUrl(fileName, "images"),
                ThumbnailUrl = GetMediaUrl(fileName, "images"), // Same for images
                FileSize = media.FileSize,
                UploadedAt = media.UploadedAt
            };

            result.ProcessedMedia.Add(mediaDto);
            result.Success = true;

            _logger.LogInformation("Successfully processed image {FileName} for review {ReviewId}",
                fileName, reviewId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing image {FileName} for review {ReviewId}",
                image.FileName, reviewId);
            result.Errors.Add($"Failed to process image: {image.FileName}");
            return result;
        }
    }

    public async Task<MediaProcessingResult> ProcessVideoAsync(IFormFile video, int reviewId)
    {
        var result = new MediaProcessingResult();

        try
        {
            // Validate file
            if (!await ValidateVideoFileAsync(video))
            {
                result.Errors.Add($"Invalid video file: {video.FileName}");
                return result;
            }

            // Generate unique filename
            var extension = Path.GetExtension(video.FileName).ToLowerInvariant();
            var fileName = $"review_{reviewId}_{Guid.NewGuid()}{extension}";
            var uploadPath = GetUploadPath("videos");
            var filePath = Path.Combine(uploadPath, fileName);

            // Ensure directory exists
            Directory.CreateDirectory(uploadPath);

            // Save video file
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await video.CopyToAsync(stream);
            }

            // Generate thumbnail
            var thumbnailPath = await GenerateThumbnailAsync(filePath);

            // Create media entity
            var media = new ReviewMedia
            {
                ReviewId = reviewId,
                MediaType = MediaType.Video,
                FileName = fileName,
                FilePath = filePath,
                ThumbnailPath = thumbnailPath,
                FileSize = new FileInfo(filePath).Length,
                MimeType = video.ContentType,
                UploadedAt = DateTime.UtcNow,
                IsProcessed = true
            };

            // Save to database
            await _mediaRepository.AddAsync(media);

            // Create DTO
            var mediaDto = new ReviewMediaDto
            {
                Id = media.Id,
                MediaType = MediaType.Video.ToString(),
                FileName = fileName,
                Url = GetMediaUrl(fileName, "videos"),
                ThumbnailUrl = GetMediaUrl(Path.GetFileName(thumbnailPath), "thumbnails"),
                FileSize = media.FileSize,
                UploadedAt = media.UploadedAt
            };

            result.ProcessedMedia.Add(mediaDto);
            result.Success = true;

            _logger.LogInformation("Successfully processed video {FileName} for review {ReviewId}",
                fileName, reviewId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing video {FileName} for review {ReviewId}",
                video.FileName, reviewId);
            result.Errors.Add($"Failed to process video: {video.FileName}");
            return result;
        }
    }

    public async Task<string> GenerateThumbnailAsync(string videoPath)
    {
        try
        {
            var thumbnailPath = GetUploadPath("thumbnails");
            Directory.CreateDirectory(thumbnailPath);

            var thumbnailFileName = $"thumb_{Path.GetFileNameWithoutExtension(videoPath)}_{Guid.NewGuid()}.jpg";
            var fullThumbnailPath = Path.Combine(thumbnailPath, thumbnailFileName);

            // For now, create a placeholder thumbnail
            // In production, you would use FFmpeg or similar to extract a frame
            await CreatePlaceholderThumbnailAsync(fullThumbnailPath);

            return fullThumbnailPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating thumbnail for video {VideoPath}", videoPath);
            throw;
        }
    }

    public async Task<List<ReviewMediaDto>> GetReviewMediaAsync(int reviewId)
    {
        try
        {
            var media = await _mediaRepository.GetByReviewIdAsync(reviewId);
            return media.Select(m => new ReviewMediaDto
            {
                Id = m.Id,
                MediaType = m.MediaType.ToString(),
                FileName = m.FileName,
                Url = GetMediaUrl(m.FileName, m.MediaType == MediaType.Image ? "images" : "videos"),
                ThumbnailUrl = m.MediaType == MediaType.Video && !string.IsNullOrEmpty(m.ThumbnailPath)
                    ? GetMediaUrl(Path.GetFileName(m.ThumbnailPath), "thumbnails")
                    : GetMediaUrl(m.FileName, m.MediaType == MediaType.Image ? "images" : "videos"),
                FileSize = m.FileSize,
                UploadedAt = m.UploadedAt
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting media for review {ReviewId}", reviewId);
            throw;
        }
    }

    public async Task<bool> RemoveReviewMediaAsync(int reviewId, List<int> mediaIds)
    {
        try
        {
            var media = await _mediaRepository.GetByIdsAsync(mediaIds);
            var reviewMedia = media.Where(m => m.ReviewId == reviewId).ToList();

            foreach (var mediaItem in reviewMedia)
            {
                // Delete physical files
                await DeleteMediaFileAsync(mediaItem.FilePath);
                if (!string.IsNullOrEmpty(mediaItem.ThumbnailPath))
                {
                    await DeleteMediaFileAsync(mediaItem.ThumbnailPath);
                }

                // Remove from database
                await _mediaRepository.DeleteAsync(mediaItem.Id);
            }

            _logger.LogInformation("Removed {Count} media files for review {ReviewId}",
                reviewMedia.Count, reviewId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing media for review {ReviewId}", reviewId);
            return false;
        }
    }

    public async Task<bool> ValidateMediaFileAsync(IFormFile file)
    {
        if (file == null || file.Length == 0)
            return false;

        var extension = Path.GetExtension(file.FileName).ToLowerInvariant();

        // Check if it's an allowed image or video type
        if (_allowedImageTypes.Contains(extension))
        {
            return await ValidateImageFileAsync(file);
        }
        else if (_allowedVideoTypes.Contains(extension))
        {
            return await ValidateVideoFileAsync(file);
        }

        return false;
    }

    public async Task<bool> DeleteMediaFileAsync(string filePath)
    {
        try
        {
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
                _logger.LogInformation("Deleted media file: {FilePath}", filePath);
            }
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting media file: {FilePath}", filePath);
            return false;
        }
    }

    public async Task<ReviewMediaDto?> GetMediaByIdAsync(int mediaId)
    {
        try
        {
            var media = await _mediaRepository.GetByIdAsync(mediaId);
            if (media == null)
                return null;

            return new ReviewMediaDto
            {
                Id = media.Id,
                MediaType = media.MediaType.ToString(),
                FileName = media.FileName,
                Url = GetMediaUrl(media.FileName, media.MediaType == MediaType.Image ? "images" : "videos"),
                ThumbnailUrl = media.MediaType == MediaType.Video && !string.IsNullOrEmpty(media.ThumbnailPath)
                    ? GetMediaUrl(Path.GetFileName(media.ThumbnailPath), "thumbnails")
                    : GetMediaUrl(media.FileName, media.MediaType == MediaType.Image ? "images" : "videos"),
                FileSize = media.FileSize,
                UploadedAt = media.UploadedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting media {MediaId}", mediaId);
            throw;
        }
    }

    public async Task<bool> IsMediaOwnedByUserAsync(int mediaId, string userId)
    {
        try
        {
            var media = await _mediaRepository.GetByIdAsync(mediaId);
            if (media == null)
                return false;

            var review = await _reviewRepository.GetByIdAsync(media.ReviewId);
            return review?.UserId == userId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking media ownership for media {MediaId}", mediaId);
            return false;
        }
    }

    #region Private Helper Methods

    private async Task<bool> ValidateImageFileAsync(IFormFile file)
    {
        try
        {
            if (file.Length > _maxImageSize)
                return false;

            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            if (!_allowedImageTypes.Contains(extension))
                return false;

            // Validate that it's actually an image
            using (var stream = file.OpenReadStream())
            {
                try
                {
                    using (var image = Image.FromStream(stream))
                    {
                        // If we can create an Image object, it's a valid image
                        return true;
                    }
                }
                catch
                {
                    return false;
                }
            }
        }
        catch
        {
            return false;
        }
    }

    private async Task<bool> ValidateVideoFileAsync(IFormFile file)
    {
        try
        {
            if (file.Length > _maxVideoSize)
                return false;

            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            return _allowedVideoTypes.Contains(extension);
        }
        catch
        {
            return false;
        }
    }

    private Image ResizeImageIfNeeded(Image originalImage)
    {
        const int maxWidth = 1920;
        const int maxHeight = 1080;

        if (originalImage.Width <= maxWidth && originalImage.Height <= maxHeight)
        {
            return new Bitmap(originalImage);
        }

        var ratioX = (double)maxWidth / originalImage.Width;
        var ratioY = (double)maxHeight / originalImage.Height;
        var ratio = Math.Min(ratioX, ratioY);

        var newWidth = (int)(originalImage.Width * ratio);
        var newHeight = (int)(originalImage.Height * ratio);

        var resizedImage = new Bitmap(newWidth, newHeight);
        using (var graphics = Graphics.FromImage(resizedImage))
        {
            graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
            graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
            graphics.DrawImage(originalImage, 0, 0, newWidth, newHeight);
        }

        return resizedImage;
    }

    private ImageCodecInfo GetEncoderInfo(string mimeType)
    {
        var codecs = ImageCodecInfo.GetImageEncoders();
        return codecs.FirstOrDefault(codec => codec.MimeType == mimeType)
               ?? throw new NotSupportedException($"No encoder found for MIME type: {mimeType}");
    }

    private string GetUploadPath(string subfolder)
    {
        var basePath = _configuration["FileStorage:ReviewMediaPath"] ?? "wwwroot/uploads/reviews";
        return Path.Combine(basePath, subfolder);
    }

    private string GetMediaUrl(string fileName, string subfolder)
    {
        var baseUrl = _configuration["FileStorage:BaseUrl"] ?? "/uploads/reviews";
        return $"{baseUrl}/{subfolder}/{fileName}";
    }

    private async Task CreatePlaceholderThumbnailAsync(string thumbnailPath)
    {
        // Create a simple placeholder thumbnail
        using (var bitmap = new Bitmap(320, 180))
        using (var graphics = Graphics.FromImage(bitmap))
        {
            graphics.Clear(Color.LightGray);
            using (var brush = new SolidBrush(Color.DarkGray))
            using (var font = new Font("Arial", 16))
            {
                var text = "VIDEO";
                var textSize = graphics.MeasureString(text, font);
                var x = (bitmap.Width - textSize.Width) / 2;
                var y = (bitmap.Height - textSize.Height) / 2;
                graphics.DrawString(text, font, brush, x, y);
            }

            bitmap.Save(thumbnailPath, ImageFormat.Jpeg);
        }
    }

    #endregion
}