namespace NafaPlace.Reviews.DTOs;

/// <summary>
/// DTO pour les médias des reviews
/// </summary>
public class ReviewMediaDto
{
    public int Id { get; set; }
    public int ReviewId { get; set; }
    public string FileName { get; set; } = "";
    public string OriginalFileName { get; set; } = "";
    public string FileUrl { get; set; } = "";
    public string ContentType { get; set; } = "";
    public long FileSize { get; set; }
    public string MediaType { get; set; } = ""; // Image, Video, Audio
    public int? Width { get; set; }
    public int? Height { get; set; }
    public int? Duration { get; set; }
    public string? ThumbnailUrl { get; set; }
    public string? AltText { get; set; }
    public string? Caption { get; set; }
    public int DisplayOrder { get; set; }
    public DateTime CreatedAt { get; set; }

    // Alternative properties for compatibility
    public string Url
    {
        get => FileUrl;
        set => FileUrl = value;
    }

    public DateTime UploadedAt
    {
        get => CreatedAt;
        set => CreatedAt = value;
    }
}

/// <summary>
/// DTO pour l'upload de médias
/// </summary>
public class UploadMediaRequestDto
{
    public int ReviewId { get; set; }
    public string? Caption { get; set; }
    public string? AltText { get; set; }
    public int DisplayOrder { get; set; } = 0;
}

/// <summary>
/// DTO pour la réponse d'upload
/// </summary>
public class UploadMediaResponseDto
{
    public bool Success { get; set; }
    public string Message { get; set; } = "";
    public ReviewMediaDto? Media { get; set; }
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// DTO pour le résultat du traitement de médias
/// </summary>
public class MediaProcessingResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<ReviewMediaDto> ProcessedMedia { get; set; } = new();
    public int ProcessedCount { get; set; }
    public int FailedCount { get; set; }
}