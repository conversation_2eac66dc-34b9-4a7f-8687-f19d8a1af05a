using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.ChatEcommerce.Application.DTOs;
using NafaPlace.ChatEcommerce.Application.Services;

namespace NafaPlace.ChatEcommerce.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ConversationsController : ControllerBase
{
    private readonly IChatEcommerceService _chatService;
    private readonly ILogger<ConversationsController> _logger;

    public ConversationsController(IChatEcommerceService chatService, ILogger<ConversationsController> logger)
    {
        _chatService = chatService;
        _logger = logger;
    }

    /// <summary>
    /// Créer une nouvelle conversation
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<int>> CreateConversation([FromBody] CreateConversationDto dto)
    {
        try
        {
            var conversationId = await _chatService.CreateConversationAsync(dto);
            return Ok(conversationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de la conversation");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtenir une conversation par ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<ConversationDto>> GetConversation(int id)
    {
        try
        {
            var conversation = await _chatService.GetConversationAsync(id);
            if (conversation == null)
                return NotFound();

            return Ok(conversation);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de la conversation {ConversationId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtenir les conversations avec filtres
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<List<ConversationDto>>> GetConversations([FromQuery] ConversationFilterDto filter)
    {
        try
        {
            var conversations = await _chatService.GetConversationsAsync(filter);
            return Ok(conversations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des conversations");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtenir les conversations du client connecté
    /// </summary>
    [HttpGet("customer")]
    public async Task<ActionResult<List<ConversationDto>>> GetCustomerConversations()
    {
        try
        {
            var userId = User.FindFirst("sub")?.Value ?? User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(userId))
            {
                _logger.LogWarning("Impossible de récupérer l'ID de l'utilisateur depuis les claims");
                return Unauthorized("Utilisateur non authentifié");
            }

            _logger.LogInformation("Récupération des conversations pour le client {UserId}", userId);
            var conversations = await _chatService.GetUserConversationsAsync(userId);
            return Ok(conversations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des conversations du client");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtenir les conversations d'un utilisateur
    /// </summary>
    [HttpGet("user/{userId}")]
    public async Task<ActionResult<List<ConversationDto>>> GetUserConversations(string userId)
    {
        try
        {
            var conversations = await _chatService.GetUserConversationsAsync(userId);
            return Ok(conversations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des conversations de l'utilisateur {UserId}", userId);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtenir les conversations d'un vendeur
    /// </summary>
    [HttpGet("seller/{sellerId}")]
    public async Task<ActionResult<List<ConversationDto>>> GetSellerConversations(string sellerId)
    {
        try
        {
            var conversations = await _chatService.GetSellerConversationsAsync(sellerId);
            return Ok(conversations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des conversations du vendeur {SellerId}", sellerId);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtenir les conversations d'un produit
    /// </summary>
    [HttpGet("product/{productId}")]
    public async Task<ActionResult<List<ConversationDto>>> GetProductConversations(int productId)
    {
        try
        {
            var conversations = await _chatService.GetProductConversationsAsync(productId);
            return Ok(conversations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des conversations du produit {ProductId}", productId);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtenir les conversations d'une commande
    /// </summary>
    [HttpGet("order/{orderId}")]
    public async Task<ActionResult<List<ConversationDto>>> GetOrderConversations(int orderId)
    {
        try
        {
            var conversations = await _chatService.GetOrderConversationsAsync(orderId);
            return Ok(conversations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des conversations de la commande {OrderId}", orderId);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Mettre à jour le statut d'une conversation
    /// </summary>
    [HttpPut("{id}/status")]
    public async Task<ActionResult<bool>> UpdateConversationStatus(int id, [FromBody] UpdateStatusRequest request)
    {
        try
        {
            var result = await _chatService.UpdateConversationStatusAsync(id, request.Status, request.Reason);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour du statut de la conversation {ConversationId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Fermer une conversation
    /// </summary>
    [HttpPut("{id}/close")]
    public async Task<ActionResult<bool>> CloseConversation(int id, [FromBody] CloseConversationRequest request)
    {
        try
        {
            var result = await _chatService.CloseConversationAsync(id, request.ClosedBy, request.Reason);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la fermeture de la conversation {ConversationId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Rouvrir une conversation
    /// </summary>
    [HttpPut("{id}/reopen")]
    public async Task<ActionResult<bool>> ReopenConversation(int id)
    {
        try
        {
            var result = await _chatService.ReopenConversationAsync(id);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la réouverture de la conversation {ConversationId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Assigner une conversation à un vendeur
    /// </summary>
    [HttpPut("{id}/assign")]
    public async Task<ActionResult<bool>> AssignConversation(int id, [FromBody] AssignConversationRequest request)
    {
        try
        {
            var result = await _chatService.AssignConversationToSellerAsync(id, request.SellerId);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'assignation de la conversation {ConversationId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Marquer une conversation comme lue
    /// </summary>
    [HttpPut("{id}/read")]
    public async Task<ActionResult<bool>> MarkConversationAsRead(int id, [FromBody] MarkAsReadRequest request)
    {
        try
        {
            var result = await _chatService.MarkConversationAsReadAsync(id, request.UserId);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du marquage de la conversation {ConversationId} comme lue", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Rechercher des conversations
    /// </summary>
    [HttpGet("search")]
    public async Task<ActionResult<List<ConversationDto>>> SearchConversations([FromQuery] string query, [FromQuery] ConversationFilterDto? filter = null)
    {
        try
        {
            var conversations = await _chatService.SearchConversationsAsync(query, filter);
            return Ok(conversations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche des conversations");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Créer une demande de support produit
    /// </summary>
    [HttpPost("product-inquiry")]
    public async Task<ActionResult<int>> CreateProductInquiry([FromBody] ProductInquiryRequest request)
    {
        try
        {
            var conversationId = await _chatService.CreateProductInquiryAsync(
                request.CustomerId, 
                request.CustomerName, 
                request.ProductId, 
                request.Message);
            return Ok(conversationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de la demande de support produit");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Créer une demande de support commande
    /// </summary>
    [HttpPost("order-support")]
    public async Task<ActionResult<int>> CreateOrderSupport([FromBody] OrderSupportRequest request)
    {
        try
        {
            var conversationId = await _chatService.CreateOrderSupportAsync(
                request.CustomerId, 
                request.CustomerName, 
                request.OrderId, 
                request.Message, 
                request.IssueType);
            return Ok(conversationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de la demande de support commande");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }
}

// DTOs pour les requêtes
public class UpdateStatusRequest
{
    public string Status { get; set; } = string.Empty;
    public string? Reason { get; set; }
}

public class CloseConversationRequest
{
    public string ClosedBy { get; set; } = string.Empty;
    public string? Reason { get; set; }
}

public class AssignConversationRequest
{
    public string SellerId { get; set; } = string.Empty;
}

public class MarkAsReadRequest
{
    public string UserId { get; set; } = string.Empty;
}

public class ProductInquiryRequest
{
    public string CustomerId { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public int ProductId { get; set; }
    public string Message { get; set; } = string.Empty;
}

public class OrderSupportRequest
{
    public string CustomerId { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public int OrderId { get; set; }
    public string Message { get; set; } = string.Empty;
    public string IssueType { get; set; } = string.Empty;
}
