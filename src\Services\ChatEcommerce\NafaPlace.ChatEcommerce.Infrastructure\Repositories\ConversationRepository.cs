using Microsoft.EntityFrameworkCore;
using NafaPlace.ChatEcommerce.Application.DTOs;
using NafaPlace.ChatEcommerce.Application.Repositories;
using NafaPlace.ChatEcommerce.Domain.Entities;
using NafaPlace.ChatEcommerce.Infrastructure.Data;

namespace NafaPlace.ChatEcommerce.Infrastructure.Repositories;

public class MessageRepository : IMessageRepository
{
    private readonly ChatEcommerceDbContext _context;

    public MessageRepository(ChatEcommerceDbContext context)
    {
        _context = context;
    }

    public async Task<int> CreateAsync(Message message)
    {
        _context.Messages.Add(message);
        await _context.SaveChangesAsync();
        return message.Id;
    }

    public async Task<Message?> GetByIdAsync(int id)
    {
        return await _context.Messages
            .Include(m => m.Conversation)
            .FirstOrDefaultAsync(m => m.Id == id);
    }

    public async Task<List<Message>> GetByConversationIdAsync(int conversationId, int page = 1, int pageSize = 50)
    {
        return await _context.Messages
            .Where(m => m.ConversationId == conversationId)
            .OrderBy(m => m.Timestamp)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    public async Task<bool> UpdateAsync(Message message)
    {
        _context.Messages.Update(message);
        return await _context.SaveChangesAsync() > 0;
    }

    public async Task<bool> DeleteAsync(int id)
    {
        var message = await _context.Messages.FindAsync(id);
        if (message == null) return false;

        _context.Messages.Remove(message);
        return await _context.SaveChangesAsync() > 0;
    }

    public async Task<bool> MarkAsReadAsync(int messageId, string userId)
    {
        var message = await _context.Messages.FindAsync(messageId);
        if (message == null) return false;

        message.IsRead = true;
        message.ReadAt = DateTime.UtcNow;
        return await _context.SaveChangesAsync() > 0;
    }

    public async Task<bool> MarkConversationAsReadAsync(int conversationId, string userId)
    {
        var messages = await _context.Messages
            .Where(m => m.ConversationId == conversationId && !m.IsRead && m.SenderId != userId)
            .ToListAsync();

        foreach (var message in messages)
        {
            message.IsRead = true;
            message.ReadAt = DateTime.UtcNow;
        }

        var conversation = await _context.Conversations.FindAsync(conversationId);
        if (conversation != null)
        {
            conversation.HasUnreadMessages = false;
            conversation.UnreadCount = 0;
        }

        return await _context.SaveChangesAsync() > 0;
    }

    public async Task<List<Message>> SearchAsync(string query, int? conversationId = null)
    {
        var queryable = _context.Messages
            .Where(m => m.Content.Contains(query));

        if (conversationId.HasValue)
            queryable = queryable.Where(m => m.ConversationId == conversationId);

        return await queryable
            .OrderByDescending(m => m.Timestamp)
            .ToListAsync();
    }

    public async Task<int> CountUnreadAsync(string userId)
    {
        return await _context.Messages
            .Where(m => !m.IsRead && m.SenderId != userId)
            .CountAsync();
    }

    public async Task<int> CountByConversationAsync(int conversationId)
    {
        return await _context.Messages
            .Where(m => m.ConversationId == conversationId)
            .CountAsync();
    }
}

public class ConversationRepository : IConversationRepository
{
    private readonly ChatEcommerceDbContext _context;

    public ConversationRepository(ChatEcommerceDbContext context)
    {
        _context = context;
    }

    public async Task<int> CreateAsync(Conversation conversation)
    {
        _context.Conversations.Add(conversation);
        await _context.SaveChangesAsync();
        return conversation.Id;
    }

    public async Task<Conversation?> GetByIdAsync(int id)
    {
        return await _context.Conversations
            .Include(c => c.Messages.OrderBy(m => m.Timestamp))
            .FirstOrDefaultAsync(c => c.Id == id);
    }

    public async Task<List<Conversation>> GetAllAsync()
    {
        return await _context.Conversations
            .Include(c => c.Messages.OrderByDescending(m => m.Timestamp).Take(1))
            .OrderByDescending(c => c.UpdatedAt)
            .ToListAsync();
    }

    public async Task<List<Conversation>> GetByFilterAsync(ConversationFilterDto filter)
    {
        var query = _context.Conversations
            .Include(c => c.Messages.OrderByDescending(m => m.Timestamp).Take(1))
            .AsQueryable();

        // Filtres
        if (!string.IsNullOrEmpty(filter.CustomerId))
            query = query.Where(c => c.CustomerId == filter.CustomerId);

        if (!string.IsNullOrEmpty(filter.SellerId))
            query = query.Where(c => c.SellerId == filter.SellerId);

        if (filter.ProductId.HasValue)
            query = query.Where(c => c.ProductId == filter.ProductId);

        if (filter.OrderId.HasValue)
            query = query.Where(c => c.OrderId == filter.OrderId);

        if (!string.IsNullOrEmpty(filter.Status))
        {
            if (Enum.TryParse<ConversationStatus>(filter.Status, out var status))
                query = query.Where(c => c.Status == status);
        }

        if (!string.IsNullOrEmpty(filter.Type))
        {
            if (Enum.TryParse<ConversationType>(filter.Type, out var type))
                query = query.Where(c => c.Type == type);
        }

        if (!string.IsNullOrEmpty(filter.Priority))
        {
            if (Enum.TryParse<ConversationPriority>(filter.Priority, out var priority))
                query = query.Where(c => c.Priority == priority);
        }

        if (!string.IsNullOrEmpty(filter.Category))
            query = query.Where(c => c.Category == filter.Category);

        if (filter.HasUnreadMessages.HasValue)
            query = query.Where(c => c.HasUnreadMessages == filter.HasUnreadMessages);

        if (filter.StartDate.HasValue)
            query = query.Where(c => c.CreatedAt >= filter.StartDate);

        if (filter.EndDate.HasValue)
            query = query.Where(c => c.CreatedAt <= filter.EndDate);

        if (!string.IsNullOrEmpty(filter.SearchTerm))
        {
            query = query.Where(c => 
                c.Subject.Contains(filter.SearchTerm) ||
                c.CustomerName.Contains(filter.SearchTerm) ||
                c.SellerName!.Contains(filter.SearchTerm) ||
                c.ProductName!.Contains(filter.SearchTerm));
        }

        // Tri
        query = filter.SortBy.ToLower() switch
        {
            "createdat" => filter.SortDirection.ToUpper() == "ASC" 
                ? query.OrderBy(c => c.CreatedAt) 
                : query.OrderByDescending(c => c.CreatedAt),
            "updatedat" => filter.SortDirection.ToUpper() == "ASC" 
                ? query.OrderBy(c => c.UpdatedAt) 
                : query.OrderByDescending(c => c.UpdatedAt),
            "priority" => filter.SortDirection.ToUpper() == "ASC" 
                ? query.OrderBy(c => c.Priority) 
                : query.OrderByDescending(c => c.Priority),
            _ => query.OrderByDescending(c => c.UpdatedAt)
        };

        // Pagination
        return await query
            .Skip((filter.Page - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .ToListAsync();
    }

    public async Task<List<Conversation>> GetByUserIdAsync(string userId)
    {
        return await _context.Conversations
            .Include(c => c.Messages.OrderByDescending(m => m.Timestamp).Take(1))
            .Where(c => c.CustomerId == userId)
            .OrderByDescending(c => c.UpdatedAt)
            .ToListAsync();
    }

    public async Task<List<Conversation>> GetBySellerIdAsync(string sellerId)
    {
        return await _context.Conversations
            .Include(c => c.Messages.OrderByDescending(m => m.Timestamp).Take(1))
            .Where(c => c.SellerId == sellerId)
            .OrderByDescending(c => c.UpdatedAt)
            .ToListAsync();
    }

    public async Task<List<Conversation>> GetByProductIdAsync(int productId)
    {
        return await _context.Conversations
            .Include(c => c.Messages.OrderByDescending(m => m.Timestamp).Take(1))
            .Where(c => c.ProductId == productId)
            .OrderByDescending(c => c.UpdatedAt)
            .ToListAsync();
    }

    public async Task<List<Conversation>> GetByOrderIdAsync(int orderId)
    {
        return await _context.Conversations
            .Include(c => c.Messages.OrderByDescending(m => m.Timestamp).Take(1))
            .Where(c => c.OrderId == orderId)
            .OrderByDescending(c => c.UpdatedAt)
            .ToListAsync();
    }

    public async Task<bool> UpdateAsync(Conversation conversation)
    {
        conversation.UpdatedAt = DateTime.UtcNow;
        _context.Conversations.Update(conversation);
        return await _context.SaveChangesAsync() > 0;
    }

    public async Task<bool> DeleteAsync(int id)
    {
        var conversation = await _context.Conversations.FindAsync(id);
        if (conversation == null) return false;

        _context.Conversations.Remove(conversation);
        return await _context.SaveChangesAsync() > 0;
    }

    public async Task<List<Conversation>> SearchAsync(string query, ConversationFilterDto? filter = null)
    {
        var queryable = _context.Conversations
            .Include(c => c.Messages.OrderByDescending(m => m.Timestamp).Take(1))
            .Where(c => 
                c.Subject.Contains(query) ||
                c.CustomerName.Contains(query) ||
                c.SellerName!.Contains(query) ||
                c.ProductName!.Contains(query) ||
                c.Messages.Any(m => m.Content.Contains(query)));

        if (filter != null)
        {
            // Appliquer les filtres additionnels
            if (!string.IsNullOrEmpty(filter.CustomerId))
                queryable = queryable.Where(c => c.CustomerId == filter.CustomerId);

            if (!string.IsNullOrEmpty(filter.SellerId))
                queryable = queryable.Where(c => c.SellerId == filter.SellerId);
        }

        return await queryable
            .OrderByDescending(c => c.UpdatedAt)
            .ToListAsync();
    }

    public async Task<int> CountAsync(ConversationFilterDto? filter = null)
    {
        var query = _context.Conversations.AsQueryable();

        if (filter != null)
        {
            if (!string.IsNullOrEmpty(filter.CustomerId))
                query = query.Where(c => c.CustomerId == filter.CustomerId);

            if (!string.IsNullOrEmpty(filter.SellerId))
                query = query.Where(c => c.SellerId == filter.SellerId);

            if (!string.IsNullOrEmpty(filter.Status))
            {
                if (Enum.TryParse<ConversationStatus>(filter.Status, out var status))
                    query = query.Where(c => c.Status == status);
            }
        }

        return await query.CountAsync();
    }

    public async Task<int> CountUnreadAsync(string userId)
    {
        return await _context.Conversations
            .Where(c => (c.CustomerId == userId || c.SellerId == userId) && c.HasUnreadMessages)
            .CountAsync();
    }

    public async Task<Dictionary<string, int>> GetConversationsByStatusAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _context.Conversations.AsQueryable();

        if (startDate.HasValue)
            query = query.Where(c => c.CreatedAt >= startDate);

        if (endDate.HasValue)
            query = query.Where(c => c.CreatedAt <= endDate);

        return await query
            .GroupBy(c => c.Status)
            .Select(g => new { Status = g.Key.ToString(), Count = g.Count() })
            .ToDictionaryAsync(x => x.Status, x => x.Count);
    }

    public async Task<Dictionary<string, int>> GetConversationsByTypeAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _context.Conversations.AsQueryable();

        if (startDate.HasValue)
            query = query.Where(c => c.CreatedAt >= startDate);

        if (endDate.HasValue)
            query = query.Where(c => c.CreatedAt <= endDate);

        return await query
            .GroupBy(c => c.Type)
            .Select(g => new { Type = g.Key.ToString(), Count = g.Count() })
            .ToDictionaryAsync(x => x.Type, x => x.Count);
    }

    public async Task<double> GetAverageResponseTimeAsync(string? sellerId = null, DateTime? startDate = null, DateTime? endDate = null)
    {
        // Logique pour calculer le temps de réponse moyen
        // Pour l'instant, retourner une valeur par défaut
        return await Task.FromResult(2.5); // 2.5 heures en moyenne
    }

    public async Task<double> GetAverageResolutionTimeAsync(string? sellerId = null, DateTime? startDate = null, DateTime? endDate = null)
    {
        // Logique pour calculer le temps de résolution moyen
        // Pour l'instant, retourner une valeur par défaut
        return await Task.FromResult(24.0); // 24 heures en moyenne
    }
}
