using System.ComponentModel.DataAnnotations;

namespace NafaPlace.Analytics.Domain.DTOs;

/// <summary>
/// DTO pour le tableau de bord analytics complet
/// </summary>
public class ComprehensiveDashboardDto
{
    // KPIs globaux
    public decimal TotalRevenue { get; set; }
    public decimal RevenueGrowth { get; set; } // Pourcentage
    public int TotalOrders { get; set; }
    public double OrderGrowth { get; set; }
    public int ActiveCustomers { get; set; }
    public double CustomerGrowth { get; set; }
    public decimal AverageOrderValue { get; set; }
    public double ConversionRate { get; set; }

    // Métriques avancées
    public decimal CustomerLifetimeValue { get; set; }
    public double CustomerRetentionRate { get; set; }
    public double ChurnRate { get; set; }
    public decimal NetPromoterScore { get; set; }

    // Données de performance
    public List<SalesPerformanceDto> SalesPerformance { get; set; } = new();
    public List<CategoryPerformanceDto> CategoryPerformance { get; set; } = new();
    public List<TopProductDto> TopProducts { get; set; } = new();
    public List<CustomerSegmentDto> CustomerSegments { get; set; } = new();

    // Données temporelles
    public List<TimeSeriesDataDto> RevenueTimeSeries { get; set; } = new();
    public List<TimeSeriesDataDto> OrderTimeSeries { get; set; } = new();
    public List<TimeSeriesDataDto> CustomerTimeSeries { get; set; } = new();

    // Insights IA
    public List<AIInsightDto> AIInsights { get; set; } = new();

    // Métriques géographiques (spécifique à la Guinée)
    public List<GeographicPerformanceDto> GeographicData { get; set; } = new();

    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
}

/// <summary>
/// DTO pour les prédictions de ventes
/// </summary>
public class SalesPredictionDto
{
    public DateTime PredictionDate { get; set; }
    public decimal PredictedRevenue { get; set; }
    public decimal LowerBound { get; set; } // Borne inférieure (confidence interval)
    public decimal UpperBound { get; set; } // Borne supérieure
    public int PredictedOrders { get; set; }
    public double ConfidenceLevel { get; set; } // 0-1 (0-100%)
    public string Trend { get; set; } = ""; // "increasing", "decreasing", "stable"
    public List<string> InfluencingFactors { get; set; } = new();
    public string SeasonalityNote { get; set; } = "";
}

/// <summary>
/// DTO pour les prédictions de demande de produits
/// </summary>
public class DemandPredictionDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = "";
    public string Category { get; set; } = "";
    public List<DemandForecastDto> Forecasts { get; set; } = new();
    public RecommendedActionDto RecommendedActions { get; set; } = new();
    public double ModelAccuracy { get; set; }
}

/// <summary>
/// DTO pour une prévision de demande spécifique
/// </summary>
public class DemandForecastDto
{
    public DateTime Date { get; set; }
    public int PredictedDemand { get; set; }
    public double Confidence { get; set; }
    public int RecommendedStock { get; set; }
    public decimal EstimatedRevenue { get; set; }
}

/// <summary>
/// DTO pour l'analyse du churn client
/// </summary>
public class CustomerChurnAnalysisDto
{
    public double OverallChurnRate { get; set; }
    public List<ChurnRiskCustomerDto> HighRiskCustomers { get; set; } = new();
    public List<ChurnFactorDto> ChurnFactors { get; set; } = new();
    public List<RetentionStrategyDto> RetentionStrategies { get; set; } = new();
    public ChurnPredictionMetricsDto ModelMetrics { get; set; } = new();
}

/// <summary>
/// DTO pour un client à risque de churn
/// </summary>
public class ChurnRiskCustomerDto
{
    public string CustomerId { get; set; } = "";
    public string CustomerName { get; set; } = "";
    public string Email { get; set; } = "";
    public double ChurnProbability { get; set; } // 0-1
    public string RiskLevel { get; set; } = ""; // "Low", "Medium", "High", "Critical"
    public decimal CustomerValue { get; set; }
    public List<string> RiskFactors { get; set; } = new();
    public List<string> RecommendedActions { get; set; } = new();
    public DateTime LastOrderDate { get; set; }
    public int DaysSinceLastOrder { get; set; }
}

/// <summary>
/// DTO pour les insights client avancés
/// </summary>
public class AdvancedCustomerInsightsDto
{
    public CustomerBehaviorSummaryDto BehaviorSummary { get; set; } = new();
    public List<CustomerJourneyDto> CustomerJourneys { get; set; } = new();
    public List<PurchasePatternDto> PurchasePatterns { get; set; } = new();
    public List<CustomerPreferenceDto> Preferences { get; set; } = new();
    public List<CrossSellOpportunityDto> CrossSellOpportunities { get; set; } = new();
    public List<CustomerLifecycleStageDto> LifecycleStages { get; set; } = new();
}

/// <summary>
/// DTO pour la segmentation client par IA
/// </summary>
public class AICustomerSegmentationDto
{
    public List<CustomerSegmentDto> Segments { get; set; } = new();
    public SegmentationMetricsDto Metrics { get; set; } = new();
    public List<SegmentInsightDto> Insights { get; set; } = new();
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// DTO pour un segment de clients
/// </summary>
public class CustomerSegmentDto
{
    public string SegmentId { get; set; } = "";
    public string Name { get; set; } = "";
    public string Description { get; set; } = "";
    public int CustomerCount { get; set; }
    public double Percentage { get; set; }
    public decimal AverageOrderValue { get; set; }
    public double PurchaseFrequency { get; set; }
    public decimal TotalRevenue { get; set; }
    public List<string> Characteristics { get; set; } = new();
    public List<ProductAffinityDto> PreferredProducts { get; set; } = new();
    public string MarketingStrategy { get; set; } = "";
}

/// <summary>
/// DTO pour les recommandations personnalisées
/// </summary>
public class PersonalizedRecommendationDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = "";
    public string Category { get; set; } = "";
    public decimal Price { get; set; }
    public string ImageUrl { get; set; } = "";
    public double RecommendationScore { get; set; } // 0-1
    public string RecommendationReason { get; set; } = "";
    public List<string> Tags { get; set; } = new();
    public bool InStock { get; set; }
    public double PurchaseProbability { get; set; }
}

/// <summary>
/// DTO pour l'analyse de positionnement marché
/// </summary>
public class MarketPositionAnalysisDto
{
    public MarketShareDto MarketShare { get; set; } = new();
    public List<CompetitorAnalysisDto> Competitors { get; set; } = new();
    public List<MarketTrendDto> Trends { get; set; } = new();
    public List<OpportunityDto> Opportunities { get; set; } = new();
    public List<ThreatDto> Threats { get; set; } = new();
    public MarketPositionSummaryDto Summary { get; set; } = new();
}

/// <summary>
/// DTO pour l'optimisation des prix par IA
/// </summary>
public class AIPricingOptimizationDto
{
    public List<PricingRecommendationDto> PricingRecommendations { get; set; } = new();
    public PricingStrategyDto RecommendedStrategy { get; set; } = new();
    public List<PriceElasticityDto> PriceElasticities { get; set; } = new();
    public decimal EstimatedRevenueImpact { get; set; }
    public double ConfidenceLevel { get; set; }
}

/// <summary>
/// DTO pour une recommandation de prix
/// </summary>
public class PricingRecommendationDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = "";
    public decimal CurrentPrice { get; set; }
    public decimal RecommendedPrice { get; set; }
    public decimal PriceChange { get; set; }
    public double PriceChangePercentage { get; set; }
    public decimal EstimatedRevenueImpact { get; set; }
    public List<string> Reasoning { get; set; } = new();
    public string Action { get; set; } = ""; // "increase", "decrease", "maintain"
    public double Confidence { get; set; }
}

/// <summary>
/// DTO pour les tendances en temps réel
/// </summary>
public class RealTimeTrendsDto
{
    public List<TrendingProductDto> TrendingProducts { get; set; } = new();
    public List<SearchTrendDto> SearchTrends { get; set; } = new();
    public List<CategoryTrendDto> CategoryTrends { get; set; } = new();
    public RealTimeMetricsDto RealTimeMetrics { get; set; } = new();
    public List<AlertDto> ActiveAlerts { get; set; } = new();
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// DTO pour l'analyse de cohort
/// </summary>
public class CohortAnalysisDto
{
    public List<CohortDataDto> Cohorts { get; set; } = new();
    public CohortInsightsDto Insights { get; set; } = new();
    public List<RetentionRateDto> RetentionRates { get; set; } = new();
    public DateTime AnalysisDate { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// DTO pour l'analyse RFM
/// </summary>
public class RFMAnalysisDto
{
    public List<RFMCustomerDto> Customers { get; set; } = new();
    public List<RFMSegmentDto> Segments { get; set; } = new();
    public RFMInsightsDto Insights { get; set; } = new();
    public DateTime AnalysisDate { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// DTO pour la détection d'anomalies
/// </summary>
public class AnomalyDetectionDto
{
    public List<AnomalyDto> Anomalies { get; set; } = new();
    public AnomalyStatisticsDto Statistics { get; set; } = new();
    public List<AnomalyInsightDto> Insights { get; set; } = new();
    public DateTime DetectionDate { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// DTO pour l'attribution marketing
/// </summary>
public class MarketingAttributionDto
{
    public List<ChannelAttributionDto> ChannelAttributions { get; set; } = new();
    public List<CampaignPerformanceDto> CampaignPerformances { get; set; } = new();
    public AttributionModelDto AttributionModel { get; set; } = new();
    public ROIAnalysisDto ROIAnalysis { get; set; } = new();
}

/// <summary>
/// DTO pour l'optimisation d'inventaire
/// </summary>
public class InventoryOptimizationDto
{
    public List<InventoryRecommendationDto> Recommendations { get; set; } = new();
    public InventoryMetricsDto CurrentMetrics { get; set; } = new();
    public List<StockOutRiskDto> StockOutRisks { get; set; } = new();
    public decimal EstimatedSavings { get; set; }
    public DateTime OptimizationDate { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// DTO pour la performance des vendeurs
/// </summary>
public class SellerPerformanceAnalysisDto
{
    public List<SellerMetricsDto> SellerMetrics { get; set; } = new();
    public SellerBenchmarkDto Benchmarks { get; set; } = new();
    public List<SellerInsightDto> Insights { get; set; } = new();
    public List<ImprovementOpportunityDto> ImprovementOpportunities { get; set; } = new();
}

/// <summary>
/// DTO pour une requête de rapport personnalisé
/// </summary>
public class CustomReportRequestDto
{
    [Required]
    public string ReportName { get; set; } = "";

    [Required]
    public DateTime FromDate { get; set; }

    [Required]
    public DateTime ToDate { get; set; }

    public List<string> Metrics { get; set; } = new();
    public List<string> Dimensions { get; set; } = new();
    public List<FilterDto> Filters { get; set; } = new();
    public string GroupBy { get; set; } = "";
    public string SortBy { get; set; } = "";
    public bool IncludePredictions { get; set; } = false;
    public string OutputFormat { get; set; } = "json"; // json, csv, excel, pdf
}

/// <summary>
/// DTO pour la formation des modèles ML
/// </summary>
public class TrainModelsRequestDto
{
    public List<string> ModelTypes { get; set; } = new(); // "sales_prediction", "churn_prediction", etc.
    public DateTime TrainingDataFromDate { get; set; }
    public DateTime TrainingDataToDate { get; set; }
    public Dictionary<string, object>? ModelParameters { get; set; }
    public bool UseLatestData { get; set; } = true;
    public string TrainingPriority { get; set; } = "normal"; // low, normal, high
}

/// <summary>
/// DTO pour la configuration d'alertes
/// </summary>
public class AlertConfigurationDto
{
    public string UserId { get; set; } = "";
    public string AlertName { get; set; } = "";
    public string AlertType { get; set; } = ""; // "sales_drop", "stock_low", "anomaly", etc.
    public Dictionary<string, object> Conditions { get; set; } = new();
    public List<string> NotificationChannels { get; set; } = new(); // "email", "sms", "push"
    public string Frequency { get; set; } = "immediate"; // immediate, hourly, daily
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// DTO pour l'export de données
/// </summary>
public class ExportRequestDto
{
    [Required]
    public string DataType { get; set; } = ""; // "dashboard", "sales", "customers", etc.

    [Required]
    public string Format { get; set; } = "csv"; // csv, excel, pdf, json

    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public List<string>? Columns { get; set; }
    public List<FilterDto>? Filters { get; set; }
    public bool IncludeCharts { get; set; } = false;
}

// DTOs de support
public class SalesPerformanceDto
{
    public DateTime Date { get; set; }
    public decimal Revenue { get; set; }
    public int Orders { get; set; }
    public int Units { get; set; }
}

public class CategoryPerformanceDto
{
    public string Category { get; set; } = "";
    public decimal Revenue { get; set; }
    public int Orders { get; set; }
    public double GrowthRate { get; set; }
}

public class TopProductDto
{
    public int ProductId { get; set; }
    public string Name { get; set; } = "";
    public decimal Revenue { get; set; }
    public int UnitsSold { get; set; }
    public string Category { get; set; } = "";
}

public class TimeSeriesDataDto
{
    public DateTime Date { get; set; }
    public decimal Value { get; set; }
    public double? Change { get; set; }
}

public class AIInsightDto
{
    public string Title { get; set; } = "";
    public string Description { get; set; } = "";
    public string Type { get; set; } = ""; // "opportunity", "risk", "trend", "anomaly"
    public double Confidence { get; set; }
    public List<string> Recommendations { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

public class GeographicPerformanceDto
{
    public string Region { get; set; } = ""; // Commune de Conakry
    public decimal Revenue { get; set; }
    public int Orders { get; set; }
    public int Customers { get; set; }
    public double GrowthRate { get; set; }
}

public class FilterDto
{
    public string Field { get; set; } = "";
    public string Operator { get; set; } = ""; // equals, contains, greater_than, etc.
    public object Value { get; set; } = new();
}

public class RecommendedActionDto
{
    public string Action { get; set; } = "";
    public string Description { get; set; } = "";
    public string Priority { get; set; } = "";
    public decimal EstimatedImpact { get; set; }
}

public class ChurnFactorDto
{
    public string Factor { get; set; } = "";
    public double Importance { get; set; }
    public string Description { get; set; } = "";
}

public class RetentionStrategyDto
{
    public string Strategy { get; set; } = "";
    public string Description { get; set; } = "";
    public string TargetSegment { get; set; } = "";
    public decimal EstimatedCost { get; set; }
    public double ExpectedEffectiveness { get; set; }
}

public class ChurnPredictionMetricsDto
{
    public double Accuracy { get; set; }
    public double Precision { get; set; }
    public double Recall { get; set; }
    public double F1Score { get; set; }
}

public class CustomerBehaviorSummaryDto
{
    public double AverageSessionDuration { get; set; }
    public int AveragePageViews { get; set; }
    public double BounceRate { get; set; }
    public string PreferredChannel { get; set; } = "";
    public string PreferredTime { get; set; } = "";
}

public class CustomerJourneyDto
{
    public string Stage { get; set; } = "";
    public int CustomerCount { get; set; }
    public double ConversionRate { get; set; }
    public double AverageTimeInStage { get; set; }
}

public class PurchasePatternDto
{
    public string Pattern { get; set; } = "";
    public double Frequency { get; set; }
    public string Description { get; set; } = "";
}

public class CustomerPreferenceDto
{
    public string Category { get; set; } = "";
    public double Affinity { get; set; }
    public string Reason { get; set; } = "";
}

public class CrossSellOpportunityDto
{
    public string ProductCombination { get; set; } = "";
    public double Probability { get; set; }
    public decimal PotentialRevenue { get; set; }
}

public class CustomerLifecycleStageDto
{
    public string Stage { get; set; } = "";
    public int CustomerCount { get; set; }
    public double Percentage { get; set; }
    public List<string> Characteristics { get; set; } = new();
}

public class SegmentationMetricsDto
{
    public int TotalSegments { get; set; }
    public double SilhouetteScore { get; set; }
    public double InertiaScore { get; set; }
    public DateTime LastUpdated { get; set; }
}

public class SegmentInsightDto
{
    public string SegmentId { get; set; } = "";
    public string Insight { get; set; } = "";
    public string ActionRecommendation { get; set; } = "";
}

public class ProductAffinityDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = "";
    public double AffinityScore { get; set; }
}

// DTOs supplémentaires pour les services d'analytics avancés

public class TrainingResultDto
{
    public bool Success { get; set; }
    public string TrainingId { get; set; } = "";
    public List<string> TrainedModels { get; set; } = new();
    public Dictionary<string, double> ModelAccuracies { get; set; } = new();
    public DateTime TrainingStarted { get; set; }
    public DateTime TrainingCompleted { get; set; }
    public string Status { get; set; } = "";
    public List<string> Errors { get; set; } = new();
}

public class ModelPerformanceMetricsDto
{
    public List<ModelMetricDto> Models { get; set; } = new();
    public DateTime LastUpdated { get; set; }
    public string OverallHealth { get; set; } = "";
}

public class ModelMetricDto
{
    public string ModelName { get; set; } = "";
    public string ModelType { get; set; } = "";
    public double Accuracy { get; set; }
    public double Precision { get; set; }
    public double Recall { get; set; }
    public double F1Score { get; set; }
    public DateTime LastTrained { get; set; }
    public string Status { get; set; } = "";
}

public class PriceImpactPredictionDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = "";
    public decimal CurrentPrice { get; set; }
    public decimal ProposedPrice { get; set; }
    public decimal EstimatedSalesImpact { get; set; }
    public decimal EstimatedRevenueImpact { get; set; }
    public double DemandElasticity { get; set; }
    public double ConfidenceLevel { get; set; }
    public List<string> RiskFactors { get; set; } = new();
}

public class SeasonalityAnalysisDto
{
    public string Category { get; set; } = "";
    public List<SeasonalPatternDto> SeasonalPatterns { get; set; } = new();
    public List<SeasonalForecastDto> Forecasts { get; set; } = new();
    public double SeasonalityStrength { get; set; }
}

public class SeasonalPatternDto
{
    public string Period { get; set; } = ""; // "monthly", "quarterly", "yearly"
    public int PeriodIndex { get; set; } // 1-12 for months, 1-4 for quarters, etc.
    public string PeriodName { get; set; } = "";
    public double SeasonalIndex { get; set; }
    public string Description { get; set; } = "";
}

public class SeasonalForecastDto
{
    public DateTime Date { get; set; }
    public decimal ForecastedSales { get; set; }
    public double SeasonalAdjustment { get; set; }
    public double Confidence { get; set; }
}

public class CampaignImpactPredictionDto
{
    public string CampaignName { get; set; } = "";
    public decimal EstimatedRevenue { get; set; }
    public int EstimatedOrders { get; set; }
    public double ROI { get; set; }
    public decimal CostPerAcquisition { get; set; }
    public List<string> RecommendedOptimizations { get; set; } = new();
}

public class CampaignParametersDto
{
    public string CampaignName { get; set; } = "";
    public string CampaignType { get; set; } = "";
    public decimal Budget { get; set; }
    public List<string> TargetSegments { get; set; } = new();
    public List<int> ProductIds { get; set; } = new();
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
}

public class CustomerLifetimeValueDto
{
    public string SegmentId { get; set; } = "";
    public string SegmentName { get; set; } = "";
    public decimal AverageCLV { get; set; }
    public decimal MedianCLV { get; set; }
    public List<CLVDistributionDto> CLVDistribution { get; set; } = new();
    public List<CLVFactorDto> InfluencingFactors { get; set; } = new();
}

public class CLVDistributionDto
{
    public string Range { get; set; } = "";
    public int CustomerCount { get; set; }
    public double Percentage { get; set; }
    public decimal MinValue { get; set; }
    public decimal MaxValue { get; set; }
}

public class CLVFactorDto
{
    public string Factor { get; set; } = "";
    public double Correlation { get; set; }
    public string Impact { get; set; } = "";
}

public class InfluentialCustomerDto
{
    public string CustomerId { get; set; } = "";
    public string CustomerName { get; set; } = "";
    public double InfluenceScore { get; set; }
    public int ReferralCount { get; set; }
    public decimal ReferralRevenue { get; set; }
    public double SocialMediaReach { get; set; }
    public List<string> InfluenceChannels { get; set; } = new();
}

public class CustomerJourneyAnalysisDto
{
    public List<JourneyStageDto> JourneyStages { get; set; } = new();
    public List<FrictionPointDto> FrictionPoints { get; set; } = new();
    public List<OptimizationOpportunityDto> OptimizationOpportunities { get; set; } = new();
    public double OverallConversionRate { get; set; }
}

public class JourneyStageDto
{
    public string StageName { get; set; } = "";
    public int CustomerCount { get; set; }
    public double ConversionRate { get; set; }
    public double AverageTimeInStage { get; set; }
    public double DropOffRate { get; set; }
}

public class FrictionPointDto
{
    public string StageName { get; set; } = "";
    public string FrictionType { get; set; } = "";
    public string Description { get; set; } = "";
    public double ImpactScore { get; set; }
    public List<string> RecommendedActions { get; set; } = new();
}

public class OptimizationOpportunityDto
{
    public string OpportunityType { get; set; } = "";
    public string Description { get; set; } = "";
    public decimal EstimatedImpact { get; set; }
    public string Priority { get; set; } = "";
    public List<string> RequiredActions { get; set; } = new();
}

public class NextPurchasePredictionDto
{
    public string CustomerId { get; set; } = "";
    public List<CategoryPredictionDto> CategoryPredictions { get; set; } = new();
    public DateTime PredictedNextPurchaseDate { get; set; }
    public decimal PredictedOrderValue { get; set; }
    public double Confidence { get; set; }
}

public class CategoryPredictionDto
{
    public string Category { get; set; } = "";
    public double Probability { get; set; }
    public decimal EstimatedValue { get; set; }
    public List<int> RecommendedProducts { get; set; } = new();
}

public class CommunicationPreferencesDto
{
    public List<SegmentCommunicationDto> SegmentPreferences { get; set; } = new();
    public List<ChannelEffectivenessDto> ChannelEffectiveness { get; set; } = new();
    public List<OptimalTimingDto> OptimalTimings { get; set; } = new();
}

public class SegmentCommunicationDto
{
    public string SegmentId { get; set; } = "";
    public string SegmentName { get; set; } = "";
    public string PreferredChannel { get; set; } = "";
    public string PreferredFrequency { get; set; } = "";
    public string PreferredTime { get; set; } = "";
    public double ResponseRate { get; set; }
}

public class ChannelEffectivenessDto
{
    public string Channel { get; set; } = "";
    public double OverallEffectiveness { get; set; }
    public double OpenRate { get; set; }
    public double ClickRate { get; set; }
    public double ConversionRate { get; set; }
    public decimal CostPerAcquisition { get; set; }
}

public class OptimalTimingDto
{
    public string Segment { get; set; } = "";
    public string DayOfWeek { get; set; } = "";
    public string TimeOfDay { get; set; } = "";
    public double EngagementRate { get; set; }
}

public class LoyaltyOpportunityDto
{
    public string SegmentId { get; set; } = "";
    public string SegmentName { get; set; } = "";
    public string OpportunityType { get; set; } = "";
    public string Description { get; set; } = "";
    public decimal EstimatedRevenue { get; set; }
    public decimal ImplementationCost { get; set; }
    public double ROI { get; set; }
    public List<string> RecommendedActions { get; set; } = new();
}

public class CustomerSatisfactionAnalysisDto
{
    public double OverallSatisfactionScore { get; set; }
    public List<SegmentSatisfactionDto> SegmentSatisfaction { get; set; } = new();
    public List<SatisfactionDriverDto> SatisfactionDrivers { get; set; } = new();
    public List<ImprovementAreaDto> ImprovementAreas { get; set; } = new();
}

public class SegmentSatisfactionDto
{
    public string SegmentId { get; set; } = "";
    public string SegmentName { get; set; } = "";
    public double SatisfactionScore { get; set; }
    public double NetPromoterScore { get; set; }
    public string TrendDirection { get; set; } = "";
}

public class SatisfactionDriverDto
{
    public string Driver { get; set; } = "";
    public double Importance { get; set; }
    public double CurrentPerformance { get; set; }
    public double ImpactOnSatisfaction { get; set; }
}

public class ImprovementAreaDto
{
    public string Area { get; set; } = "";
    public double Priority { get; set; }
    public string Description { get; set; } = "";
    public List<string> RecommendedActions { get; set; } = new();
}

public class DormantCustomerAnalysisDto
{
    public List<DormantCustomerDto> DormantCustomers { get; set; } = new();
    public List<ReactivationStrategyDto> ReactivationStrategies { get; set; } = new();
    public decimal TotalPotentialRevenue { get; set; }
    public int TotalDormantCustomers { get; set; }
}

public class DormantCustomerDto
{
    public string CustomerId { get; set; } = "";
    public string CustomerName { get; set; } = "";
    public DateTime LastPurchaseDate { get; set; }
    public int DaysSinceLastPurchase { get; set; }
    public decimal HistoricalValue { get; set; }
    public string ReactivationPotential { get; set; } = "";
    public List<string> RecommendedActions { get; set; } = new();
}

public class ReactivationStrategyDto
{
    public string StrategyName { get; set; } = "";
    public string Description { get; set; } = "";
    public string TargetSegment { get; set; } = "";
    public decimal ExpectedCost { get; set; }
    public double ExpectedSuccessRate { get; set; }
    public decimal ExpectedRevenue { get; set; }
}

// DTOs pour l'analyse de marché

public class MarketTrendsAnalysisDto
{
    public List<MarketTrendDto> Trends { get; set; } = new();
    public List<EmergingOpportunityDto> EmergingOpportunities { get; set; } = new();
    public List<MarketRiskDto> MarketRisks { get; set; } = new();
    public DateTime AnalysisDate { get; set; }
}

public class MarketTrendDto
{
    public string TrendName { get; set; } = "";
    public string Category { get; set; } = "";
    public string TrendDirection { get; set; } = "";
    public double TrendStrength { get; set; }
    public string Description { get; set; } = "";
    public List<string> InfluencingFactors { get; set; } = new();
}

public class EmergingOpportunityDto
{
    public string OpportunityName { get; set; } = "";
    public string Category { get; set; } = "";
    public decimal MarketSize { get; set; }
    public double GrowthRate { get; set; }
    public string OpportunityType { get; set; } = "";
    public List<string> RequiredCapabilities { get; set; } = new();
}

public class MarketRiskDto
{
    public string RiskName { get; set; } = "";
    public string RiskType { get; set; } = "";
    public double Probability { get; set; }
    public double Impact { get; set; }
    public string Description { get; set; } = "";
    public List<string> MitigationStrategies { get; set; } = new();
}

public class MarketNicheOpportunityDto
{
    public string NicheName { get; set; } = "";
    public string Category { get; set; } = "";
    public decimal EstimatedMarketSize { get; set; }
    public double CompetitionLevel { get; set; }
    public double EntryBarriers { get; set; }
    public decimal PotentialRevenue { get; set; }
    public List<string> TargetCustomers { get; set; } = new();
    public List<string> RequiredInvestments { get; set; } = new();
}

public class PriceElasticityDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = "";
    public double ElasticityCoefficient { get; set; }
    public string ElasticityType { get; set; } = ""; // "elastic", "inelastic", "unit elastic"
    public decimal OptimalPricePoint { get; set; }
    public List<PriceScenarioDto> PriceScenarios { get; set; } = new();
}

public class PriceScenarioDto
{
    public decimal Price { get; set; }
    public double PredictedDemandChange { get; set; }
    public decimal PredictedRevenue { get; set; }
    public string Recommendation { get; set; } = "";
}

public class RegionalSeasonalityDto
{
    public List<RegionalSeasonalPatternDto> RegionalPatterns { get; set; } = new();
    public List<string> Insights { get; set; } = new();
    public DateTime AnalysisDate { get; set; }
}

public class RegionalSeasonalPatternDto
{
    public string Region { get; set; } = "";
    public string Category { get; set; } = "";
    public List<SeasonalPatternDto> SeasonalPatterns { get; set; } = new();
    public string PeakSeason { get; set; } = "";
    public string LowSeason { get; set; } = "";
}

public class ProductRelationshipAnalysisDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = "";
    public List<ComplementaryProductDto> ComplementaryProducts { get; set; } = new();
    public List<SubstituteProductDto> SubstituteProducts { get; set; } = new();
    public List<CrossSellOpportunityDto> CrossSellOpportunities { get; set; } = new();
}

public class ComplementaryProductDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = "";
    public double ComplementaryStrength { get; set; }
    public decimal AdditionalRevenuePotential { get; set; }
}

public class SubstituteProductDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = "";
    public double SubstitutionProbability { get; set; }
    public string CompetitivePosition { get; set; } = "";
}

public class MarketShareAnalysisDto
{
    public List<CategoryMarketShareDto> CategoryShares { get; set; } = new();
    public List<MarketShareProjectionDto> Projections { get; set; } = new();
    public double OverallMarketShare { get; set; }
    public string MarketPosition { get; set; } = "";
}

public class CategoryMarketShareDto
{
    public string Category { get; set; } = "";
    public double MarketShare { get; set; }
    public string Trend { get; set; } = "";
    public List<CompetitorShareDto> CompetitorShares { get; set; } = new();
}

public class CompetitorShareDto
{
    public string CompetitorName { get; set; } = "";
    public double MarketShare { get; set; }
    public string Trend { get; set; } = "";
}

public class MarketShareProjectionDto
{
    public DateTime Date { get; set; }
    public string Category { get; set; } = "";
    public double ProjectedShare { get; set; }
    public double Confidence { get; set; }
}

public class CompetitiveImpactDto
{
    public string CompetitorName { get; set; } = "";
    public decimal EstimatedSalesImpact { get; set; }
    public double MarketShareImpact { get; set; }
    public List<AffectedCategoryDto> AffectedCategories { get; set; } = new();
    public List<string> RecommendedCounterActions { get; set; } = new();
}

public class AffectedCategoryDto
{
    public string Category { get; set; } = "";
    public decimal ImpactAmount { get; set; }
    public double ImpactPercentage { get; set; }
    public string Severity { get; set; } = "";
}

public class CompetitorDataDto
{
    public string CompetitorName { get; set; } = "";
    public List<string> Categories { get; set; } = new();
    public string PricingStrategy { get; set; } = "";
    public decimal EstimatedBudget { get; set; }
    public string TargetMarket { get; set; } = "";
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}

public class MacroEconomicImpactDto
{
    public List<EconomicFactorDto> EconomicFactors { get; set; } = new();
    public List<CorrelationDto> Correlations { get; set; } = new();
    public decimal EstimatedOverallImpact { get; set; }
    public List<string> Recommendations { get; set; } = new();
}

public class EconomicFactorDto
{
    public string FactorName { get; set; } = "";
    public double CurrentValue { get; set; }
    public string Trend { get; set; } = "";
    public double ImpactOnSales { get; set; }
    public string Description { get; set; } = "";
}

public class CorrelationDto
{
    public string Factor1 { get; set; } = "";
    public string Factor2 { get; set; } = "";
    public double CorrelationCoefficient { get; set; }
    public string Strength { get; set; } = "";
}

public class AssortmentOptimizationDto
{
    public string Category { get; set; } = "";
    public List<AssortmentRecommendationDto> Recommendations { get; set; } = new();
    public decimal EstimatedRevenueImpact { get; set; }
    public decimal TotalBudgetRequired { get; set; }
    public List<string> OptimizationInsights { get; set; } = new();
}

public class AssortmentRecommendationDto
{
    public string Action { get; set; } = ""; // "add", "remove", "modify"
    public int ProductId { get; set; }
    public string ProductName { get; set; } = "";
    public string Reason { get; set; } = "";
    public decimal EstimatedImpact { get; set; }
    public decimal RequiredInvestment { get; set; }
}

public class MarketSegmentAttractivenessDto
{
    public List<SegmentAttractivenessDto> Segments { get; set; } = new();
    public List<string> OverallInsights { get; set; } = new();
    public DateTime AnalysisDate { get; set; }
}

public class SegmentAttractivenessDto
{
    public string SegmentName { get; set; } = "";
    public double AttractivenessScore { get; set; }
    public decimal MarketSize { get; set; }
    public double GrowthRate { get; set; }
    public double CompetitionLevel { get; set; }
    public double ProfitabilityPotential { get; set; }
    public string Recommendation { get; set; } = "";
}

public class PromotionImpactDto
{
    public string PromotionName { get; set; } = "";
    public decimal EstimatedRevenue { get; set; }
    public decimal EstimatedCost { get; set; }
    public double ROI { get; set; }
    public int EstimatedNewCustomers { get; set; }
    public List<string> RiskFactors { get; set; } = new();
    public List<string> OptimizationSuggestions { get; set; } = new();
}

public class PromotionStrategyDto
{
    public string PromotionType { get; set; } = "";
    public List<int> ProductIds { get; set; } = new();
    public decimal DiscountPercentage { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public List<string> TargetSegments { get; set; } = new();
    public decimal Budget { get; set; }
}

public class CannibalizationAnalysisDto
{
    public List<CannibalizationPairDto> CannibalizationPairs { get; set; } = new();
    public double OverallCannibalizationRisk { get; set; }
    public List<string> Recommendations { get; set; } = new();
    public DateTime AnalysisDate { get; set; }
}

public class CannibalizationPairDto
{
    public int Product1Id { get; set; }
    public string Product1Name { get; set; } = "";
    public int Product2Id { get; set; }
    public string Product2Name { get; set; } = "";
    public double CannibalizationRate { get; set; }
    public decimal RevenueImpact { get; set; }
    public string Severity { get; set; } = "";
}

// DTOs manquants pour corriger les erreurs de compilation

public class CohortInsightsDto
{
    public double AverageRetentionRate { get; set; }
    public string BestPerformingCohort { get; set; } = "";
    public List<string> KeyInsights { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

public class RetentionRateDto
{
    public string Period { get; set; } = "";
    public double RetentionRate { get; set; }
    public int CustomerCount { get; set; }
    public string CohortName { get; set; } = "";
}

public class RFMCustomerDto
{
    public string CustomerId { get; set; } = "";
    public string CustomerName { get; set; } = "";
    public int RecencyScore { get; set; }
    public int FrequencyScore { get; set; }
    public int MonetaryScore { get; set; }
    public string RFMSegment { get; set; } = "";
    public decimal TotalValue { get; set; }
}

public class RFMSegmentDto
{
    public string SegmentName { get; set; } = "";
    public string Description { get; set; } = "";
    public int CustomerCount { get; set; }
    public decimal TotalValue { get; set; }
    public double AverageValue { get; set; }
    public List<string> Characteristics { get; set; } = new();
}

public class RFMInsightsDto
{
    public string LargestSegment { get; set; } = "";
    public string HighestValueSegment { get; set; } = "";
    public List<string> KeyFindings { get; set; } = new();
    public List<string> ActionRecommendations { get; set; } = new();
}

public class ImprovementOpportunityDto
{
    public string OpportunityType { get; set; } = "";
    public string Title { get; set; } = "";
    public string Description { get; set; } = "";
    public decimal EstimatedImpact { get; set; }
    public string Priority { get; set; } = "";
    public List<string> RequiredActions { get; set; } = new();
    public string TimeFrame { get; set; } = "";
}

public class CohortDataDto
{
    public string CohortName { get; set; } = "";
    public DateTime StartDate { get; set; }
    public int InitialCustomers { get; set; }
    public List<double> RetentionRates { get; set; } = new();
    public decimal TotalRevenue { get; set; }
}

public class AnomalyDto
{
    public string Type { get; set; } = "";
    public string Description { get; set; } = "";
    public DateTime DetectedAt { get; set; }
    public double Severity { get; set; }
    public double Confidence { get; set; }
    public string AffectedMetric { get; set; } = "";
    public double ExpectedValue { get; set; }
    public double ActualValue { get; set; }
}

public class AnomalyStatisticsDto
{
    public int TotalAnomalies { get; set; }
    public int HighSeverityCount { get; set; }
    public int MediumSeverityCount { get; set; }
    public int LowSeverityCount { get; set; }
    public double AverageConfidence { get; set; }
}

public class AnomalyInsightDto
{
    public string InsightType { get; set; } = "";
    public string Description { get; set; } = "";
    public List<string> RecommendedActions { get; set; } = new();
}

public class ChannelAttributionDto
{
    public string ChannelName { get; set; } = "";
    public decimal AttributedRevenue { get; set; }
    public double AttributionPercentage { get; set; }
    public int ConversionsAttributed { get; set; }
    public decimal CostPerAcquisition { get; set; }
}

public class CampaignPerformanceDto
{
    public string CampaignName { get; set; } = "";
    public string Channel { get; set; } = "";
    public decimal Spend { get; set; }
    public decimal Revenue { get; set; }
    public double ROI { get; set; }
    public int Conversions { get; set; }
}

public class AttributionModelDto
{
    public string ModelType { get; set; } = "";
    public string Description { get; set; } = "";
    public Dictionary<string, double> ChannelWeights { get; set; } = new();
}

public class ROIAnalysisDto
{
    public decimal TotalSpend { get; set; }
    public decimal TotalRevenue { get; set; }
    public double OverallROI { get; set; }
    public string BestPerformingChannel { get; set; } = "";
    public string WorstPerformingChannel { get; set; } = "";
}

public class InventoryRecommendationDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = "";
    public string Action { get; set; } = "";
    public int RecommendedQuantity { get; set; }
    public string Reason { get; set; } = "";
    public decimal EstimatedImpact { get; set; }
}

public class InventoryMetricsDto
{
    public int TotalProducts { get; set; }
    public decimal TotalValue { get; set; }
    public double TurnoverRate { get; set; }
    public int OutOfStockCount { get; set; }
    public int OverstockedCount { get; set; }
}

public class StockOutRiskDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = "";
    public int CurrentStock { get; set; }
    public int ReorderLevel { get; set; }
    public double RiskScore { get; set; }
    public DateTime EstimatedStockOutDate { get; set; }
}

public class SellerMetricsDto
{
    public string SellerId { get; set; } = "";
    public string SellerName { get; set; } = "";
    public decimal Revenue { get; set; }
    public int Orders { get; set; }
    public double Rating { get; set; }
    public double ConversionRate { get; set; }
    public decimal AverageOrderValue { get; set; }
}

public class SellerBenchmarkDto
{
    public decimal AverageRevenue { get; set; }
    public double AverageRating { get; set; }
    public double AverageConversionRate { get; set; }
    public decimal AverageOrderValue { get; set; }
    public string TopPerformerCategory { get; set; } = "";
}

public class SellerInsightDto
{
    public string SellerId { get; set; } = "";
    public string InsightType { get; set; } = "";
    public string Description { get; set; } = "";
    public string Recommendation { get; set; } = "";
    public double ImpactScore { get; set; }
}

// DTOs supplémentaires manquants

public class PricingStrategyDto
{
    public string StrategyName { get; set; } = "";
    public string Description { get; set; } = "";
    public List<PriceRecommendationDto> PriceRecommendations { get; set; } = new();
    public decimal EstimatedRevenueImpact { get; set; }
    public double ConfidenceScore { get; set; }
}

public class PriceRecommendationDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = "";
    public decimal CurrentPrice { get; set; }
    public decimal RecommendedPrice { get; set; }
    public string Reasoning { get; set; } = "";
    public decimal EstimatedImpact { get; set; }
}

public class TrendingProductDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = "";
    public string Category { get; set; } = "";
    public double TrendScore { get; set; }
    public string TrendDirection { get; set; } = "";
    public int ViewsIncrease { get; set; }
    public int SalesIncrease { get; set; }
    public string TrendDuration { get; set; } = "";
}

public class SearchTrendDto
{
    public string SearchTerm { get; set; } = "";
    public int SearchCount { get; set; }
    public double TrendScore { get; set; }
    public string TrendDirection { get; set; } = "";
    public List<string> RelatedTerms { get; set; } = new();
    public int ResultsFound { get; set; }
}

public class CategoryTrendDto
{
    public string CategoryName { get; set; } = "";
    public double TrendScore { get; set; }
    public string TrendDirection { get; set; } = "";
    public decimal RevenueGrowth { get; set; }
    public int OrdersGrowth { get; set; }
    public List<string> DrivingFactors { get; set; } = new();
}

public class RealTimeMetricsDto
{
    public int ActiveUsers { get; set; }
    public int OnlineVisitors { get; set; }
    public decimal RevenueToday { get; set; }
    public int OrdersToday { get; set; }
    public double ConversionRate { get; set; }
    public decimal AverageOrderValue { get; set; }
    public int CartAbandonments { get; set; }
    public DateTime LastUpdated { get; set; }
}

public class AlertDto
{
    public string AlertId { get; set; } = "";
    public string AlertType { get; set; } = "";
    public string Title { get; set; } = "";
    public string Message { get; set; } = "";
    public string Severity { get; set; } = "";
    public DateTime CreatedAt { get; set; }
    public bool IsRead { get; set; }
    public string ActionUrl { get; set; } = "";
    public Dictionary<string, object> Metadata { get; set; } = new();
}

// Derniers DTOs manquants

public class MarketShareDto
{
    public string CompanyName { get; set; } = "";
    public double MarketSharePercentage { get; set; }
    public decimal Revenue { get; set; }
    public string Category { get; set; } = "";
    public string Trend { get; set; } = "";
}

public class CompetitorAnalysisDto
{
    public string CompetitorName { get; set; } = "";
    public double MarketShare { get; set; }
    public decimal EstimatedRevenue { get; set; }
    public List<string> Strengths { get; set; } = new();
    public List<string> Weaknesses { get; set; } = new();
    public string CompetitivePosition { get; set; } = "";
}

public class OpportunityDto
{
    public string OpportunityName { get; set; } = "";
    public string Description { get; set; } = "";
    public decimal PotentialValue { get; set; }
    public double Probability { get; set; }
    public string TimeFrame { get; set; } = "";
    public List<string> RequiredActions { get; set; } = new();
}

public class ThreatDto
{
    public string ThreatName { get; set; } = "";
    public string Description { get; set; } = "";
    public decimal PotentialImpact { get; set; }
    public double Probability { get; set; }
    public string Severity { get; set; } = "";
    public List<string> MitigationStrategies { get; set; } = new();
}

public class MarketPositionSummaryDto
{
    public string OverallPosition { get; set; } = "";
    public double CompetitiveStrength { get; set; }
    public double MarketAttractiveness { get; set; }
    public string StrategicRecommendation { get; set; } = "";
    public List<string> KeyInsights { get; set; } = new();
}

// Additional DTOs for IAdvancedAnalyticsService
public class GuineaMarketTrendsDto
{
    public List<RegionalTrendDto> RegionalTrends { get; set; } = new();
    public List<CulturalFactorDto> CulturalFactors { get; set; } = new();
    public List<SeasonalPatternDto> SeasonalPatterns { get; set; } = new();
    public EconomicEnvironmentDto EconomicEnvironment { get; set; } = new();
    public DateTime AnalysisDate { get; set; }
}

public class RegionalTrendDto
{
    public string Region { get; set; } = "";
    public decimal GrowthRate { get; set; }
    public List<string> PopularCategories { get; set; } = new();
    public string MarketCharacteristics { get; set; } = "";
}

public class CulturalFactorDto
{
    public string Factor { get; set; } = "";
    public string Impact { get; set; } = "";
    public decimal Influence { get; set; }
}

public class EconomicEnvironmentDto
{
    public decimal InflationRate { get; set; }
    public decimal GDPGrowth { get; set; }
    public decimal PurchasingPower { get; set; }
    public List<string> KeyFactors { get; set; } = new();
}

public class MarketOpportunityDto
{
    public string OpportunityType { get; set; } = "";
    public string Description { get; set; } = "";
    public decimal PotentialRevenue { get; set; }
    public string Timeline { get; set; } = "";
    public decimal SuccessProbability { get; set; }
    public List<string> RequiredActions { get; set; } = new();
}

public class CompetitiveAnalysisPredictionDto
{
    public List<CompetitorThreatDto> EmergingThreats { get; set; } = new();
    public List<CompetitiveAdvantageDto> OurAdvantages { get; set; } = new();
    public MarketPositionForecastDto PositionForecast { get; set; } = new();
    public DateTime ForecastDate { get; set; }
}

public class CompetitorThreatDto
{
    public string CompetitorName { get; set; } = "";
    public string ThreatLevel { get; set; } = "";
    public string ThreatType { get; set; } = "";
    public List<string> ImpactAreas { get; set; } = new();
}

public class CompetitiveAdvantageDto
{
    public string AdvantageType { get; set; } = "";
    public string Description { get; set; } = "";
    public decimal StrengthScore { get; set; }
    public string Sustainability { get; set; } = "";
}

public class MarketPositionForecastDto
{
    public decimal CurrentMarketShare { get; set; }
    public decimal PredictedMarketShare { get; set; }
    public string Trend { get; set; } = "";
    public List<string> KeyFactors { get; set; } = new();
}

public class CompetitorMonitoringDto
{
    public List<CompetitorActivityDto> RecentActivities { get; set; } = new();
    public List<CompetitorTrendDto> MarketTrends { get; set; } = new();
    public AlertLevelDto AlertLevel { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

public class CompetitorActivityDto
{
    public string CompetitorName { get; set; } = "";
    public string ActivityType { get; set; } = "";
    public string Description { get; set; } = "";
    public DateTime ActivityDate { get; set; }
    public string ImpactLevel { get; set; } = "";
}

public class CompetitorTrendDto
{
    public string TrendType { get; set; } = "";
    public string Description { get; set; } = "";
    public List<string> AffectedCompetitors { get; set; } = new();
    public string ImplicationForUs { get; set; } = "";
}

public class AlertLevelDto
{
    public string Level { get; set; } = "";
    public string Reason { get; set; } = "";
    public List<string> RecommendedActions { get; set; } = new();
}

public class MarketingOptimizationDto
{
    public List<ChannelOptimizationDto> ChannelOptimizations { get; set; } = new();
    public BudgetAllocationDto OptimalBudgetAllocation { get; set; } = new();
    public List<CampaignRecommendationDto> CampaignRecommendations { get; set; } = new();
    public ROIForecastDto ROIForecast { get; set; } = new();
}

public class ChannelOptimizationDto
{
    public string Channel { get; set; } = "";
    public decimal CurrentEfficiency { get; set; }
    public decimal OptimizedEfficiency { get; set; }
    public List<string> Recommendations { get; set; } = new();
    public decimal RecommendedBudget { get; set; }
}

public class BudgetAllocationDto
{
    public Dictionary<string, decimal> ChannelAllocations { get; set; } = new();
    public decimal TotalBudget { get; set; }
    public decimal ExpectedROI { get; set; }
}

public class CampaignRecommendationDto
{
    public string CampaignType { get; set; } = "";
    public string TargetAudience { get; set; } = "";
    public decimal RecommendedBudget { get; set; }
    public string Timeline { get; set; } = "";
    public decimal ExpectedROI { get; set; }
    public List<string> KeyMessages { get; set; } = new();
}

public class ROIForecastDto
{
    public decimal CurrentROI { get; set; }
    public decimal ForecastedROI { get; set; }
    public string TimeHorizon { get; set; } = "";
    public List<string> KeyAssumptions { get; set; } = new();
}

public class MarketBarriersAnalysisDto
{
    public List<BarrierDto> IdentifiedBarriers { get; set; } = new();
    public List<OpportunityDto> OvercomeStrategies { get; set; } = new();
    public MarketEntryDifficultyDto EntryDifficulty { get; set; } = new();
    public DateTime AnalysisDate { get; set; }
}

public class BarrierDto
{
    public string BarrierType { get; set; } = "";
    public string Description { get; set; } = "";
    public string Severity { get; set; } = "";
    public List<string> AffectedAreas { get; set; } = new();
}

public class MarketEntryDifficultyDto
{
    public string DifficultyLevel { get; set; } = "";
    public decimal Score { get; set; }
    public List<string> MainChallenges { get; set; } = new();
    public List<string> SuccessFactors { get; set; } = new();
}

public class SeasonalTrendDto
{
    public string Season { get; set; } = "";
    public decimal SalesMultiplier { get; set; }
    public List<string> PopularCategories { get; set; } = new();
    public string TrendDirection { get; set; } = "";
}

public class SeasonalInsightDto
{
    public string Insight { get; set; } = "";
    public string Category { get; set; } = "";
    public decimal ImpactLevel { get; set; }
    public List<string> ActionableRecommendations { get; set; } = new();
}

public class HighValueCustomerDto
{
    public string CustomerId { get; set; } = "";
    public string CustomerName { get; set; } = "";
    public decimal LifetimeValue { get; set; }
    public decimal AverageOrderValue { get; set; }
    public int OrderFrequency { get; set; }
    public string LoyaltyLevel { get; set; } = "";
    public List<string> PreferredCategories { get; set; } = new();
}

public class CompetitiveStrategyDto
{
    public List<StrategyPillarDto> StrategyPillars { get; set; } = new();
    public List<TacticalActionDto> ImmediateActions { get; set; } = new();
    public List<TacticalActionDto> LongTermActions { get; set; } = new();
    public StrategyEffectivenessDto EffectivenessProjection { get; set; } = new();
}

public class StrategyPillarDto
{
    public string PillarName { get; set; } = "";
    public string Description { get; set; } = "";
    public decimal Priority { get; set; }
    public List<string> KeyInitiatives { get; set; } = new();
}

public class TacticalActionDto
{
    public string ActionName { get; set; } = "";
    public string Description { get; set; } = "";
    public string Timeline { get; set; } = "";
    public decimal EstimatedCost { get; set; }
    public decimal ExpectedImpact { get; set; }
}

public class StrategyEffectivenessDto
{
    public decimal SuccessProbability { get; set; }
    public decimal ExpectedMarketShareGain { get; set; }
    public decimal ROIProjection { get; set; }
    public List<string> RiskFactors { get; set; } = new();
}

public class ProductAffinityAnalysisDto
{
    public List<ProductAffinityPairDto> AffinityPairs { get; set; } = new();
    public List<CrossSellOpportunityDto> CrossSellOpportunities { get; set; } = new();
    public List<BundleRecommendationDto> BundleRecommendations { get; set; } = new();
    public DateTime AnalysisDate { get; set; }
}

public class ProductAffinityPairDto
{
    public string Product1 { get; set; } = "";
    public string Product2 { get; set; } = "";
    public decimal AffinityScore { get; set; }
    public decimal Confidence { get; set; }
    public int TransactionCount { get; set; }
}


public class BundleRecommendationDto
{
    public List<string> Products { get; set; } = new();
    public decimal ExpectedSalesLift { get; set; }
    public decimal OptimalPriceDiscount { get; set; }
    public string TargetCustomerSegment { get; set; } = "";
}

public class PromotionMetricsDto
{
    public string PromotionName { get; set; } = "";
    public decimal ROI { get; set; }
    public decimal ConversionRate { get; set; }
    public decimal AverageOrderValue { get; set; }
    public int ParticipantCount { get; set; }
    public List<string> TopPerformingProducts { get; set; } = new();
    public decimal IncrementalRevenue { get; set; }
}

public class BehaviorChangeDto
{
    public string ChangeType { get; set; } = "";
    public string Description { get; set; } = "";
    public decimal Magnitude { get; set; }
    public DateTime DetectedAt { get; set; }
    public List<string> AffectedSegments { get; set; } = new();
    public List<string> PossibleCauses { get; set; } = new();
}

public class MarketPotentialDto
{
    public decimal TotalAddressableMarket { get; set; }
    public decimal ServiceableAddressableMarket { get; set; }
    public decimal ServiceableObtainableMarket { get; set; }
    public decimal CurrentMarketPenetration { get; set; }
    public decimal GrowthPotential { get; set; }
    public List<string> GrowthDrivers { get; set; } = new();
}

public class GeopoliticalImpactDto
{
    public List<GeopoliticalFactorDto> Factors { get; set; } = new();
    public decimal OverallRiskLevel { get; set; }
    public List<string> Opportunities { get; set; } = new();
    public List<string> Threats { get; set; } = new();
    public List<string> MitigationStrategies { get; set; } = new();
}

public class GeopoliticalFactorDto
{
    public string Factor { get; set; } = "";
    public string Impact { get; set; } = "";
    public decimal Probability { get; set; }
    public string TimeHorizon { get; set; } = "";
}

public class GeographicExpansionDto
{
    public List<ExpansionOpportunityDto> Opportunities { get; set; } = new();
    public List<MarketReadinessDto> MarketReadiness { get; set; } = new();
    public ExpansionStrategyDto RecommendedStrategy { get; set; } = new();
    public DateTime AnalysisDate { get; set; }
}

public class ExpansionOpportunityDto
{
    public string Region { get; set; } = "";
    public decimal MarketSize { get; set; }
    public decimal CompetitionLevel { get; set; }
    public decimal EntryDifficulty { get; set; }
    public decimal PotentialROI { get; set; }
    public string Priority { get; set; } = "";
}

public class MarketReadinessDto
{
    public string Market { get; set; } = "";
    public decimal ReadinessScore { get; set; }
    public List<string> ReadyFactors { get; set; } = new();
    public List<string> Challenges { get; set; } = new();
}

public class ExpansionStrategyDto
{
    public string Strategy { get; set; } = "";
    public List<string> Phases { get; set; } = new();
    public decimal EstimatedInvestment { get; set; }
    public string Timeline { get; set; } = "";
    public List<string> KeySuccessFactors { get; set; } = new();
}

public class HealthMetricDto
{
    public string MetricName { get; set; } = "";
    public decimal CurrentValue { get; set; }
    public decimal TargetValue { get; set; }
    public string Status { get; set; } = "";
    public string Trend { get; set; } = "";
    public List<string> ImprovementActions { get; set; } = new();
}

public class MarketingResponsivenessDto
{
    public List<SegmentResponsivenessDto> SegmentResponsiveness { get; set; } = new();
    public List<ChannelEffectivenessDto> ChannelEffectiveness { get; set; } = new();
    public OptimalTimingDto OptimalTiming { get; set; } = new();
    public DateTime AnalysisDate { get; set; }
}

public class SegmentResponsivenessDto
{
    public string Segment { get; set; } = "";
    public decimal ResponsivenessScore { get; set; }
    public List<string> PreferredChannels { get; set; } = new();
    public List<string> EffectiveMessages { get; set; } = new();
}


public class CustomerLoyaltyAnalysisDto
{
    public List<LoyaltySegmentDto> LoyaltySegments { get; set; } = new();
    public List<LoyaltyDriverDto> LoyaltyDrivers { get; set; } = new();
    public List<ChurnRiskFactorDto> ChurnRisks { get; set; } = new();
    public LoyaltyProgramEffectivenessDto ProgramEffectiveness { get; set; } = new();
}

public class LoyaltySegmentDto
{
    public string SegmentName { get; set; } = "";
    public int CustomerCount { get; set; }
    public decimal AverageLifetimeValue { get; set; }
    public decimal RetentionRate { get; set; }
    public List<string> Characteristics { get; set; } = new();
}

public class LoyaltyDriverDto
{
    public string Driver { get; set; } = "";
    public decimal Impact { get; set; }
    public string Category { get; set; } = "";
    public List<string> ImprovementOpportunities { get; set; } = new();
}

public class ChurnRiskFactorDto
{
    public string RiskFactor { get; set; } = "";
    public decimal RiskLevel { get; set; }
    public List<string> AffectedSegments { get; set; } = new();
    public List<string> PreventionStrategies { get; set; } = new();
}

public class LoyaltyProgramEffectivenessDto
{
    public decimal OverallEffectiveness { get; set; }
    public List<string> SuccessfulElements { get; set; } = new();
    public List<string> ImprovementAreas { get; set; } = new();
    public decimal ROI { get; set; }
}